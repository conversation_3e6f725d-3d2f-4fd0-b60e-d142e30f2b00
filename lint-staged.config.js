module.exports = {
  "packages/utils-amplitude/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-amplitude run lint:ts --fix",
  ],
  "packages/utils-amplitude/**/*.js": [
    "yarn workspace @unlockre/utils-amplitude run lint:js --fix",
  ],
  "packages/utils-array/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-array run lint:ts --fix",
  ],
  "packages/utils-array/**/*.js": [
    "yarn workspace @unlockre/utils-array run lint:js --fix",
  ],
  "packages/utils-auth0/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-auth0 run lint:ts --fix",
  ],
  "packages/utils-auth0/**/*.js": [
    "yarn workspace @unlockre/utils-auth0 run lint:js --fix",
  ],
  "packages/utils-dom/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-dom run lint:ts --fix",
  ],
  "packages/utils-dom/**/*.js": [
    "yarn workspace @unlockre/utils-dom run lint:js --fix",
  ],
  "packages/utils-formatting/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-formatting run lint:ts --fix",
  ],
  "packages/utils-formatting/**/*.js": [
    "yarn workspace @unlockre/utils-formatting run lint:js --fix",
  ],
  "packages/utils-http/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-http run lint:ts --fix",
  ],
  "packages/utils-http/**/*.js": [
    "yarn workspace @unlockre/utils-http run lint:js --fix",
  ],
  "packages/utils-intl/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-intl run lint:ts --fix",
  ],
  "packages/utils-intl/**/*.js": [
    "yarn workspace @unlockre/utils-intl run lint:js --fix",
  ],
  "packages/utils-next/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-next run lint:ts --fix",
  ],
  "packages/utils-next/**/*.js": [
    "yarn workspace @unlockre/utils-next run lint:js --fix",
  ],
  "packages/utils-number/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-number run lint:ts --fix",
  ],
  "packages/utils-number/**/*.js": [
    "yarn workspace @unlockre/utils-number run lint:js --fix",
  ],
  "packages/utils-object/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-object run lint:ts --fix",
  ],
  "packages/utils-object/**/*.js": [
    "yarn workspace @unlockre/utils-object run lint:js --fix",
  ],
  "packages/utils-promise/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-promise run lint:ts --fix",
  ],
  "packages/utils-promise/**/*.js": [
    "yarn workspace @unlockre/utils-promise run lint:js --fix",
  ],
  "packages/utils-react/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-react run lint:ts --fix",
  ],
  "packages/utils-react/**/*.js": [
    "yarn workspace @unlockre/utils-react run lint:js --fix",
  ],
  "packages/utils-split-io/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-split-io run lint:ts --fix",
  ],
  "packages/utils-split-io/**/*.js": [
    "yarn workspace @unlockre/utils-split-io run lint:js --fix",
  ],
  "packages/utils-string/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-string run lint:ts --fix",
  ],
  "packages/utils-string/**/*.js": [
    "yarn workspace @unlockre/utils-string run lint:js --fix",
  ],
  "packages/utils-swr/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-swr run lint:ts --fix",
  ],
  "packages/utils-swr/**/*.js": [
    "yarn workspace @unlockre/utils-swr run lint:js --fix",
  ],
  "packages/utils-userback/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-userback run lint:ts --fix",
  ],
  "packages/utils-userback/**/*.js": [
    "yarn workspace @unlockre/utils-userback run lint:js --fix",
  ],
  "packages/utils-validation/**/*.{ts,tsx}": [
    "yarn workspace @unlockre/utils-validation run lint:ts --fix",
  ],
  "packages/utils-validation/**/*.js": [
    "yarn workspace @unlockre/utils-validation run lint:js --fix",
  ]
};
