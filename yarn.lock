# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 5
  cacheKey: 8

"@aashutoshrathi/word-wrap@npm:^1.2.3":
  version: 1.2.6
  resolution: "@aashutoshrathi/word-wrap@npm:1.2.6"
  checksum: ada901b9e7c680d190f1d012c84217ce0063d8f5c5a7725bb91ec3c5ed99bb7572680eb2d2938a531ccbaec39a95422fcd8a6b4a13110c7d98dd75402f66a0cd
  languageName: node
  linkType: hard

"@amplitude/analytics-browser@npm:^2.4.1":
  version: 2.4.1
  resolution: "@amplitude/analytics-browser@npm:2.4.1"
  dependencies:
    "@amplitude/analytics-client-common": ^2.0.11
    "@amplitude/analytics-core": ^2.2.0
    "@amplitude/analytics-types": ^2.4.0
    "@amplitude/plugin-page-view-tracking-browser": ^2.1.1
    "@amplitude/plugin-web-attribution-browser": ^2.1.1
    tslib: ^2.4.1
  checksum: c8c9cf5bfa3e3acfe4bfb5e9d45c55135cfeba5ec050f3db9e73a13508bdea3c0eb8c8833a5b2f1e30b862cffdf4e2d038cb812f35e90e3e997fe2c548570243
  languageName: node
  linkType: hard

"@amplitude/analytics-client-common@npm:^2.0.11":
  version: 2.0.11
  resolution: "@amplitude/analytics-client-common@npm:2.0.11"
  dependencies:
    "@amplitude/analytics-connector": ^1.4.8
    "@amplitude/analytics-core": ^2.2.0
    "@amplitude/analytics-types": ^2.4.0
    tslib: ^2.4.1
  checksum: c4d52e1bef21e10e6b0337a179572133064f172dcbc0fee16d9c42255526e977655bd7153b987276652480a6b8999f9db645b4c46df6b7868c14953b99643a77
  languageName: node
  linkType: hard

"@amplitude/analytics-connector@npm:^1.4.8":
  version: 1.4.8
  resolution: "@amplitude/analytics-connector@npm:1.4.8"
  checksum: 72b94cc2018af4d7bec48acf2129a933bee8268fee53d737408a058eef98ac84e7239265d53885f0d8fc73774657a6620db401a44674137486ebc550b320d6e4
  languageName: node
  linkType: hard

"@amplitude/analytics-core@npm:^2.2.0":
  version: 2.2.0
  resolution: "@amplitude/analytics-core@npm:2.2.0"
  dependencies:
    "@amplitude/analytics-types": ^2.4.0
    tslib: ^2.4.1
  checksum: b2f7fe2eae314132bf1bb81004763e9354707a93d07556e1a22b5b594266f1f6b614d8713ff1852ce8b17cee56d62af923e94d17359197b01d01c5c850e01f15
  languageName: node
  linkType: hard

"@amplitude/analytics-types@npm:^2.4.0":
  version: 2.4.0
  resolution: "@amplitude/analytics-types@npm:2.4.0"
  checksum: 5b50a898005e7d8e741ab04030f3be2db7839f59ed1ed88fd7942b046b60d4084d88fc041141802d4614ce7d1f6af93712dee1d381b8ffc5680a765515274f3f
  languageName: node
  linkType: hard

"@amplitude/plugin-page-view-tracking-browser@npm:^2.1.1":
  version: 2.1.1
  resolution: "@amplitude/plugin-page-view-tracking-browser@npm:2.1.1"
  dependencies:
    "@amplitude/analytics-client-common": ^2.0.11
    "@amplitude/analytics-types": ^2.4.0
    tslib: ^2.4.1
  checksum: 4c4fa7a102ce1bf55e86a0b72b71b4bbb879c420d158481e2121afad4fc3a339f9a5284f7e7771dddd72476aa5a160740d1e64ed1b38e796117a7a08a251e523
  languageName: node
  linkType: hard

"@amplitude/plugin-web-attribution-browser@npm:^2.1.1":
  version: 2.1.1
  resolution: "@amplitude/plugin-web-attribution-browser@npm:2.1.1"
  dependencies:
    "@amplitude/analytics-client-common": ^2.0.11
    "@amplitude/analytics-core": ^2.2.0
    "@amplitude/analytics-types": ^2.4.0
    tslib: ^2.4.1
  checksum: 491a4141fec075f2b99f06097c5efcd5abf0a56ca61efefba8d13aa9da85189dd92b89deb7e90e4da2bb006cd74f608b936d1035bd343f1c13442c01c0e27b5f
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.2.1
  resolution: "@ampproject/remapping@npm:2.2.1"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.0
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 03c04fd526acc64a1f4df22651186f3e5ef0a9d6d6530ce4482ec9841269cf7a11dbb8af79237c282d721c5312024ff17529cd72cc4768c11e999b58e2302079
  languageName: node
  linkType: hard

"@auth0/auth0-react@npm:^2.1.0, @auth0/auth0-react@npm:^2.2.0, @auth0/auth0-react@npm:^2.4.0":
  version: 2.4.0
  resolution: "@auth0/auth0-react@npm:2.4.0"
  dependencies:
    "@auth0/auth0-spa-js": ^2.2.0
  peerDependencies:
    react: ^16.11.0 || ^17 || ^18 || ^19
    react-dom: ^16.11.0 || ^17 || ^18 || ^19
  checksum: 9f03d3db7f0c63304ba99499974f89d6cf243f8dddc965b84fe400ebea57bf6a70f0fbc5c21f32a464d957c965bbc80ff9c18dd86b22e812fe799a705321c821
  languageName: node
  linkType: hard

"@auth0/auth0-spa-js@npm:^2.0.7, @auth0/auth0-spa-js@npm:^2.2.0":
  version: 2.3.0
  resolution: "@auth0/auth0-spa-js@npm:2.3.0"
  checksum: 6f9c84cd300ba7215a7b894690418010cd743ada5970083f0753f189a39208b4262a9828f28c8f932ca33c1dfe6e587b057a6c956e40632aea9dbed5099220fd
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.21.4, @babel/code-frame@npm:^7.22.13, @babel/code-frame@npm:^7.22.5, @babel/code-frame@npm:^7.26.2":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": ^7.25.9
    js-tokens: ^4.0.0
    picocolors: ^1.0.0
  checksum: db13f5c42d54b76c1480916485e6900748bbcb0014a8aca87f50a091f70ff4e0d0a6db63cade75eb41fcc3d2b6ba0a7f89e343def4f96f00269b41b8ab8dd7b8
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/compat-data@npm:7.22.9"
  checksum: bed77d9044ce948b4327b30dd0de0779fa9f3a7ed1f2d31638714ed00229fa71fc4d1617ae0eb1fad419338d3658d0e9a5a083297451e09e73e078d0347ff808
  languageName: node
  linkType: hard

"@babel/core@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/core@npm:7.22.9"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.22.5
    "@babel/generator": ^7.22.9
    "@babel/helper-compilation-targets": ^7.22.9
    "@babel/helper-module-transforms": ^7.22.9
    "@babel/helpers": ^7.22.6
    "@babel/parser": ^7.22.7
    "@babel/template": ^7.22.5
    "@babel/traverse": ^7.22.8
    "@babel/types": ^7.22.5
    convert-source-map: ^1.7.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.2
    semver: ^6.3.1
  checksum: 7bf069aeceb417902c4efdaefab1f7b94adb7dea694a9aed1bda2edf4135348a080820529b1a300c6f8605740a00ca00c19b2d5e74b5dd489d99d8c11d5e56d1
  languageName: node
  linkType: hard

"@babel/eslint-parser@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/eslint-parser@npm:7.22.9"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals": 5.1.1-v1
    eslint-visitor-keys: ^2.1.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ">=7.11.0"
    eslint: ^7.5.0 || ^8.0.0
  checksum: 4f417796c803056aad2c8fa69b8a7a78a1fdacc307d95702f22894cab42b83554e47de7d0b3cfbee667f25014bca0179f859aa86ceb684b09803192e1200b48d
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.22.9, @babel/generator@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/generator@npm:7.26.5"
  dependencies:
    "@babel/parser": ^7.26.5
    "@babel/types": ^7.26.5
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
    jsesc: ^3.0.2
  checksum: baa42a98cd01efa3ae3634a6caa81d0738e5e0bdba4efbf1ac735216c8d7cf6bdffeab69c468e6ab2063b07db402346113def4962719746756518432f83c53ba
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-annotate-as-pure@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 53da330f1835c46f26b7bf4da31f7a496dee9fd8696cca12366b94ba19d97421ce519a74a837f687749318f94d1a37f8d1abcbf35e8ed22c32d16373b2f6198d
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-compilation-targets@npm:7.22.9"
  dependencies:
    "@babel/compat-data": ^7.22.9
    "@babel/helper-validator-option": ^7.22.5
    browserslist: ^4.21.9
    lru-cache: ^5.1.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: ea0006c6a93759025f4a35a25228ae260538c9f15023e8aac2a6d45ca68aef4cf86cfc429b19af9a402cbdd54d5de74ad3fbcf6baa7e48184dc079f1a791e178
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-create-class-features-plugin@npm:7.22.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-member-expression-to-functions": ^7.22.5
    "@babel/helper-optimise-call-expression": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 6c2436d1a5a3f1ff24628d78fa8c6d3120c40285aa3eda7815b1adbf8c5951e0dd73d368cf845825888fa3dc2f207dadce53309825598d7c67953e5ed9dd51d2
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-environment-visitor@npm:7.22.5"
  checksum: 248532077d732a34cd0844eb7b078ff917c3a8ec81a7f133593f71a860a582f05b60f818dc5049c2212e5baa12289c27889a4b81d56ef409b4863db49646c4b1
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-function-name@npm:7.22.5"
  dependencies:
    "@babel/template": ^7.22.5
    "@babel/types": ^7.22.5
  checksum: 6b1f6ce1b1f4e513bf2c8385a557ea0dd7fa37971b9002ad19268ca4384bbe90c09681fe4c076013f33deabc63a53b341ed91e792de741b4b35e01c00238177a
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-member-expression-to-functions@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 4bd5791529c280c00743e8bdc669ef0d4cd1620d6e3d35e0d42b862f8262bc2364973e5968007f960780344c539a4b9cf92ab41f5b4f94560a9620f536de2a39
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-module-imports@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 9ac2b0404fa38b80bdf2653fbeaf8e8a43ccb41bd505f9741d820ed95d3c4e037c62a1bcdcb6c9527d7798d2e595924c4d025daed73283badc180ada2c9c49ad
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.22.5, @babel/helper-module-transforms@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-module-transforms@npm:7.22.9"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-module-imports": ^7.22.5
    "@babel/helper-simple-access": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/helper-validator-identifier": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2751f77660518cf4ff027514d6f4794f04598c6393be7b04b8e46c6e21606e11c19f3f57ab6129a9c21bacdf8b3ffe3af87bb401d972f34af2d0ffde02ac3001
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-optimise-call-expression@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: c70ef6cc6b6ed32eeeec4482127e8be5451d0e5282d5495d5d569d39eb04d7f1d66ec99b327f45d1d5842a9ad8c22d48567e93fc502003a47de78d122e355f7c
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-plugin-utils@npm:7.22.5"
  checksum: c0fc7227076b6041acd2f0e818145d2e8c41968cc52fb5ca70eed48e21b8fe6dd88a0a91cbddf4951e33647336eb5ae184747ca706817ca3bef5e9e905151ff5
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-replace-supers@npm:7.22.9"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-member-expression-to-functions": ^7.22.5
    "@babel/helper-optimise-call-expression": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: d41471f56ff2616459d35a5df1900d5f0756ae78b1027040365325ef332d66e08e3be02a9489756d870887585ff222403a228546e93dd7019e19e59c0c0fe586
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-simple-access@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: fe9686714caf7d70aedb46c3cce090f8b915b206e09225f1e4dbc416786c2fdbbee40b38b23c268b7ccef749dd2db35f255338fb4f2444429874d900dede5ad2
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 1012ef2295eb12dc073f2b9edf3425661e9b8432a3387e62a8bc27c42963f1f216ab3124228015c748770b2257b4f1fda882ca8fa34c0bf485e929ae5bc45244
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/helper-split-export-declaration@npm:7.22.6"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: e141cace583b19d9195f9c2b8e17a3ae913b7ee9b8120246d0f9ca349ca6f03cb2c001fd5ec57488c544347c0bb584afec66c936511e447fd20a360e591ac921
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 6435ee0849e101681c1849868278b5aee82686ba2c1e27280e5e8aca6233af6810d39f8e4e693d2f2a44a3728a6ccfd66f72d71826a94105b86b731697cdfa99
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.22.5, @babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 5b85918cb1a92a7f3f508ea02699e8d2422fe17ea8e82acd445006c0ef7520fbf48e3dbcdaf7b0a1d571fc3a2715a29719e5226636cb6042e15fe6ed2a590944
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-validator-option@npm:7.22.5"
  checksum: bbeca8a85ee86990215c0424997438b388b8d642d69b9f86c375a174d3cdeb270efafd1ff128bc7a1d370923d13b6e45829ba8581c027620e83e3a80c5c414b3
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.22.6":
  version: 7.27.0
  resolution: "@babel/helpers@npm:7.27.0"
  dependencies:
    "@babel/template": ^7.27.0
    "@babel/types": ^7.27.0
  checksum: d11bb8ada0c5c298d2dbd478d69b16a79216b812010e78855143e321807df4e34f60ab65e56332e72315ccfe52a22057f0cf1dcc06e518dcfa3e3141bb8576cd
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.22.7, @babel/parser@npm:^7.26.5, @babel/parser@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/parser@npm:7.27.0"
  dependencies:
    "@babel/types": ^7.27.0
  bin:
    parser: ./bin/babel-parser.js
  checksum: 062a4e6d51553603253990c84e051ed48671a55b9d4e9caf2eff9dc888465070a0cfd288a467dbf0d99507781ea4a835b5606e32ddc0319f1b9273f913676829
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-jsx@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8829d30c2617ab31393d99cec2978e41f014f4ac6f01a1cecf4c4dd8320c3ec12fdc3ce121126b2d8d32f6887e99ca1a0bad53dedb1e6ad165640b92b24980ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-typescript@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8ab7718fbb026d64da93681a57797d60326097fd7cb930380c8bffd9eb101689e90142c760a14b51e8e69c88a73ba3da956cb4520a3b0c65743aee5c71ef360a
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.22.5"
  dependencies:
    "@babel/helper-module-transforms": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-simple-access": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2067aca8f6454d54ffcce69b02c457cfa61428e11372f6a1d99ff4fcfbb55c396ed2ca6ca886bf06c852e38c1a205b8095921b2364fd0243f3e66bc1dda61caa
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-display-name@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a12bfd1e4e93055efca3ace3c34722571bda59d9740dca364d225d9c6e3ca874f134694d21715c42cc63d79efd46db9665bd4a022998767f9245f1e29d5d204d
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.22.5"
  dependencies:
    "@babel/plugin-transform-react-jsx": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 36bc3ff0b96bb0ef4723070a50cfdf2e72cfd903a59eba448f9fe92fea47574d6f22efd99364413719e1f3fb3c51b6c9b2990b87af088f8486a84b2a5f9e4560
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-jsx@npm:7.22.5"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-module-imports": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-jsx": ^7.22.5
    "@babel/types": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c8f93f29f32cf79683ca2b8958fd62f38155674846ef27a7d4b6fbeb8713c37257418391731b58ff8024ec37b888bed5960e615a3f552e28245d2082e7f2a2df
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.22.5"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 092021c4f404e267002099ec20b3f12dd730cb90b0d83c5feed3dc00dbe43b9c42c795a18e7c6c7d7bddea20c7dd56221b146aec81b37f2e7eb5137331c61120
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.22.5":
  version: 7.22.9
  resolution: "@babel/plugin-transform-typescript@npm:7.22.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-create-class-features-plugin": ^7.22.9
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-typescript": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6d1317a54d093b302599a4bee8ba9865d0de8b7b6ac1a0746c4316231d632f75b7f086e6e78acb9ac95ba12ba3b9da462dc9ca69370abb4603c4cc987f62e67e
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/preset-react@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-option": ^7.22.5
    "@babel/plugin-transform-react-display-name": ^7.22.5
    "@babel/plugin-transform-react-jsx": ^7.22.5
    "@babel/plugin-transform-react-jsx-development": ^7.22.5
    "@babel/plugin-transform-react-pure-annotations": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b977c7ee83e93f62d77e61929ca3d97e5291e026e2f025a1b8b7ac9186486ed56c7d5bc36f0becabe0c24e8c42a4e4f2243a3cf841384cfafc3204c5d3e6c619
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/preset-typescript@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-option": ^7.22.5
    "@babel/plugin-syntax-jsx": ^7.22.5
    "@babel/plugin-transform-modules-commonjs": ^7.22.5
    "@babel/plugin-transform-typescript": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7be1670cb4404797d3a473bd72d66eb2b3e0f2f8a672a5e40bdb0812cc66085ec84bcd7b896709764cabf042fdc6b7f2d4755ac7cce10515eb596ff61dab5154
  languageName: node
  linkType: hard

"@babel/template@npm:^7.22.5, @babel/template@npm:^7.25.9, @babel/template@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/template@npm:7.27.0"
  dependencies:
    "@babel/code-frame": ^7.26.2
    "@babel/parser": ^7.27.0
    "@babel/types": ^7.27.0
  checksum: 46d6db4c204a092f11ad6c3bfb6ec3dc1422e32121186d68ab1b3e633313aa5b7e21f26ca801dbd7da21f256225305a76454429fc500e52dabadb30af35df961
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.22.8":
  version: 7.26.5
  resolution: "@babel/traverse@npm:7.26.5"
  dependencies:
    "@babel/code-frame": ^7.26.2
    "@babel/generator": ^7.26.5
    "@babel/parser": ^7.26.5
    "@babel/template": ^7.25.9
    "@babel/types": ^7.26.5
    debug: ^4.3.1
    globals: ^11.1.0
  checksum: 28f28037ec6bb72ded695b2bd79c373f13dc993a408c6037c3d46a1234360342a688c031f9ed4fc8528183892a63b54edce0b516e723fb3dffd606da75496cdc
  languageName: node
  linkType: hard

"@babel/types@npm:^7.22.5, @babel/types@npm:^7.26.5, @babel/types@npm:^7.27.0":
  version: 7.27.0
  resolution: "@babel/types@npm:7.27.0"
  dependencies:
    "@babel/helper-string-parser": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
  checksum: 59582019eb8a693d4277015d4dec0233874d884b9019dcd09550332db7f0f2ac9e30eca685bb0ada4bab5a4dc8bbc2a6bcaadb151c69b7e6aa94b5eaf8fc8c51
  languageName: node
  linkType: hard

"@colors/colors@npm:1.5.0":
  version: 1.5.0
  resolution: "@colors/colors@npm:1.5.0"
  checksum: d64d5260bed1d5012ae3fc617d38d1afc0329fec05342f4e6b838f46998855ba56e0a73833f4a80fa8378c84810da254f76a8a19c39d038260dc06dc4e007425
  languageName: node
  linkType: hard

"@commitlint/cli@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/cli@npm:16.2.1"
  dependencies:
    "@commitlint/format": ^16.2.1
    "@commitlint/lint": ^16.2.1
    "@commitlint/load": ^16.2.1
    "@commitlint/read": ^16.2.1
    "@commitlint/types": ^16.2.1
    lodash: ^4.17.19
    resolve-from: 5.0.0
    resolve-global: 1.0.0
    yargs: ^17.0.0
  bin:
    commitlint: cli.js
  checksum: da673e8e037f74b1a4257884d8f55fd6bfaf575283aa15b48a787090d3b81bdb49c8daa92aaa93208e1c026296c451e9d697d53380ea93a1a5c110a10e5794d0
  languageName: node
  linkType: hard

"@commitlint/config-conventional@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/config-conventional@npm:16.2.1"
  dependencies:
    conventional-changelog-conventionalcommits: ^4.3.1
  checksum: 23dac76a8fbe5624433e1c948bc53eaf0a60b77f418fe7f94eea8e17abf9605d7ae9418188d8c1a38c2e6d5cb865c8e2d0894826c3eb41fb052af95e79bc02d3
  languageName: node
  linkType: hard

"@commitlint/config-validator@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/config-validator@npm:16.2.1"
  dependencies:
    "@commitlint/types": ^16.2.1
    ajv: ^6.12.6
  checksum: 1b86832dc03fc7f9442f9358c6c73d42974e9006944b8524bc4b4cd2ce946e50f3eca972737844dc7765a874c465ff5f18dad210f979491f9ee07c831b0eb8d3
  languageName: node
  linkType: hard

"@commitlint/ensure@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/ensure@npm:16.2.1"
  dependencies:
    "@commitlint/types": ^16.2.1
    lodash: ^4.17.19
  checksum: 388a124e515c02f14d026973821a6ce1d586ac966da8a51e69fabb925ee858e864696cd2b398bb5bec8d7ceee97f9f04c77630061b7784a10b06e0a436447d44
  languageName: node
  linkType: hard

"@commitlint/execute-rule@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/execute-rule@npm:16.2.1"
  checksum: 83be0e858fa415ba7d844fc68c7c8bcc3b14074fe862f2129e03ce5fd07a58876d88d080e0d2fbf25e10f6d3189a04bca024def48206fa0f0f1c5890d689539c
  languageName: node
  linkType: hard

"@commitlint/format@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/format@npm:16.2.1"
  dependencies:
    "@commitlint/types": ^16.2.1
    chalk: ^4.0.0
  checksum: d8f26a789f0ffc2dd763ed6467262e2cfa94900d7f517f39d32b0f0e9e5222767da12b5302bdccfb1e8a4805c667e5dc36ef98d41754c3ed0e339c35664c0ba6
  languageName: node
  linkType: hard

"@commitlint/is-ignored@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/is-ignored@npm:16.2.1"
  dependencies:
    "@commitlint/types": ^16.2.1
    semver: 7.3.5
  checksum: 130011f5723a58e2f1e4a7aa5720766fb709ad27203efc1f468efac841d99f672c4273daa247a2ba03a7779afd83f5a8f5576e3632fb3abdc01cf19c101ce4db
  languageName: node
  linkType: hard

"@commitlint/lint@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/lint@npm:16.2.1"
  dependencies:
    "@commitlint/is-ignored": ^16.2.1
    "@commitlint/parse": ^16.2.1
    "@commitlint/rules": ^16.2.1
    "@commitlint/types": ^16.2.1
  checksum: f93b1402e9e34aa91d2eccf0049c3c77fc224dc887c1f82ee9fe4b5d4fd96632cf0b9481d631462c741baafbc822827af52e6de1e8bfeb41a414b6d71422680b
  languageName: node
  linkType: hard

"@commitlint/load@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/load@npm:16.2.1"
  dependencies:
    "@commitlint/config-validator": ^16.2.1
    "@commitlint/execute-rule": ^16.2.1
    "@commitlint/resolve-extends": ^16.2.1
    "@commitlint/types": ^16.2.1
    "@types/node": ">=12"
    chalk: ^4.0.0
    cosmiconfig: ^7.0.0
    cosmiconfig-typescript-loader: ^1.0.0
    lodash: ^4.17.19
    resolve-from: ^5.0.0
    typescript: ^4.4.3
  checksum: 2b2fceff10c02ba61a5fa9c24a8f22156af0be6865a6b3b1543d93861d75b1552d7c86ce9cadd8598248387829feee26d0e0e646f3f0822e7a9bc74023e18fde
  languageName: node
  linkType: hard

"@commitlint/message@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/message@npm:16.2.1"
  checksum: 172e18bd5bd47bf7d61356ba1da4a552a5f96860fadb277b9431e1ecfe6b49dd8f303e6d7ad120961325093346ec6764231975f8c73434f5487b05493406d551
  languageName: node
  linkType: hard

"@commitlint/parse@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/parse@npm:16.2.1"
  dependencies:
    "@commitlint/types": ^16.2.1
    conventional-changelog-angular: ^5.0.11
    conventional-commits-parser: ^3.2.2
  checksum: 8f966c45b2838900dfe8af14fa5085707a2c2ece7d6f00d8e61dad1fdd617b202177cfcc428ef6f7a41b7e6872560c9a040cf92eb122ad31a8f7777e3f9bab7b
  languageName: node
  linkType: hard

"@commitlint/read@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/read@npm:16.2.1"
  dependencies:
    "@commitlint/top-level": ^16.2.1
    "@commitlint/types": ^16.2.1
    fs-extra: ^10.0.0
    git-raw-commits: ^2.0.0
  checksum: c2eb6c299a6af0ffda8ba27a5534210638b227855dd5d01d757fbf7a26a05a5c3d4d1f30e91bdd5ce12de023e482a329fad049df1f5b0f232049e7212e3cf947
  languageName: node
  linkType: hard

"@commitlint/resolve-extends@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/resolve-extends@npm:16.2.1"
  dependencies:
    "@commitlint/config-validator": ^16.2.1
    "@commitlint/types": ^16.2.1
    import-fresh: ^3.0.0
    lodash: ^4.17.19
    resolve-from: ^5.0.0
    resolve-global: ^1.0.0
  checksum: e710fcb24573e1027bf0b7336983cd0539c32734b01831eb0da8a7f500d0734669d38ea75ff93e90c162417fd4db5cc460c2f122d772dfa0f4577f49caaee687
  languageName: node
  linkType: hard

"@commitlint/rules@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/rules@npm:16.2.1"
  dependencies:
    "@commitlint/ensure": ^16.2.1
    "@commitlint/message": ^16.2.1
    "@commitlint/to-lines": ^16.2.1
    "@commitlint/types": ^16.2.1
    execa: ^5.0.0
  checksum: 86d2c7d6564d231705e215135275e2f8c3aacd047402edfc78021398e83743d3b4f759ccc93294c94cb5773fed22eeca2036ea2a07e177c2e82c581430b89e2b
  languageName: node
  linkType: hard

"@commitlint/to-lines@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/to-lines@npm:16.2.1"
  checksum: 94b1523298f335583307cff4f634137788bdce67f572dcdd6f08ca09cbe1176193ba2e308158696951ce3dd93cb2c6d1d8946e8ee376f506ac5212a65d87ed58
  languageName: node
  linkType: hard

"@commitlint/top-level@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/top-level@npm:16.2.1"
  dependencies:
    find-up: ^5.0.0
  checksum: db6ae0483a4b7fbe3e2ca02541049180f87d88417039ea58e7539f22fb042fe50e465f5654394555bf9759b1c1e6130b435e4e80fbcec1d0e58cf24f9ccaf728
  languageName: node
  linkType: hard

"@commitlint/types@npm:^16.2.1":
  version: 16.2.1
  resolution: "@commitlint/types@npm:16.2.1"
  dependencies:
    chalk: ^4.0.0
  checksum: 93af3c26c36f3b11d99f0cbbb09c8952581eed2a6b7763eb728c0e7e7ecff5072de064a208b80225fb51533823af84ee3117d9c2efbcb63d1f5cfbf6fbfb8ed8
  languageName: node
  linkType: hard

"@cspotcode/source-map-consumer@npm:0.8.0":
  version: 0.8.0
  resolution: "@cspotcode/source-map-consumer@npm:0.8.0"
  checksum: c0c16ca3d2f58898f1bd74c4f41a189dbcc202e642e60e489cbcc2e52419c4e89bdead02c886a12fb13ea37798ede9e562b2321df997ebc210ae9bd881561b4e
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:0.7.0":
  version: 0.7.0
  resolution: "@cspotcode/source-map-support@npm:0.7.0"
  dependencies:
    "@cspotcode/source-map-consumer": 0.8.0
  checksum: 9faddda7757cd778b5fd6812137b2cc265810043680d6399acc20441668fafcdc874053be9dccd0d9110087287bfad27eb3bf342f72bceca9aa9059f5d0c4be8
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.2.0":
  version: 1.4.0
  resolution: "@emnapi/runtime@npm:1.4.0"
  dependencies:
    tslib: ^2.4.0
  checksum: 90cca909ceb842e95cb5e16ab64e4c4d8925c6f4ed5527848e53faad8c4e23885e4a001a6099e26a76d099a67758032a9f208b13252176e2b68c860d19f5c1f9
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.0
  resolution: "@eslint-community/eslint-utils@npm:4.4.0"
  dependencies:
    eslint-visitor-keys: ^3.3.0
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: cdfe3ae42b4f572cbfb46d20edafe6f36fc5fb52bf2d90875c58aefe226892b9677fef60820e2832caf864a326fe4fc225714c46e8389ccca04d5f9288aabd22
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.5.1, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.6.2
  resolution: "@eslint-community/regexpp@npm:4.6.2"
  checksum: a3c341377b46b54fa228f455771b901d1a2717f95d47dcdf40199df30abc000ba020f747f114f08560d119e979d882a94cf46cfc51744544d54b00319c0f2724
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.1":
  version: 2.1.1
  resolution: "@eslint/eslintrc@npm:2.1.1"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^9.6.0
    globals: ^13.19.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: bf909ea183d27238c257a82d4ffdec38ca94b906b4b8dfae02ecbe7ecc9e5a8182ef5e469c808bb8cb4fea4750f43ac4ca7c4b4a167b6cd7e3aaacd386b2bd25
  languageName: node
  linkType: hard

"@eslint/js@npm:^8.46.0":
  version: 8.46.0
  resolution: "@eslint/js@npm:8.46.0"
  checksum: 7aed479832302882faf5bec37e9d068f270f84c19b3fb529646a7c1b031e73a312f730569c78806492bc09cfce3d7651dfab4ce09a56cbb06bc6469449e56377
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.11.10":
  version: 0.11.10
  resolution: "@humanwhocodes/config-array@npm:0.11.10"
  dependencies:
    "@humanwhocodes/object-schema": ^1.2.1
    debug: ^4.1.1
    minimatch: ^3.0.5
  checksum: 1b1302e2403d0e35bc43e66d67a2b36b0ad1119efc704b5faff68c41f791a052355b010fb2d27ef022670f550de24cd6d08d5ecf0821c16326b7dcd0ee5d5d8a
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^1.2.1":
  version: 1.2.1
  resolution: "@humanwhocodes/object-schema@npm:1.2.1"
  checksum: a824a1ec31591231e4bad5787641f59e9633827d0a2eaae131a288d33c9ef0290bd16fda8da6f7c0fcb014147865d12118df10db57f27f41e20da92369fcb3f1
  languageName: node
  linkType: hard

"@img/sharp-darwin-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-darwin-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-darwin-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-darwin-arm64":
      optional: true
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-darwin-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-darwin-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-darwin-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-darwin-x64":
      optional: true
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-darwin-arm64@npm:1.0.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-darwin-x64@npm:1.0.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-arm64@npm:1.0.4"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm@npm:1.0.5":
  version: 1.0.5
  resolution: "@img/sharp-libvips-linux-arm@npm:1.0.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-s390x@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-s390x@npm:1.0.4"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-x64@npm:1.0.4"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linuxmusl-arm64@npm:1.0.4"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linuxmusl-x64@npm:1.0.4"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-linux-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm64":
      optional: true
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-linux-arm@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-arm@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-arm": 1.0.5
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm":
      optional: true
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@img/sharp-linux-s390x@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-s390x@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-s390x": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-s390x":
      optional: true
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@img/sharp-linux-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-x64":
      optional: true
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linuxmusl-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linuxmusl-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linuxmusl-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linuxmusl-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-wasm32@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-wasm32@npm:0.33.5"
  dependencies:
    "@emnapi/runtime": ^1.2.0
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@img/sharp-win32-ia32@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-win32-ia32@npm:0.33.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@img/sharp-win32-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-win32-x64@npm:0.33.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@isaacs/string-locale-compare@npm:^1.1.0":
  version: 1.1.0
  resolution: "@isaacs/string-locale-compare@npm:1.1.0"
  checksum: 7287da5d11497b82c542d3c2abe534808015be4f4883e71c26853277b5456f6bbe4108535db847a29f385ad6dc9318ffb0f55ee79bb5f39993233d7dccf8751d
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.0, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: c0687b5227461717aa537fe71a42e356bcd1c43293b3353796a148bf3b0d6f59109def46c22f05b60e29a46f19b2e4676d027959a7c53a6c92b9d5b0d87d0420
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 05df4f2538b3b0f998ea4c1cd34574d0feba216fa5d4ccaef0187d12abf82eafe6021cec8b49f9bb4d90f2ba4582ccc581e72986a5fcf4176ae0cfeb04cf52ec
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25, @jridgewell/trace-mapping@npm:^0.3.9":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 9d3c40d225e139987b50c48988f8717a54a8c994d8a948ee42e1412e08988761d0754d7d10b803061cc3aebf35f92a5dbbab493bd0e1a9ef9e89a2130e83ba34
  languageName: node
  linkType: hard

"@mgtitimoli/utils-error@npm:^1.0.2":
  version: 1.0.2
  resolution: "@mgtitimoli/utils-error@npm:1.0.2"
  dependencies:
    tslib: ^2.0.0
  checksum: 821eea2c46df8dce20de94709978ee4981f4e9151f68413f51a277163954597df7af8b3e5e4eee942896f9de979510d5dfe35d6b9043ceef4cce1ec5fc19b61d
  languageName: node
  linkType: hard

"@next/env@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/env@npm:15.2.4"
  checksum: 8c532d963408766406baeb3b7f018fcdfac6b953d829afbd9d7d649668c20546dd32400259d3b0d894a7a1947116e364cb4a62e9a642cf0cca7c0f1fcf9e7920
  languageName: node
  linkType: hard

"@next/eslint-plugin-next@npm:^12.3.4":
  version: 12.3.4
  resolution: "@next/eslint-plugin-next@npm:12.3.4"
  dependencies:
    glob: 7.1.7
  checksum: e4ae97062f3efe8f70904cf0da296ab501a2924423273352d01b18d8ffff1eb2e9a65c47dd6f9cfa0d696eada272486a3f519b2786918d0a9ab735b93f5ce4b3
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-darwin-arm64@npm:15.2.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-darwin-x64@npm:15.2.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-linux-arm64-gnu@npm:15.2.4"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-linux-arm64-musl@npm:15.2.4"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-linux-x64-gnu@npm:15.2.4"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-linux-x64-musl@npm:15.2.4"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-win32-arm64-msvc@npm:15.2.4"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-win32-x64-msvc@npm:15.2.4"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1":
  version: 5.1.1-v1
  resolution: "@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1"
  dependencies:
    eslint-scope: 5.1.1
  checksum: f2e3b2d6a6e2d9f163ca22105910c9f850dc4897af0aea3ef0a5886b63d8e1ba6505b71c99cb78a3bba24a09557d601eb21c8dede3f3213753fcfef364eb0e57
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/arborist@npm:^8.0.0":
  version: 8.0.0
  resolution: "@npmcli/arborist@npm:8.0.0"
  dependencies:
    "@isaacs/string-locale-compare": ^1.1.0
    "@npmcli/fs": ^4.0.0
    "@npmcli/installed-package-contents": ^3.0.0
    "@npmcli/map-workspaces": ^4.0.1
    "@npmcli/metavuln-calculator": ^8.0.0
    "@npmcli/name-from-folder": ^3.0.0
    "@npmcli/node-gyp": ^4.0.0
    "@npmcli/package-json": ^6.0.1
    "@npmcli/query": ^4.0.0
    "@npmcli/redact": ^3.0.0
    "@npmcli/run-script": ^9.0.1
    bin-links: ^5.0.0
    cacache: ^19.0.1
    common-ancestor-path: ^1.0.1
    hosted-git-info: ^8.0.0
    json-parse-even-better-errors: ^4.0.0
    json-stringify-nice: ^1.1.4
    lru-cache: ^10.2.2
    minimatch: ^9.0.4
    nopt: ^8.0.0
    npm-install-checks: ^7.1.0
    npm-package-arg: ^12.0.0
    npm-pick-manifest: ^10.0.0
    npm-registry-fetch: ^18.0.1
    pacote: ^19.0.0
    parse-conflict-json: ^4.0.0
    proc-log: ^5.0.0
    proggy: ^3.0.0
    promise-all-reject-late: ^1.0.0
    promise-call-limit: ^3.0.1
    read-package-json-fast: ^4.0.0
    semver: ^7.3.7
    ssri: ^12.0.0
    treeverse: ^3.0.0
    walk-up-path: ^3.0.1
  bin:
    arborist: bin/index.js
  checksum: 42b521b674eb1cb9095d1651887f84e148cd9ba70c747f515c96ec83e45e6549447f38337624295d4ef83bf9c7ea6bbfdefef43135da765b0434f044d2c82137
  languageName: node
  linkType: hard

"@npmcli/config@npm:^9.0.0":
  version: 9.0.0
  resolution: "@npmcli/config@npm:9.0.0"
  dependencies:
    "@npmcli/map-workspaces": ^4.0.1
    "@npmcli/package-json": ^6.0.1
    ci-info: ^4.0.0
    ini: ^5.0.0
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    walk-up-path: ^3.0.1
  checksum: 391d9f66bada5bb952e8a5eaae30d4541381903b3457241d4d24c3f4278dcf2c20992eb383ff67eedfbc8a8cfb0fbe9db9b6d8d7f7628b5cfb92ba34d732e7a9
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@npmcli/git@npm:^6.0.0, @npmcli/git@npm:^6.0.1":
  version: 6.0.1
  resolution: "@npmcli/git@npm:6.0.1"
  dependencies:
    "@npmcli/promise-spawn": ^8.0.0
    ini: ^5.0.0
    lru-cache: ^10.0.1
    npm-pick-manifest: ^10.0.0
    proc-log: ^5.0.0
    promise-inflight: ^1.0.1
    promise-retry: ^2.0.1
    semver: ^7.3.5
    which: ^5.0.0
  checksum: 2cdefa8c714861a73ff5afb262259ff968fa5c45224f7748e55ab1952eee78183094331570fa5f4cc92071f274dfdc0e072cf4902d2638c43b832a364363f33d
  languageName: node
  linkType: hard

"@npmcli/installed-package-contents@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/installed-package-contents@npm:3.0.0"
  dependencies:
    npm-bundled: ^4.0.0
    npm-normalize-package-bin: ^4.0.0
  bin:
    installed-package-contents: bin/index.js
  checksum: b259157c682512b1eb8a3df58d0cdb73189befda1e5eca8a2c8e4128698a098aa93038931d45f819463fa0f9a5873f782936cf5ab0941f1d125387144361f577
  languageName: node
  linkType: hard

"@npmcli/map-workspaces@npm:^4.0.1, @npmcli/map-workspaces@npm:^4.0.2":
  version: 4.0.2
  resolution: "@npmcli/map-workspaces@npm:4.0.2"
  dependencies:
    "@npmcli/name-from-folder": ^3.0.0
    "@npmcli/package-json": ^6.0.0
    glob: ^10.2.2
    minimatch: ^9.0.0
  checksum: 1dba46e94b1e53b59e9b735f89b93ba39c5925120d3f27acb122033833c0e36e5017ab5bdbb0b5dc190300a4359eb4ef9c3539e36e7e2484875b9ac0c75fcfd6
  languageName: node
  linkType: hard

"@npmcli/metavuln-calculator@npm:^8.0.0":
  version: 8.0.1
  resolution: "@npmcli/metavuln-calculator@npm:8.0.1"
  dependencies:
    cacache: ^19.0.0
    json-parse-even-better-errors: ^4.0.0
    pacote: ^20.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
  checksum: f836ba5a5bd9eb7c7ec470f89208498c047e5ed727c6239d9c62664dc779783fa5985ec99c965d113057a5e20fb1be0c5e052de29e8997fcb5d9a6e63278ca99
  languageName: node
  linkType: hard

"@npmcli/name-from-folder@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/name-from-folder@npm:3.0.0"
  checksum: 1b56429f56c8228bf0eaea8298627b36e383930800a49c9445ae4500b905c98eae1d5f506042a36f49d863d5b79f2aadd154a03d9862dc381ce3fabadcb46e70
  languageName: node
  linkType: hard

"@npmcli/node-gyp@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/node-gyp@npm:4.0.0"
  checksum: ea4ac6aa273d762a540841315c59c61f3e4ef182c29b1295c30f287cd9d0e33650cd60d626cdce38caf5cff43a5848ea6c213bad5f884110fc90beb167ccbc46
  languageName: node
  linkType: hard

"@npmcli/package-json@npm:^6.0.0, @npmcli/package-json@npm:^6.0.1, @npmcli/package-json@npm:^6.1.0":
  version: 6.1.0
  resolution: "@npmcli/package-json@npm:6.1.0"
  dependencies:
    "@npmcli/git": ^6.0.0
    glob: ^10.2.2
    hosted-git-info: ^8.0.0
    json-parse-even-better-errors: ^4.0.0
    normalize-package-data: ^7.0.0
    proc-log: ^5.0.0
    semver: ^7.5.3
  checksum: 645ce40b159de40f292f0a12a6957eed5127ad1d8aada3f519279cf8700b629e1f51f8215532f33a0094337be571044f8e1a9066b45196447f54440c1326baaf
  languageName: node
  linkType: hard

"@npmcli/promise-spawn@npm:^8.0.0, @npmcli/promise-spawn@npm:^8.0.2":
  version: 8.0.2
  resolution: "@npmcli/promise-spawn@npm:8.0.2"
  dependencies:
    which: ^5.0.0
  checksum: d8963742d01c5d0a40d68d1f1919f0e0cddaead05359abb4a2913362641855bc869dc30982f701dbdf6f079c68b41b2b4515d78d9f068d23aa8f7bf521916567
  languageName: node
  linkType: hard

"@npmcli/query@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/query@npm:4.0.0"
  dependencies:
    postcss-selector-parser: ^6.1.2
  checksum: 5aafa9d09c7f1fbbf35b8ec9c0fc27e8869183aca8da6a957154d630ad82a08dcf21743f158d04c31b2cd950301db7efbfcb48a59ba98a584763212bc9dcc946
  languageName: node
  linkType: hard

"@npmcli/redact@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/redact@npm:3.0.0"
  checksum: 449b73da396b93e42603342207aeada3ca1eed0b0da7ec143f8d10feb5697efd27c2d670aef69b1ef73653cf6a9d8b0ee84e1ab960a0f11cbc8e8b1901ec825f
  languageName: node
  linkType: hard

"@npmcli/run-script@npm:^9.0.0, @npmcli/run-script@npm:^9.0.1":
  version: 9.0.2
  resolution: "@npmcli/run-script@npm:9.0.2"
  dependencies:
    "@npmcli/node-gyp": ^4.0.0
    "@npmcli/package-json": ^6.0.0
    "@npmcli/promise-spawn": ^8.0.0
    node-gyp: ^11.0.0
    proc-log: ^5.0.0
    which: ^5.0.0
  checksum: 85d83c093bfce589632e82b6c26e50a365a61a88e4ad27ba287563cbc6dc5ec05652de4ac60871b42b2c454209fd996d47889217d5358fdcfd0b0fbc109f46a4
  languageName: node
  linkType: hard

"@octokit/auth-token@npm:^5.0.0":
  version: 5.1.1
  resolution: "@octokit/auth-token@npm:5.1.1"
  checksum: b39516dda44aeced0326227c53aade621effe1d59c4b0f48ebe2b9fd32b5156e02705bcb2fb1bf48b11f26cc6aff1a0683c32c3d5424e0118dae6596e431d489
  languageName: node
  linkType: hard

"@octokit/core@npm:^6.0.0":
  version: 6.1.3
  resolution: "@octokit/core@npm:6.1.3"
  dependencies:
    "@octokit/auth-token": ^5.0.0
    "@octokit/graphql": ^8.1.2
    "@octokit/request": ^9.1.4
    "@octokit/request-error": ^6.1.6
    "@octokit/types": ^13.6.2
    before-after-hook: ^3.0.2
    universal-user-agent: ^7.0.0
  checksum: 9174c8658f362a34a42dba77681b9ee8724b13c2231690dccbc2664a4fe64da8339b0875f73917e09f67ef370d59d9aee499fe0855f1eba55535383af1018e8f
  languageName: node
  linkType: hard

"@octokit/endpoint@npm:^10.1.3":
  version: 10.1.3
  resolution: "@octokit/endpoint@npm:10.1.3"
  dependencies:
    "@octokit/types": ^13.6.2
    universal-user-agent: ^7.0.2
  checksum: 47253e341ea1ef2d22fd33566753574f97fa28ebc8a9869821dd4f3f0eca2541562f31317118d203b91194804f1bac5f3373a261abb0b7b7a47ef84d6a88c124
  languageName: node
  linkType: hard

"@octokit/graphql@npm:^8.1.2":
  version: 8.1.2
  resolution: "@octokit/graphql@npm:8.1.2"
  dependencies:
    "@octokit/request": ^9.1.4
    "@octokit/types": ^13.6.2
    universal-user-agent: ^7.0.0
  checksum: 8a4a16ae53883a7d881a04d814a9e73f48e6a83cd43e73e6f0dbebdd5307b391763c101fdeb293525f9ecdca4294e598a1aaba39b43fcc5368a98e73d41a6a97
  languageName: node
  linkType: hard

"@octokit/openapi-types@npm:^24.2.0":
  version: 24.2.0
  resolution: "@octokit/openapi-types@npm:24.2.0"
  checksum: 3c2d2f4cafd21c8a1e6a6fe6b56df6a3c09bc52ab6f829c151f9397694d028aa183ae856f08e006ee7ecaa7bd7eb413a903fbc0ffa6403e7b284ddcda20b1294
  languageName: node
  linkType: hard

"@octokit/plugin-paginate-rest@npm:^11.0.0":
  version: 11.6.0
  resolution: "@octokit/plugin-paginate-rest@npm:11.6.0"
  dependencies:
    "@octokit/types": ^13.10.0
  peerDependencies:
    "@octokit/core": ">=6"
  checksum: b7b1fb9b5d02688d89f936a4ec1a46142d942fb72fe889bd6317488ca9fbdc3c34bd06c46ec10b91715d35250efb67977207408626734ad4702ee58522090455
  languageName: node
  linkType: hard

"@octokit/plugin-retry@npm:^7.0.0":
  version: 7.1.3
  resolution: "@octokit/plugin-retry@npm:7.1.3"
  dependencies:
    "@octokit/request-error": ^6.1.6
    "@octokit/types": ^13.6.2
    bottleneck: ^2.15.3
  peerDependencies:
    "@octokit/core": ">=6"
  checksum: d8ee8cf72d348cd52ef86906264528c047aaf49517f8e3d61bf74629d8852f1ec0a1ce535f07dcc0437422bad8a0c9b47af712457b9347419a81f8caa2a99033
  languageName: node
  linkType: hard

"@octokit/plugin-throttling@npm:^9.0.0":
  version: 9.4.0
  resolution: "@octokit/plugin-throttling@npm:9.4.0"
  dependencies:
    "@octokit/types": ^13.7.0
    bottleneck: ^2.15.3
  peerDependencies:
    "@octokit/core": ^6.1.3
  checksum: 84f774a40b9a5e4b0c1a405b28368490bd104ef2bf27006da2e319cd62b096b70c00a00d89d6705b8ac31bc2633a95c0cd3c7ca4e66c85c10d9470113aa6d3c3
  languageName: node
  linkType: hard

"@octokit/request-error@npm:^6.1.6, @octokit/request-error@npm:^6.1.7":
  version: 6.1.7
  resolution: "@octokit/request-error@npm:6.1.7"
  dependencies:
    "@octokit/types": ^13.6.2
  checksum: 02273f6388f1fa8e9962f5eeddffac784454200fa291d9e2333eeaa53f70fbf3fb8d9bca191f38457c455dda758b95c8db50167085cfd6f97dd7a67a5aff452d
  languageName: node
  linkType: hard

"@octokit/request@npm:^9.1.4":
  version: 9.2.2
  resolution: "@octokit/request@npm:9.2.2"
  dependencies:
    "@octokit/endpoint": ^10.1.3
    "@octokit/request-error": ^6.1.7
    "@octokit/types": ^13.6.2
    fast-content-type-parse: ^2.0.0
    universal-user-agent: ^7.0.2
  checksum: b5600cca1823bd1a16a9d85298351d9879c399b5acbaf4c99c77c62c2eaf18fb66debc29ab82e8b0bc3e6b098ab01c0075a843dc57f0647ad9a6dc7cb8e18aa9
  languageName: node
  linkType: hard

"@octokit/types@npm:^13.10.0, @octokit/types@npm:^13.6.2, @octokit/types@npm:^13.7.0":
  version: 13.10.0
  resolution: "@octokit/types@npm:13.10.0"
  dependencies:
    "@octokit/openapi-types": ^24.2.0
  checksum: fca3764548d5872535b9025c3b5fe6373fe588b287cb5b5259364796c1931bbe5e9ab8a86a5274ce43bb2b3e43b730067c3b86b6b1ade12a98cd59b2e8b3610d
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@pkgr/utils@npm:^2.3.1":
  version: 2.4.2
  resolution: "@pkgr/utils@npm:2.4.2"
  dependencies:
    cross-spawn: ^7.0.3
    fast-glob: ^3.3.0
    is-glob: ^4.0.3
    open: ^9.1.0
    picocolors: ^1.0.0
    tslib: ^2.6.0
  checksum: 24e04c121269317d259614cd32beea3af38277151c4002df5883c4be920b8e3490bb897748e844f9d46bf68230f86dabd4e8f093773130e7e60529a769a132fc
  languageName: node
  linkType: hard

"@pnpm/config.env-replace@npm:^1.1.0":
  version: 1.1.0
  resolution: "@pnpm/config.env-replace@npm:1.1.0"
  checksum: a3d2b57e35eec9543d9eb085854f6e33e8102dac99fdef2fad2eebdbbfc345e93299f0c20e8eb61c1b4c7aa123bfd47c175678626f161cda65dd147c2b6e1fa0
  languageName: node
  linkType: hard

"@pnpm/network.ca-file@npm:^1.0.1":
  version: 1.0.2
  resolution: "@pnpm/network.ca-file@npm:1.0.2"
  dependencies:
    graceful-fs: 4.2.10
  checksum: d8d0884646500576bd5390464d13db1bb9a62e32a1069293e5bddb2ad8354b354b7e2d2a35e12850025651e795e6a80ce9e601c66312504667b7e3ee7b52becc
  languageName: node
  linkType: hard

"@pnpm/npm-conf@npm:^2.1.0":
  version: 2.3.1
  resolution: "@pnpm/npm-conf@npm:2.3.1"
  dependencies:
    "@pnpm/config.env-replace": ^1.1.0
    "@pnpm/network.ca-file": ^1.0.1
    config-chain: ^1.1.11
  checksum: 9e1e1ce5faa64719e866b02d10e28d727d809365eb3692ccfdc420ab6d2073b93abe403994691868f265e34a5601a8eee18ffff6562b27124d971418ba6bb815
  languageName: node
  linkType: hard

"@rushstack/eslint-patch@npm:^1.3.2":
  version: 1.3.2
  resolution: "@rushstack/eslint-patch@npm:1.3.2"
  checksum: 010c87ef2d901faaaf70ea1bf86fd3e7b74f24e23205f836e9a32790bca2076afe5de58ded03c35cb482f83691c8d22b1a0c34291b075bfe81afd26cfa5d14cc
  languageName: node
  linkType: hard

"@sec-ant/readable-stream@npm:^0.4.1":
  version: 0.4.1
  resolution: "@sec-ant/readable-stream@npm:0.4.1"
  checksum: eb56f72a70995f725269f1c1c206d6dbeb090e88413b1302a456c600041175a7a484c2f0172454f7bed65a8ab95ffed7647d8ad03e6c23b1e3bbc9845f78cd17
  languageName: node
  linkType: hard

"@semantic-release/changelog@npm:^6.0.1":
  version: 6.0.1
  resolution: "@semantic-release/changelog@npm:6.0.1"
  dependencies:
    "@semantic-release/error": ^3.0.0
    aggregate-error: ^3.0.0
    fs-extra: ^9.0.0
    lodash: ^4.17.4
  peerDependencies:
    semantic-release: ">=18.0.0"
  checksum: a7c999f20297f229ebb32dc65f56c3aee237d941b478a1c75f5e904382c66fc4054bf3da93b1f5382e0b689147a825665500332f70807bfed952d312d2f501ac
  languageName: node
  linkType: hard

"@semantic-release/commit-analyzer@npm:^13.0.0-beta.1":
  version: 13.0.1
  resolution: "@semantic-release/commit-analyzer@npm:13.0.1"
  dependencies:
    conventional-changelog-angular: ^8.0.0
    conventional-changelog-writer: ^8.0.0
    conventional-commits-filter: ^5.0.0
    conventional-commits-parser: ^6.0.0
    debug: ^4.0.0
    import-from-esm: ^2.0.0
    lodash-es: ^4.17.21
    micromatch: ^4.0.2
  peerDependencies:
    semantic-release: ">=20.1.0"
  checksum: 90f9f92a2a8c7e4b04da074cebda9726e27d94d5dd7792440479b7019060cc97d35a22329c0f310445d1fc9e1a71bb24c723c8121a8e46310e5de1e41ebf8432
  languageName: node
  linkType: hard

"@semantic-release/error@npm:^2.2.0":
  version: 2.2.0
  resolution: "@semantic-release/error@npm:2.2.0"
  checksum: a264a8e16a89e5fcb104ffb2c4339fde3135b90a6d8fe4497a95fe0776a2bf77771d4c702343c47324aefee2e2a2af72f48b5310c84e8a0902fadb631272700f
  languageName: node
  linkType: hard

"@semantic-release/error@npm:^3.0.0":
  version: 3.0.0
  resolution: "@semantic-release/error@npm:3.0.0"
  checksum: 29c4391ecbefd9ea991f8fdf5ab3ceb9c4830281da56d9dbacd945c476cb86f10c3b55cd4a6597098c0ea3a59f1ec4752132abeea633e15972f49f4704e61d35
  languageName: node
  linkType: hard

"@semantic-release/error@npm:^4.0.0":
  version: 4.0.0
  resolution: "@semantic-release/error@npm:4.0.0"
  checksum: 01213195ae3b8e2490b0d0db79525f7abbb1cc795494b46b8022f81ab1f24f5eab6232b549528b437cff872a66d36649f2fb4f3b56eba351d947a02cccc81ecc
  languageName: node
  linkType: hard

"@semantic-release/git@npm:^10.0.1":
  version: 10.0.1
  resolution: "@semantic-release/git@npm:10.0.1"
  dependencies:
    "@semantic-release/error": ^3.0.0
    aggregate-error: ^3.0.0
    debug: ^4.0.0
    dir-glob: ^3.0.0
    execa: ^5.0.0
    lodash: ^4.17.4
    micromatch: ^4.0.0
    p-reduce: ^2.0.0
  peerDependencies:
    semantic-release: ">=18.0.0"
  checksum: b0a346acaf13d1bbd8d8d895bb0dee025dd6d4742769b5dd875018fff8fcfe0f5414299dbe1ed026e53b8f8b04eeceef49a3d56c5f6506016c656df95d2ced04
  languageName: node
  linkType: hard

"@semantic-release/github@npm:^11.0.0":
  version: 11.0.1
  resolution: "@semantic-release/github@npm:11.0.1"
  dependencies:
    "@octokit/core": ^6.0.0
    "@octokit/plugin-paginate-rest": ^11.0.0
    "@octokit/plugin-retry": ^7.0.0
    "@octokit/plugin-throttling": ^9.0.0
    "@semantic-release/error": ^4.0.0
    aggregate-error: ^5.0.0
    debug: ^4.3.4
    dir-glob: ^3.0.1
    globby: ^14.0.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.0
    issue-parser: ^7.0.0
    lodash-es: ^4.17.21
    mime: ^4.0.0
    p-filter: ^4.0.0
    url-join: ^5.0.0
  peerDependencies:
    semantic-release: ">=24.1.0"
  checksum: e6b3a78ab3aadf8f6c05b530bcb395060a414f0ca3000e6e12874db869a2220a3e81bff08ecdb74ee091234534ddc69e0fd28ff7d5b6ffaed996fad0c245377a
  languageName: node
  linkType: hard

"@semantic-release/npm@npm:^12.0.0":
  version: 12.0.1
  resolution: "@semantic-release/npm@npm:12.0.1"
  dependencies:
    "@semantic-release/error": ^4.0.0
    aggregate-error: ^5.0.0
    execa: ^9.0.0
    fs-extra: ^11.0.0
    lodash-es: ^4.17.21
    nerf-dart: ^1.0.0
    normalize-url: ^8.0.0
    npm: ^10.5.0
    rc: ^1.2.8
    read-pkg: ^9.0.0
    registry-auth-token: ^5.0.0
    semver: ^7.1.2
    tempy: ^3.0.0
  peerDependencies:
    semantic-release: ">=20.1.0"
  checksum: 56081a899111c7a5fdf4780eff65e2e587c2179596fc24f139d8e7cc1269b2282900176bb6a95af403f3dbf6e777598a4c2ade765b5c74f8b16704b324fcb1b4
  languageName: node
  linkType: hard

"@semantic-release/release-notes-generator@npm:^14.0.0-beta.1":
  version: 14.0.3
  resolution: "@semantic-release/release-notes-generator@npm:14.0.3"
  dependencies:
    conventional-changelog-angular: ^8.0.0
    conventional-changelog-writer: ^8.0.0
    conventional-commits-filter: ^5.0.0
    conventional-commits-parser: ^6.0.0
    debug: ^4.0.0
    get-stream: ^7.0.0
    import-from-esm: ^2.0.0
    into-stream: ^7.0.0
    lodash-es: ^4.17.21
    read-package-up: ^11.0.0
  peerDependencies:
    semantic-release: ">=20.1.0"
  checksum: bb9b0913e17a449e2a495aeed12ee4d5d116c0876a2420f8c3308d4f89b279c64f78f79f3b7e9812f84f27dd4abba1009bb0d3418ab559ddb1665a0b211d63d7
  languageName: node
  linkType: hard

"@sigstore/bundle@npm:^3.0.0":
  version: 3.0.0
  resolution: "@sigstore/bundle@npm:3.0.0"
  dependencies:
    "@sigstore/protobuf-specs": ^0.3.2
  checksum: cef110acd000dea0cbdf01f48e979ea574e4f29439d2367bad255dcbe7ec185d66873f7c2428bbe109dffd944a50786e9eefc802f008d464dc683c1bbc8fb2d5
  languageName: node
  linkType: hard

"@sigstore/core@npm:^2.0.0":
  version: 2.0.0
  resolution: "@sigstore/core@npm:2.0.0"
  checksum: fd21df6ce574ef8fed855955ce864523368bdca8202ed9d90f2b4822f4889315a23f52eef72cbf09534af669329c8affdd36a3615c9598eb9311a4cc22f3f21a
  languageName: node
  linkType: hard

"@sigstore/protobuf-specs@npm:^0.3.2":
  version: 0.3.3
  resolution: "@sigstore/protobuf-specs@npm:0.3.3"
  checksum: 5457c64efd564ef1a7fcf06fe48fc2c96f2e5865b9a4cde818ebbee6e592492b3834bd8f1c1202e5790f21278ad45f2dc771c1f7328175c099147ce3a680614a
  languageName: node
  linkType: hard

"@sigstore/sign@npm:^3.0.0":
  version: 3.0.0
  resolution: "@sigstore/sign@npm:3.0.0"
  dependencies:
    "@sigstore/bundle": ^3.0.0
    "@sigstore/core": ^2.0.0
    "@sigstore/protobuf-specs": ^0.3.2
    make-fetch-happen: ^14.0.1
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
  checksum: 8a7523ef128808c0b962757e6451269ed966b56b5487c6e2b0708c3703e70d83d70911d0731cbc35e926400fbcf1ecb5187cf82aec3334d18a72e49e78f79330
  languageName: node
  linkType: hard

"@sigstore/tuf@npm:^3.0.0":
  version: 3.0.0
  resolution: "@sigstore/tuf@npm:3.0.0"
  dependencies:
    "@sigstore/protobuf-specs": ^0.3.2
    tuf-js: ^3.0.1
  checksum: 184b84642abb8cad377a845892d1039dfd29b4275f324f1987ed4153689887adddfe5172bfb7f1851f3f915c967f78e51d40e7efb6877a5ae61313a3b44af754
  languageName: node
  linkType: hard

"@sigstore/verify@npm:^2.0.0":
  version: 2.0.0
  resolution: "@sigstore/verify@npm:2.0.0"
  dependencies:
    "@sigstore/bundle": ^3.0.0
    "@sigstore/core": ^2.0.0
    "@sigstore/protobuf-specs": ^0.3.2
  checksum: 987d3d927f3be11f3a50287b4a30aff1abd622b59cbc233f633b32bf5a331c8b7da5624b27b01aa0213da4e14e28def23e43f353d95033a4a3b3f8b44a59564b
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^4.6.0":
  version: 4.6.0
  resolution: "@sindresorhus/is@npm:4.6.0"
  checksum: 83839f13da2c29d55c97abc3bc2c55b250d33a0447554997a85c539e058e57b8da092da396e252b11ec24a0279a0bed1f537fa26302209327060643e327f81d2
  languageName: node
  linkType: hard

"@sindresorhus/merge-streams@npm:^2.1.0":
  version: 2.3.0
  resolution: "@sindresorhus/merge-streams@npm:2.3.0"
  checksum: e989d53dee68d7e49b4ac02ae49178d561c461144cea83f66fa91ff012d981ad0ad2340cbd13f2fdb57989197f5c987ca22a74eb56478626f04e79df84291159
  languageName: node
  linkType: hard

"@sindresorhus/merge-streams@npm:^4.0.0":
  version: 4.0.0
  resolution: "@sindresorhus/merge-streams@npm:4.0.0"
  checksum: 5759d31dfd822999bbe3ddcf72d4b15dc3d99ea51dd5b3210888f3348234eaff0f44bc999bef6b3c328daeb34e862a63b2c4abe5590acec541f93bc6fa016c9d
  languageName: node
  linkType: hard

"@splitsoftware/splitio-commons@npm:1.9.0":
  version: 1.9.0
  resolution: "@splitsoftware/splitio-commons@npm:1.9.0"
  dependencies:
    tslib: ^2.3.1
  peerDependencies:
    ioredis: ^4.28.0
  peerDependenciesMeta:
    ioredis:
      optional: true
  checksum: 6e9d17b6f4a5529e0776b88395bc7d1c9e9e9e9b1593cb15be38738f8d779caa5d3a225504532def74af5635f99148e47c3c8a2dff4b4137a40d780c5b710590
  languageName: node
  linkType: hard

"@splitsoftware/splitio-commons@npm:1.9.1":
  version: 1.9.1
  resolution: "@splitsoftware/splitio-commons@npm:1.9.1"
  dependencies:
    tslib: ^2.3.1
  peerDependencies:
    ioredis: ^4.28.0
  peerDependenciesMeta:
    ioredis:
      optional: true
  checksum: 5bd0f3f43fa29e0a77e102c3a499b8e064fc0ee5c87ca6feccb9a81eeafc7055788978b4634b229ca0bc84b2063c06f7c4c5bc2fb4c4e3f24dfe493aa6902e29
  languageName: node
  linkType: hard

"@splitsoftware/splitio-react@npm:1.9.0":
  version: 1.9.0
  resolution: "@splitsoftware/splitio-react@npm:1.9.0"
  dependencies:
    "@splitsoftware/splitio": 10.23.0
    memoize-one: ^5.1.1
    shallowequal: ^1.1.0
  peerDependencies:
    react: ">=16.3.0"
  checksum: f32e602ceb0e1595fa8bb8f6c217aea97d008c8fc6ae2a12a03e8e2cb973272a9a2b8d08b77a6c0dee2c7b552fdcafd2a186fdd45dab913d3dce76f6e40c624a
  languageName: node
  linkType: hard

"@splitsoftware/splitio@npm:10.23.0":
  version: 10.23.0
  resolution: "@splitsoftware/splitio@npm:10.23.0"
  dependencies:
    "@splitsoftware/splitio-commons": 1.9.0
    "@types/google.analytics": 0.0.40
    "@types/ioredis": ^4.28.0
    bloom-filters: ^3.0.0
    eventsource: ^1.1.2
    ioredis: ^4.28.0
    js-yaml: ^3.13.1
    node-fetch: ^2.6.7
    unfetch: ^4.2.0
  dependenciesMeta:
    eventsource:
      optional: true
  checksum: 597b15c45410fc916062314c07eeeaeaa8e13e82157e82884d26138fd67d4cca0234745c9ce8ed249b954b16faa50d7665248cc7ecc6a945254595baafb2ccc8
  languageName: node
  linkType: hard

"@splitsoftware/splitio@npm:^10.23.1":
  version: 10.23.1
  resolution: "@splitsoftware/splitio@npm:10.23.1"
  dependencies:
    "@splitsoftware/splitio-commons": 1.9.1
    "@types/google.analytics": 0.0.40
    "@types/ioredis": ^4.28.0
    bloom-filters: ^3.0.0
    eventsource: ^1.1.2
    ioredis: ^4.28.0
    js-yaml: ^3.13.1
    node-fetch: ^2.6.7
    unfetch: ^4.2.0
  dependenciesMeta:
    eventsource:
      optional: true
  checksum: aa33080275bfd70a172b2b4e4771e07e6e06b8b11ae4e643c24f8d3d09b4635468ff03a75789140879a053c415fa54b28c6e869d6df356e81a86645a5f2cad37
  languageName: node
  linkType: hard

"@swc/counter@npm:0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.15":
  version: 0.5.15
  resolution: "@swc/helpers@npm:0.5.15"
  dependencies:
    tslib: ^2.8.0
  checksum: 1a9e0dbb792b2d1e0c914d69c201dbc96af3a0e6e6e8cf5a7f7d6a5d7b0e8b762915cd4447acb6b040e2ecc1ed49822875a7239f99a2d63c96c3c3407fb6fccf
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.8
  resolution: "@tsconfig/node10@npm:1.0.8"
  checksum: b8d5fffbc6b17ef64ef74f7fdbccee02a809a063ade785c3648dae59406bc207f70ea2c4296f92749b33019fa36a5ae716e42e49cc7f1bbf0fd147be0d6b970a
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.9
  resolution: "@tsconfig/node12@npm:1.0.9"
  checksum: a01b2400ab3582b86b589c6d31dcd0c0656f333adecde85d6d7d4086adb059808b82692380bb169546d189bf771ae21d02544a75b57bd6da4a5dd95f8567bec9
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.1
  resolution: "@tsconfig/node14@npm:1.0.1"
  checksum: 976345e896c0f059867f94f8d0f6ddb8b1844fb62bf36b727de8a9a68f024857e5db97ed51d3325e23e0616a5e48c034ff51a8d595b3fe7e955f3587540489be
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.2
  resolution: "@tsconfig/node16@npm:1.0.2"
  checksum: ca94d3639714672bbfd55f03521d3f56bb6a25479bd425da81faf21f13e1e9d15f40f97377dedbbf477a5841c5b0c8f4cd1b391f33553d750b9202c54c2c07aa
  languageName: node
  linkType: hard

"@tufjs/canonical-json@npm:2.0.0":
  version: 2.0.0
  resolution: "@tufjs/canonical-json@npm:2.0.0"
  checksum: cc719a1d0d0ae1aa1ba551a82c87dcbefac088e433c03a3d8a1d547ea721350e47dab4ab5b0fca40d5c7ab1f4882e72edc39c9eae15bf47c45c43bcb6ee39f4f
  languageName: node
  linkType: hard

"@tufjs/models@npm:3.0.1":
  version: 3.0.1
  resolution: "@tufjs/models@npm:3.0.1"
  dependencies:
    "@tufjs/canonical-json": 2.0.0
    minimatch: ^9.0.5
  checksum: 95b179bc09e5a0b6dfc9e7001e15882e863e034bf41e0502e89f2fa82cb3f6d5bd9edaefd2baf2a7f515abdb521127adf771e8bbe66f3e7f212e3b777ae993f5
  languageName: node
  linkType: hard

"@types/glob@npm:^7.1.1":
  version: 7.2.0
  resolution: "@types/glob@npm:7.2.0"
  dependencies:
    "@types/minimatch": "*"
    "@types/node": "*"
  checksum: 6ae717fedfdfdad25f3d5a568323926c64f52ef35897bcac8aca8e19bc50c0bd84630bbd063e5d52078b2137d8e7d3c26eabebd1a2f03ff350fff8a91e79fc19
  languageName: node
  linkType: hard

"@types/google.analytics@npm:0.0.40":
  version: 0.0.40
  resolution: "@types/google.analytics@npm:0.0.40"
  checksum: 7c9b1c63b049f1018a30ed4ff193fbe76d3eee6d98c268346da82095709f1822e50e7130373187219dee765a5d10ed32d58962ff21801b39b219d1932f62ccdc
  languageName: node
  linkType: hard

"@types/ioredis@npm:^4.28.0":
  version: 4.28.10
  resolution: "@types/ioredis@npm:4.28.10"
  dependencies:
    "@types/node": "*"
  checksum: 0f2788cf25f490d3b345db8c5f8b8ce3f6c92cc99abcf744c8f974f02b9b3875233b3d22098614c462a0d6c41c523bd655509418ea88eb6249db6652290ce7cf
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.12":
  version: 7.0.12
  resolution: "@types/json-schema@npm:7.0.12"
  checksum: 00239e97234eeb5ceefb0c1875d98ade6e922bfec39dd365ec6bd360b5c2f825e612ac4f6e5f1d13601b8b30f378f15e6faa805a3a732f4a1bbe61915163d293
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: e60b153664572116dfea673c5bda7778dbff150498f44f998e34b5886d8afc47f16799280e4b6e241c0472aef1bc36add771c569c68fc5125fc2ae519a3eb9ac
  languageName: node
  linkType: hard

"@types/mdast@npm:^3.0.0":
  version: 3.0.10
  resolution: "@types/mdast@npm:3.0.10"
  dependencies:
    "@types/unist": "*"
  checksum: 3f587bfc0a9a2403ecadc220e61031b01734fedaf82e27eb4d5ba039c0eb54db8c85681ccc070ab4df3f7ec711b736a82b990e69caa14c74bf7ac0ccf2ac7313
  languageName: node
  linkType: hard

"@types/minimatch@npm:*":
  version: 5.1.2
  resolution: "@types/minimatch@npm:5.1.2"
  checksum: 0391a282860c7cb6fe262c12b99564732401bdaa5e395bee9ca323c312c1a0f45efbf34dce974682036e857db59a5c9b1da522f3d6055aeead7097264c8705a8
  languageName: node
  linkType: hard

"@types/minimist@npm:^1.2.0":
  version: 1.2.2
  resolution: "@types/minimist@npm:1.2.2"
  checksum: b8da83c66eb4aac0440e64674b19564d9d86c80ae273144db9681e5eeff66f238ade9515f5006ffbfa955ceff8b89ad2bd8ec577d7caee74ba101431fb07045d
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=12":
  version: 18.11.18
  resolution: "@types/node@npm:18.11.18"
  checksum: 03f17f9480f8d775c8a72da5ea7e9383db5f6d85aa5fefde90dd953a1449bd5e4ffde376f139da4f3744b4c83942166d2a7603969a6f8ea826edfb16e6e3b49d
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0, @types/normalize-package-data@npm:^2.4.1, @types/normalize-package-data@npm:^2.4.3":
  version: 2.4.4
  resolution: "@types/normalize-package-data@npm:2.4.4"
  checksum: 65dff72b543997b7be8b0265eca7ace0e34b75c3e5fee31de11179d08fa7124a7a5587265d53d0409532ecb7f7fba662c2012807963e1f9b059653ec2c83ee05
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "@types/parse-json@npm:4.0.0"
  checksum: fd6bce2b674b6efc3db4c7c3d336bd70c90838e8439de639b909ce22f3720d21344f52427f1d9e57b265fcb7f6c018699b99e5e0c208a1a4823014269a6bf35b
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.5
  resolution: "@types/prop-types@npm:15.7.5"
  checksum: 5b43b8b15415e1f298243165f1d44390403bb2bd42e662bca3b5b5633fdd39c938e91b7fce3a9483699db0f7a715d08cef220c121f723a634972fdf596aec980
  languageName: node
  linkType: hard

"@types/react@npm:^18.3.20":
  version: 18.3.20
  resolution: "@types/react@npm:18.3.20"
  dependencies:
    "@types/prop-types": "*"
    csstype: ^3.0.2
  checksum: a93a4eec87c671ad9d68eaedaa2aa3688926409802939d2b291800cf926c771eb505a18721174364217ae9e1e8b89d09c1519f06ba1f168271de9f4c832710ea
  languageName: node
  linkType: hard

"@types/semver@npm:^7.5.0, @types/semver@npm:^7.5.5":
  version: 7.5.8
  resolution: "@types/semver@npm:7.5.8"
  checksum: ea6f5276f5b84c55921785a3a27a3cd37afee0111dfe2bcb3e03c31819c197c782598f17f0b150a69d453c9584cd14c4c4d7b9a55d2c5e6cacd4d66fdb3b3663
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^2.0.0, @types/unist@npm:^2.0.2":
  version: 2.0.6
  resolution: "@types/unist@npm:2.0.6"
  checksum: 25cb860ff10dde48b54622d58b23e66214211a61c84c0f15f88d38b61aa1b53d4d46e42b557924a93178c501c166aa37e28d7f6d994aba13d24685326272d5db
  languageName: node
  linkType: hard

"@types/uuid@npm:^9.0.8":
  version: 9.0.8
  resolution: "@types/uuid@npm:9.0.8"
  checksum: b8c60b7ba8250356b5088302583d1704a4e1a13558d143c549c408bf8920535602ffc12394ede77f8a8083511b023704bc66d1345792714002bfa261b17c5275
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^6.1.0":
  version: 6.2.1
  resolution: "@typescript-eslint/eslint-plugin@npm:6.2.1"
  dependencies:
    "@eslint-community/regexpp": ^4.5.1
    "@typescript-eslint/scope-manager": 6.2.1
    "@typescript-eslint/type-utils": 6.2.1
    "@typescript-eslint/utils": 6.2.1
    "@typescript-eslint/visitor-keys": 6.2.1
    debug: ^4.3.4
    graphemer: ^1.4.0
    ignore: ^5.2.4
    natural-compare: ^1.4.0
    natural-compare-lite: ^1.4.0
    semver: ^7.5.4
    ts-api-utils: ^1.0.1
  peerDependencies:
    "@typescript-eslint/parser": ^6.0.0 || ^6.0.0-alpha
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: e73f3fe36519d895037d223f3ddf200b97e17bcde9390984118c38733add1edf996357c809ec2db92cec61bc7c9e5a3d9a583e0d0f92fa9c3919b68716a27b37
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^6.1.0, @typescript-eslint/parser@npm:^6.2.1":
  version: 6.2.1
  resolution: "@typescript-eslint/parser@npm:6.2.1"
  dependencies:
    "@typescript-eslint/scope-manager": 6.2.1
    "@typescript-eslint/types": 6.2.1
    "@typescript-eslint/typescript-estree": 6.2.1
    "@typescript-eslint/visitor-keys": 6.2.1
    debug: ^4.3.4
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: cf4768cbfc696ce1d4b15ae55b3d2b52761e91a4a80e738cf3a75c501c2257d735cd6e462567965069d0d693a8cf5463ab9e8b97c36c6ed1fccd3c1c09855bdb
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:6.2.1":
  version: 6.2.1
  resolution: "@typescript-eslint/scope-manager@npm:6.2.1"
  dependencies:
    "@typescript-eslint/types": 6.2.1
    "@typescript-eslint/visitor-keys": 6.2.1
  checksum: 3bb461678c7e729895c5ac16781ec7d66efc6ffa944bb49693ce8e9560f9a6cac70929157c0fc0875b2829ae19a5cdabb97973ddcfb7e81c16e22cdd5d39e3fd
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:6.2.1":
  version: 6.2.1
  resolution: "@typescript-eslint/type-utils@npm:6.2.1"
  dependencies:
    "@typescript-eslint/typescript-estree": 6.2.1
    "@typescript-eslint/utils": 6.2.1
    debug: ^4.3.4
    ts-api-utils: ^1.0.1
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 7f8d80f03e6ddc1838307a2a4df61dc4bd8400efb9dcc7316063ae293fce54afad238404a0c25cd2cdaceee73ae514f254b850bd7ff11e2def700d5d6b90af05
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:6.2.1":
  version: 6.2.1
  resolution: "@typescript-eslint/types@npm:6.2.1"
  checksum: 388d32f15a9db8ad5d80794caf9ab280d6e5a428efdf4f6a6dfc4069afe4d19da32d628acf638e4c5b92ee77a9a18eecf728a778a3b91cc8a24484af579fc9cf
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:6.2.1":
  version: 6.2.1
  resolution: "@typescript-eslint/typescript-estree@npm:6.2.1"
  dependencies:
    "@typescript-eslint/types": 6.2.1
    "@typescript-eslint/visitor-keys": 6.2.1
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    semver: ^7.5.4
    ts-api-utils: ^1.0.1
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 3d9beeb5e36b8827de5c160ed8e5c111dd66ca00671b183409b051e242b291480679b900bb74aaf4895dcae49497037567d3fcbbe67fa9930786ddd01c685f04
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:6.2.1":
  version: 6.2.1
  resolution: "@typescript-eslint/utils@npm:6.2.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@types/json-schema": ^7.0.12
    "@types/semver": ^7.5.0
    "@typescript-eslint/scope-manager": 6.2.1
    "@typescript-eslint/types": 6.2.1
    "@typescript-eslint/typescript-estree": 6.2.1
    semver: ^7.5.4
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  checksum: d16356a633f39d988a9af159da15e28c6a28fa47abce372061c79cf186d193d148e1c32862c9702ff87e2a06f7a2f82773e4b56320a39f432f4b1a989f8005ad
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:6.2.1":
  version: 6.2.1
  resolution: "@typescript-eslint/visitor-keys@npm:6.2.1"
  dependencies:
    "@typescript-eslint/types": 6.2.1
    eslint-visitor-keys: ^3.4.1
  checksum: c05a1c45129f2cf9a8c49dadc3da10b675232e59b69dfe9fdc0bfb45d3be077ceff78097baf50e502dab3e71ce9fd799d2015e356a4be2787ee10c6c7a44ea8a
  languageName: node
  linkType: hard

"@unlockre/eslint-config@npm:^2.1.2":
  version: 2.1.2
  resolution: "@unlockre/eslint-config@npm:2.1.2::__archiveUrl=https%3A%2F%2Fnpm.pkg.github.com%2Fdownload%2F%40unlockre%2Feslint-config%2F2.1.2%2F31c6bc62276f64abceb620cb495d2abe50964532"
  dependencies:
    "@babel/core": ^7.22.9
    "@babel/eslint-parser": ^7.22.9
    "@babel/preset-react": ^7.22.5
    "@babel/preset-typescript": ^7.22.5
    "@next/eslint-plugin-next": ^12.3.4
    "@rushstack/eslint-patch": ^1.3.2
    "@typescript-eslint/eslint-plugin": ^6.1.0
    "@typescript-eslint/parser": ^6.1.0
    "@unlockre/eslint-plugin-switch-case": ^1.0.0
    eslint: ^8.45.0
    eslint-config-prettier: ^8.8.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-import: ^2.27.5
    eslint-plugin-next: ^0.0.0
    eslint-plugin-prefer-arrow-functions: ^3.1.4
    eslint-plugin-prettier: ^5.0.0
    eslint-plugin-react: ^7.33.0
    eslint-plugin-react-hooks: ^4.6.0
    eslint-plugin-sort-destructure-keys: ^1.5.0
    prettier: ^3.0.0
  checksum: f64dd73e934ec1988aa44abc4f21e29cfd952a6172c4bc2a9221713fcb1d4a7fd66a764510f4717e1f83f6c6546035322107db59d2d7154e9be4e1af95e843c6
  languageName: node
  linkType: hard

"@unlockre/eslint-plugin-switch-case@npm:^1.0.0":
  version: 1.0.0
  resolution: "@unlockre/eslint-plugin-switch-case@npm:1.0.0::__archiveUrl=https%3A%2F%2Fnpm.pkg.github.com%2Fdownload%2F%40unlockre%2Feslint-plugin-switch-case%2F1.0.0%2Fbfd5ed5902114f8ad4d8b10d07e0b3a48139f209"
  dependencies:
    lodash.zipobject: ^4.1.3
  checksum: 29c1310b797b68cd71382088e2e3874b7dae5924dc221b3411a12346e213ec488b8aea0ad40e1631a1def07d84bef178ac1a133a852fa0d7102ff6627d21c522
  languageName: node
  linkType: hard

"@unlockre/utils-amplitude@workspace:packages/utils-amplitude":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-amplitude@workspace:packages/utils-amplitude"
  dependencies:
    "@amplitude/analytics-browser": ^2.4.1
    "@auth0/auth0-react": ^2.2.0
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@types/react": ^18.3.20
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    react: ^18.3.1
    react-dom: ^18.3.1
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  peerDependencies:
    "@amplitude/analytics-browser": ^2.0.0
    "@auth0/auth0-react": ^2.0.0
    react: ^18.0.0
    react-dom: ^18.0.0
  languageName: unknown
  linkType: soft

"@unlockre/utils-array@workspace:packages/utils-array":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-array@workspace:packages/utils-array"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    array.prototype.groupby: ^1.1.0
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-auth0@workspace:packages/utils-auth0":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-auth0@workspace:packages/utils-auth0"
  dependencies:
    "@auth0/auth0-react": ^2.1.0
    "@auth0/auth0-spa-js": ^2.0.7
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@types/react": ^18.3.20
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    "@unlockre/utils-object": "workspace:^"
    "@unlockre/utils-react": "workspace:^"
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    react: ^18.3.1
    react-dom: ^18.3.1
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  peerDependencies:
    "@auth0/auth0-react": ^2.0.0
    "@auth0/auth0-spa-js": ^2.0.0
    react: ^18.0.0
    react-dom: ^18.0.0
  languageName: unknown
  linkType: soft

"@unlockre/utils-date@workspace:packages/utils-date":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-date@workspace:packages/utils-date"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    "@unlockre/utils-number": "workspace:^"
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-dom@workspace:^, @unlockre/utils-dom@workspace:packages/utils-dom":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-dom@workspace:packages/utils-dom"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    jszip: ^3.10.1
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-formatting@workspace:packages/utils-formatting":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-formatting@workspace:packages/utils-formatting"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-http@workspace:packages/utils-http":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-http@workspace:packages/utils-http"
  dependencies:
    "@mgtitimoli/utils-error": ^1.0.2
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-intl@workspace:packages/utils-intl":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-intl@workspace:packages/utils-intl"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-next@workspace:packages/utils-next":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-next@workspace:packages/utils-next"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@types/react": ^18.3.20
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    "@unlockre/utils-object": "workspace:^"
    "@unlockre/utils-react": "workspace:^"
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    next: ^15.2.4
    prettier: ^3.0.0
    react: ^18.3.1
    react-dom: ^18.3.1
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  peerDependencies:
    next: ^15.0.0
    react: ^18.0.0
    react-dom: ^18.0.0
  languageName: unknown
  linkType: soft

"@unlockre/utils-number@workspace:^, @unlockre/utils-number@workspace:packages/utils-number":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-number@workspace:packages/utils-number"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-object@workspace:^, @unlockre/utils-object@workspace:packages/utils-object":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-object@workspace:packages/utils-object"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-packages@workspace:.":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-packages@workspace:."
  dependencies:
    "@commitlint/cli": ^16.2.1
    "@commitlint/config-conventional": ^16.2.1
    "@types/uuid": ^9.0.8
    husky: ^7.0.4
    lint-staged: ^12.3.4
    uuid: ^9.0.1
  languageName: unknown
  linkType: soft

"@unlockre/utils-promise@workspace:packages/utils-promise":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-promise@workspace:packages/utils-promise"
  dependencies:
    "@mgtitimoli/utils-error": ^1.0.2
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-react@workspace:^, @unlockre/utils-react@workspace:packages/utils-react":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-react@workspace:packages/utils-react"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@types/react": ^18.3.20
    "@types/uuid": ^9.0.8
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    "@unlockre/utils-object": "workspace:^"
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    fast-deep-equal: ^3.1.3
    fuse.js: ^7.0.0
    prettier: ^3.0.0
    react: ^18.3.1
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
    use-debounce: ^10.0.1
    uuid: ^9.0.1
  peerDependencies:
    react: ^18.0.0
  languageName: unknown
  linkType: soft

"@unlockre/utils-split-io@workspace:^, @unlockre/utils-split-io@workspace:packages/utils-split-io":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-split-io@workspace:packages/utils-split-io"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@splitsoftware/splitio": ^10.23.1
    "@splitsoftware/splitio-react": 1.9.0
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    "@unlockre/utils-react": "workspace:^"
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    react: ^18.3.1
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
    zod: ^3.22.4
  peerDependencies:
    "@splitsoftware/splitio": ^10.23.1
    "@splitsoftware/splitio-react": ^1.9.0
    "@unlockre/utils-auth0": "workspace:^"
    react: ^18.0.0
  languageName: unknown
  linkType: soft

"@unlockre/utils-string@workspace:packages/utils-string":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-string@workspace:packages/utils-string"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@unlockre/utils-swr@workspace:packages/utils-swr":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-swr@workspace:packages/utils-swr"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    swr: ^2.2.4
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.2
    typescript: ^5.1.6
  peerDependencies:
    react: ^18.0.0
    swr: ^2.0.0
  languageName: unknown
  linkType: soft

"@unlockre/utils-userback@workspace:packages/utils-userback":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-userback@workspace:packages/utils-userback"
  dependencies:
    "@auth0/auth0-react": ^2.4.0
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    "@unlockre/utils-dom": "workspace:^"
    "@unlockre/utils-split-io": "workspace:^"
    "@userback/widget": ^0.3.11
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  peerDependencies:
    "@auth0/auth0-react": ^2.0.0
    "@unlockre/utils-split-io": "workspace:^"
  languageName: unknown
  linkType: soft

"@unlockre/utils-validation@workspace:packages/utils-validation":
  version: 0.0.0-use.local
  resolution: "@unlockre/utils-validation@workspace:packages/utils-validation"
  dependencies:
    "@semantic-release/changelog": ^6.0.1
    "@semantic-release/git": ^10.0.1
    "@typescript-eslint/parser": ^6.2.1
    "@unlockre/eslint-config": ^2.1.2
    eslint: ^8.46.0
    eslint-import-resolver-typescript: ^3.5.5
    eslint-plugin-tsc: ^2.0.0
    prettier: ^3.0.0
    semantic-release: ^24.2.1
    semantic-release-monorepo: ^8.0.2
    semantic-release-slack-bot: ^4.0.2
    semantic-release-yarn: ^3.0.2
    tsconfig-replace-paths: ^0.0.14
    tslib: ^2.6.1
    typescript: ^5.1.6
  languageName: unknown
  linkType: soft

"@userback/widget@npm:^0.3.11":
  version: 0.3.11
  resolution: "@userback/widget@npm:0.3.11"
  checksum: 9aaf7a37b1f041c14c302e46d5d3ac0138367df277944d019f1fd45ba25f42fb64060a270b5ed922ca0db50f3f54f88af4718dd8584e4a44032e67191725619a
  languageName: node
  linkType: hard

"JSONStream@npm:^1.0.4":
  version: 1.3.5
  resolution: "JSONStream@npm:1.3.5"
  dependencies:
    jsonparse: ^1.2.0
    through: ">=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 2605fa124260c61bad38bb65eba30d2f72216a78e94d0ab19b11b4e0327d572b8d530c0c9cc3b0764f727ad26d39e00bf7ebad57781ca6368394d73169c59e46
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 0e994ad2aa6575f94670d8a2149afe94465de9cedaaaac364e7fb43a40c3691c980ff74899f682f4ca58fa96b4cbd7421a015d3a6defe43a442117d7821a2f36
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.0
  resolution: "abbrev@npm:3.0.0"
  checksum: 2500075b5ef85e97c095ab6ab2ea640dcf90bb388f46398f4d347b296f53399f984ec9462c74bee81df6bba56ef5fd9dbc2fb29076b1feb0023e0f52d43eb984
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.2.0
  resolution: "acorn-walk@npm:8.2.0"
  checksum: 1715e76c01dd7b2d4ca472f9c58968516a4899378a63ad5b6c2d668bba8da21a71976c14ec5f5b75f887b6317c4ae0b897ab141c831d741dc76024d8745f1ad1
  languageName: node
  linkType: hard

"acorn@npm:^8.4.1, acorn@npm:^8.9.0":
  version: 8.10.0
  resolution: "acorn@npm:8.10.0"
  bin:
    acorn: bin/acorn
  checksum: 538ba38af0cc9e5ef983aee196c4b8b4d87c0c94532334fa7e065b2c8a1f85863467bb774231aae91613fcda5e68740c15d97b1967ae3394d20faddddd8af61d
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"aggregate-error@npm:^5.0.0":
  version: 5.0.0
  resolution: "aggregate-error@npm:5.0.0"
  dependencies:
    clean-stack: ^5.2.0
    indent-string: ^5.0.0
  checksum: 37834eb0dac6ebd05ca8aa82e00deeb65fb7b1462c68ccb620221ba1753640fcb249e46c03401b470701a58826b65426deda83783fc2e8347c4b5037b2724d9b
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4, ajv@npm:^6.12.6":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.0":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: 93111c42189c0a6bed9cdb4d7f2829548e943827ee8479c74d6e0b22ee127b2a21d3f8b5ca57723b8ef78ce011fbfc2784350eb2bde3ccfccf2f575fa8489815
  languageName: node
  linkType: hard

"ansi-escapes@npm:^7.0.0":
  version: 7.0.0
  resolution: "ansi-escapes@npm:7.0.0"
  dependencies:
    environment: ^1.0.0
  checksum: 19baa61e68d1998c03b3b8bd023653a6c2667f0ed6caa9a00780ffd6f0a14f4a6563c57a38b3c0aba71bd704cd49c4c8df41be60bd81c957409f91e9dd49051f
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1, ansi-regex@npm:^6.1.0":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.0.0, ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 0ee8a9bdbe882c90464d75d1f55cf027f5458650c4bd1f0467e65aec38ccccda07ca5844969ee77ed46d04e7dded3eaceb027e8d32f385688523fe305fa7e1de
  languageName: node
  linkType: hard

"aproba@npm:^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"archy@npm:~1.0.0":
  version: 1.0.0
  resolution: "archy@npm:1.0.0"
  checksum: 504ae7af655130bab9f471343cfdb054feaec7d8e300e13348bc9fe9e660f83d422e473069584f73233c701ae37d1c8452ff2522f2a20c38849e0f406f1732ac
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 544af8dd3f60546d3e4aff084d451b96961d2267d668670199692f8d054f0415d86fc5497d0e641e91546f0aa920e7c29e5250e99fc89f5552a34b5d93b77f43
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"argv-formatter@npm:~1.0.0":
  version: 1.0.0
  resolution: "argv-formatter@npm:1.0.0"
  checksum: cf95ea091f4eb0fefdbbc595dbe2e307afee16fc87aad48d72e5e45d5b0b59566dbaa77e45d515242289670904838a501313efffb48ff02f49c6de0c03536a54
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-buffer-byte-length@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    is-array-buffer: ^3.0.1
  checksum: 044e101ce150f4804ad19c51d6c4d4cfa505c5b2577bd179256e4aa3f3f6a0a5e9874c78cd428ee566ac574c8a04d7ce21af9fe52e844abfdccb82b33035a7c3
  languageName: node
  linkType: hard

"array-ify@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-ify@npm:1.0.0"
  checksum: c0502015b319c93dd4484f18036bcc4b654eb76a4aa1f04afbcef11ac918859bb1f5d71ba1f0f1141770db9eef1a4f40f1761753650873068010bbf7bcdae4a4
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6":
  version: 3.1.6
  resolution: "array-includes@npm:3.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    get-intrinsic: ^1.1.3
    is-string: ^1.0.7
  checksum: f22f8cd8ba8a6448d91eebdc69f04e4e55085d09232b5216ee2d476dab3ef59984e8d1889e662c6a0ed939dcb1b57fd05b2c0209c3370942fc41b752c82a2ca5
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.2":
  version: 1.2.2
  resolution: "array.prototype.findlastindex@npm:1.2.2"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    es-shim-unscopables: ^1.0.0
    get-intrinsic: ^1.1.3
  checksum: 8a166359f69a2a751c843f26b9c8cd03d0dc396a92cdcb85f4126b5f1cecdae5b2c0c616a71ea8aff026bde68165b44950b3664404bb73db0673e288495ba264
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1":
  version: 1.3.1
  resolution: "array.prototype.flat@npm:1.3.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    es-shim-unscopables: ^1.0.0
  checksum: 5a8415949df79bf6e01afd7e8839bbde5a3581300e8ad5d8449dea52639e9e59b26a467665622783697917b43bf39940a6e621877c7dd9b3d1c1f97484b9b88b
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.1":
  version: 1.3.1
  resolution: "array.prototype.flatmap@npm:1.3.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    es-shim-unscopables: ^1.0.0
  checksum: 8c1c43a4995f12cf12523436da28515184c753807b3f0bc2ca6c075f71c470b099e2090cc67dba8e5280958fea401c1d0c59e1db0143272aef6cd1103921a987
  languageName: node
  linkType: hard

"array.prototype.groupby@npm:^1.1.0":
  version: 1.1.0
  resolution: "array.prototype.groupby@npm:1.1.0"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
    es-abstract: ^1.19.2
    es-shim-unscopables: ^1.0.0
    get-intrinsic: ^1.1.1
  checksum: ca1c659459a0a153d9346ea6c776b88bdd4e2e3df86d3206d8464082c5ece291cee7b867d75d29b7825c644221a20138903b982fce83ee5fbbafd4d9c9f9bd39
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.1":
  version: 1.1.1
  resolution: "array.prototype.tosorted@npm:1.1.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    es-shim-unscopables: ^1.0.0
    get-intrinsic: ^1.1.3
  checksum: 7923324a67e70a2fc0a6e40237405d92395e45ebd76f5cb89c2a5cf1e66b47aca6baacd0cd628ffd88830b90d47fff268071493d09c9ae123645613dac2c2ca3
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.1":
  version: 1.0.1
  resolution: "arraybuffer.prototype.slice@npm:1.0.1"
  dependencies:
    array-buffer-byte-length: ^1.0.0
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    get-intrinsic: ^1.2.1
    is-array-buffer: ^3.0.2
    is-shared-array-buffer: ^1.0.2
  checksum: e3e9b2a3e988ebfeddce4c7e8f69df730c9e48cb04b0d40ff0874ce3d86b3d1339dd520ffde5e39c02610bc172ecfbd4bc93324b1cabd9554c44a56b131ce0ce
  languageName: node
  linkType: hard

"arrify@npm:^1.0.1":
  version: 1.0.1
  resolution: "arrify@npm:1.0.1"
  checksum: 745075dd4a4624ff0225c331dacb99be501a515d39bcb7c84d24660314a6ec28e68131b137e6f7e16318170842ce97538cd298fc4cd6b2cc798e0b957f2747e7
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 463e2f8e43384f1afb54bc68485c436d7622acec08b6fad269b421cb1d29cebb5af751426793d0961ed243146fe4dc983402f6d5a51b720b277818dbf6f2e49e
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.5":
  version: 1.0.5
  resolution: "available-typed-arrays@npm:1.0.5"
  checksum: 20eb47b3cefd7db027b9bbb993c658abd36d4edd3fe1060e83699a03ee275b0c9b216cc076ff3f2db29073225fb70e7613987af14269ac1fe2a19803ccc97f1a
  languageName: node
  linkType: hard

"bail@npm:^1.0.0":
  version: 1.0.5
  resolution: "bail@npm:1.0.5"
  checksum: 6c334940d7eaa4e656a12fb12407b6555649b6deb6df04270fa806e0da82684ebe4a4e47815b271c794b40f8d6fa286e0c248b14ddbabb324a917fab09b7301a
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-arraybuffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "base64-arraybuffer@npm:1.0.2"
  checksum: 15e6400d2d028bf18be4ed97702b11418f8f8779fb8c743251c863b726638d52f69571d4cc1843224da7838abef0949c670bde46936663c45ad078e89fee5c62
  languageName: node
  linkType: hard

"before-after-hook@npm:^3.0.2":
  version: 3.0.2
  resolution: "before-after-hook@npm:3.0.2"
  checksum: 5f76a9d31909f7f1f7125b7e017ff018799308f5c1fc5a5bfeba9986149da77e6a5cdde0d151671cf374a7fa6452533237bb1de62dfd6c235c20e7c61cc9569d
  languageName: node
  linkType: hard

"big-integer@npm:^1.6.44":
  version: 1.6.51
  resolution: "big-integer@npm:1.6.51"
  checksum: 3d444173d1b2e20747e2c175568bedeebd8315b0637ea95d75fd27830d3b8e8ba36c6af40374f36bdaea7b5de376dcada1b07587cb2a79a928fccdb6e6e3c518
  languageName: node
  linkType: hard

"bin-links@npm:^5.0.0":
  version: 5.0.0
  resolution: "bin-links@npm:5.0.0"
  dependencies:
    cmd-shim: ^7.0.0
    npm-normalize-package-bin: ^4.0.0
    proc-log: ^5.0.0
    read-cmd-shim: ^5.0.0
    write-file-atomic: ^6.0.0
  checksum: b3793e0e5af4b42ac911c4a2abf78c460f0a787c038d4b401ee1017e64823679d8aef25ada5f9c39f53889c62329a23547f724b7a784aab128fb6defd9515485
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.3.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"bloom-filters@npm:^3.0.0":
  version: 3.0.1
  resolution: "bloom-filters@npm:3.0.1"
  dependencies:
    base64-arraybuffer: ^1.0.2
    is-buffer: ^2.0.5
    lodash: ^4.17.15
    lodash.eq: ^4.0.0
    lodash.indexof: ^4.0.5
    long: ^5.2.0
    reflect-metadata: ^0.1.13
    seedrandom: ^3.0.5
    xxhashjs: ^0.2.2
  checksum: 920d72607780dcbee4fb5e90a26f725b8626c637a841439a4e06ec9ce724248d2facfd5cb8610a38245e3f7c54002c89fc457bb4b9cc3713abc659bfe585d266
  languageName: node
  linkType: hard

"bottleneck@npm:^2.15.3":
  version: 2.19.5
  resolution: "bottleneck@npm:2.19.5"
  checksum: c5eef1bbea12cef1f1405e7306e7d24860568b0f7ac5eeab706a86762b3fc65ef6d1c641c8a166e4db90f412fc5c948fc5ce8008a8cd3d28c7212ef9c3482bda
  languageName: node
  linkType: hard

"bplist-parser@npm:^0.2.0":
  version: 0.2.0
  resolution: "bplist-parser@npm:0.2.0"
  dependencies:
    big-integer: ^1.6.44
  checksum: d5339dd16afc51de6c88f88f58a45b72ed6a06aa31f5557d09877575f220b7c1d3fbe375da0b62e6a10d4b8ed80523567e351f24014f5bc886ad523758142cdd
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: 12cb6d6310629e3048cadb003e1aca4d8c9bb5c67c3c321bafdd7e7a50155de081f78ea3e0ed92ecc75a9015e784f301efc8132383132f4f7904ad1ac529c562
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: ^1.0.0
  checksum: 01dff195e3646bc4b0d27b63d9bab84d2ebc06121ff5013ad6e5356daa5a9d6b60fa26cf73c74797f2dc3fbec112af13578d51f75228c1112b26c790a87b0488
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.21.9":
  version: 4.21.10
  resolution: "browserslist@npm:4.21.10"
  dependencies:
    caniuse-lite: ^1.0.30001517
    electron-to-chromium: ^1.4.477
    node-releases: ^2.0.13
    update-browserslist-db: ^1.0.11
  bin:
    browserslist: cli.js
  checksum: 1e27c0f111a35d1dd0e8fc2c61781b0daefabc2c9471b0b10537ce54843014bceb2a1ce4571af1a82b2bf1e6e6e05d38865916689a158f03bc2c7a4ec2577db8
  languageName: node
  linkType: hard

"bundle-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "bundle-name@npm:3.0.0"
  dependencies:
    run-applescript: ^5.0.0
  checksum: edf2b1fbe6096ed32e7566947ace2ea937ee427391744d7510a2880c4b9a5b3543d3f6c551236a29e5c87d3195f8e2912516290e638c15bcbede7b37cc375615
  languageName: node
  linkType: hard

"busboy@npm:1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: ^1.1.0
  checksum: 32801e2c0164e12106bf236291a00795c3c4e4b709ae02132883fe8478ba2ae23743b11c5735a0aae8afe65ac4b6ca4568b91f0d9fed1fdbc32ede824a73746e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.0, cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0, call-bind@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind@npm:1.0.2"
  dependencies:
    function-bind: ^1.1.1
    get-intrinsic: ^1.0.2
  checksum: f8e31de9d19988a4b80f3e704788c4a2d6b6f3d17cfec4f57dc29ced450c53a49270dc66bf0fbd693329ee948dd33e6c90a329519aef17474a4d961e8d6426b0
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase-keys@npm:^6.2.2":
  version: 6.2.2
  resolution: "camelcase-keys@npm:6.2.2"
  dependencies:
    camelcase: ^5.3.1
    map-obj: ^4.0.0
    quick-lru: ^4.0.1
  checksum: 43c9af1adf840471e54c68ab3e5fe8a62719a6b7dbf4e2e86886b7b0ff96112c945736342b837bd2529ec9d1c7d1934e5653318478d98e0cf22c475c04658e2a
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001517, caniuse-lite@npm:^1.0.30001579":
  version: 1.0.30001707
  resolution: "caniuse-lite@npm:1.0.30001707"
  checksum: 38824c9f88d754428844e64ba18197c06f4f8503035e30eace88c6bffdcf5f682dcf3cef895b60cd6f19c71e6714731adc1940b612ea606c6875cd2f801e4836
  languageName: node
  linkType: hard

"ccount@npm:^1.0.0":
  version: 1.1.0
  resolution: "ccount@npm:1.1.0"
  checksum: b335a79d0aa4308919cf7507babcfa04ac63d389ebed49dbf26990d4607c8a4713cde93cc83e707d84571ddfe1e7615dad248be9bc422ae4c188210f71b08b78
  languageName: node
  linkType: hard

"chalk@npm:^2.3.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chalk@npm:^5.3.0":
  version: 5.4.1
  resolution: "chalk@npm:5.4.1"
  checksum: 0c656f30b782fed4d99198825c0860158901f449a6b12b818b0aabad27ec970389e7e8767d0e00762175b23620c812e70c4fd92c0210e55fc2d993638b74e86e
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: b563e4b6039b15213114626621e7a3d12f31008bdce20f9c741d69987f62aeaace7ec30f6018890ad77b2e9b4d95324c9f5acfca58a9441e3b1dcdd1e2525d17
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^1.0.0":
  version: 1.1.4
  resolution: "character-entities-legacy@npm:1.1.4"
  checksum: fe03a82c154414da3a0c8ab3188e4237ec68006cbcd681cf23c7cfb9502a0e76cd30ab69a2e50857ca10d984d57de3b307680fff5328ccd427f400e559c3a811
  languageName: node
  linkType: hard

"character-entities@npm:^1.0.0":
  version: 1.2.4
  resolution: "character-entities@npm:1.2.4"
  checksum: e1545716571ead57beac008433c1ff69517cd8ca5b336889321c5b8ff4a99c29b65589a701e9c086cda8a5e346a67295e2684f6c7ea96819fe85cbf49bf8686d
  languageName: node
  linkType: hard

"character-reference-invalid@npm:^1.0.0":
  version: 1.1.4
  resolution: "character-reference-invalid@npm:1.1.4"
  checksum: 20274574c70e05e2f81135f3b93285536bc8ff70f37f0809b0d17791a832838f1e49938382899ed4cb444e5bbd4314ca1415231344ba29f4222ce2ccf24fea0b
  languageName: node
  linkType: hard

"chownr@npm:^1.1.3":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: 115648f8eb38bac5e41c3857f3e663f9c39ed6480d1349977c4d96c95a47266fcacc5a5aabf3cb6c481e22d72f41992827db47301851766c4fd77ac21a4f081d
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"ci-info@npm:^4.0.0, ci-info@npm:^4.1.0":
  version: 4.1.0
  resolution: "ci-info@npm:4.1.0"
  checksum: dcf286abdc1bb1c4218b91e4a617b49781b282282089b7188e1417397ea00c6b967848e2360fb9a6b10021bf18a627f20ef698f47c2c9c875aeffd1d2ea51d1e
  languageName: node
  linkType: hard

"cidr-regex@npm:^4.1.1":
  version: 4.1.1
  resolution: "cidr-regex@npm:4.1.1"
  dependencies:
    ip-regex: ^5.0.0
  checksum: 161fd1efb06a53b0fad1afbaa4b9f42c5fd10118da99f5d442e990acc99491406be5a397e9301d1a6cd2106b2aa37d72520cec9cae02f8d66500291dd5a91fe5
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"clean-stack@npm:^5.2.0":
  version: 5.2.0
  resolution: "clean-stack@npm:5.2.0"
  dependencies:
    escape-string-regexp: 5.0.0
  checksum: 9b16c9d56ef673b1666030d04afc5a382c7ec6b5fb8df2dd361090c3ac79273695d6db9867938bb3268903dcebf401e2c6034b2f56f27673f6032b5e89217b81
  languageName: node
  linkType: hard

"cli-columns@npm:^4.0.0":
  version: 4.0.0
  resolution: "cli-columns@npm:4.0.0"
  dependencies:
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
  checksum: fa1a3a7f4e8f26a18e47969c248a2b9a016391bca2588abbe77026255390bee71dc9b7b876f317f46e40164c3c5200972e77ec58b823a05154f26e81a74a54c3
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: ^3.1.0
  checksum: 2692784c6cd2fd85cfdbd11f53aea73a463a6d64a77c3e098b2b4697a20443f430c220629e1ca3b195ea5ac4a97a74c2ee411f3807abf6df2b66211fec0c0a29
  languageName: node
  linkType: hard

"cli-highlight@npm:^2.1.11":
  version: 2.1.11
  resolution: "cli-highlight@npm:2.1.11"
  dependencies:
    chalk: ^4.0.0
    highlight.js: ^10.7.1
    mz: ^2.4.0
    parse5: ^5.1.1
    parse5-htmlparser2-tree-adapter: ^6.0.0
    yargs: ^16.0.0
  bin:
    highlight: bin/highlight
  checksum: 0a60e60545e39efea78c1732a25b91692017ec40fb6e9497208dc0eeeae69991d3923a8d6e4edd0543db3c395ed14529a33dd4d0353f1679c5b6dded792a8496
  languageName: node
  linkType: hard

"cli-table3@npm:^0.6.5":
  version: 0.6.5
  resolution: "cli-table3@npm:0.6.5"
  dependencies:
    "@colors/colors": 1.5.0
    string-width: ^4.2.0
  dependenciesMeta:
    "@colors/colors":
      optional: true
  checksum: ab7afbf4f8597f1c631f3ee6bb3481d0bfeac8a3b81cffb5a578f145df5c88003b6cfff46046a7acae86596fdd03db382bfa67f20973b6b57425505abc47e42c
  languageName: node
  linkType: hard

"cli-truncate@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-truncate@npm:2.1.0"
  dependencies:
    slice-ansi: ^3.0.0
    string-width: ^4.2.0
  checksum: bf1e4e6195392dc718bf9cd71f317b6300dc4a9191d052f31046b8773230ece4fa09458813bf0e3455a5e68c0690d2ea2c197d14a8b85a7b5e01c97f4b5feb5d
  languageName: node
  linkType: hard

"cli-truncate@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-truncate@npm:3.1.0"
  dependencies:
    slice-ansi: ^5.0.0
    string-width: ^5.0.0
  checksum: c3243e41974445691c63f8b405df1d5a24049dc33d324fe448dc572e561a7b772ae982692900b1a5960901cc4fc7def25a629b9c69a4208ee89d12ab3332617a
  languageName: node
  linkType: hard

"client-only@npm:0.0.1, client-only@npm:^0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 0c16bf660dadb90610553c1d8946a7fdfb81d624adea073b8440b7d795d5b5b08beb3c950c6a2cf16279365a3265158a236876d92bce16423c485c322d7dfaf8
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.0
    wrap-ansi: ^7.0.0
  checksum: ce2e8f578a4813806788ac399b9e866297740eecd4ad1823c27fd344d78b22c5f8597d548adbcc46f0573e43e21e751f39446c5a5e804a12aace402b7a315d7f
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"cluster-key-slot@npm:^1.1.0":
  version: 1.1.2
  resolution: "cluster-key-slot@npm:1.1.2"
  checksum: be0ad2d262502adc998597e83f9ded1b80f827f0452127c5a37b22dfca36bab8edf393f7b25bb626006fb9fb2436106939ede6d2d6ecf4229b96a47f27edd681
  languageName: node
  linkType: hard

"cmd-shim@npm:^7.0.0":
  version: 7.0.0
  resolution: "cmd-shim@npm:7.0.0"
  checksum: 4cf622d175b505aff1f8a9ad26164022cfb5599c88a7d0f4b443b78a45945b0950ff6898a854bdefdf5c3155f84e862e2502756a1a83115b0d1d40825be30e96
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: c13fe7cff7885f603f49105827d621ce87f4571d78ba28ef4a3f1a104304748f620615e6bf065ecd2145d0d9dad83a3553f52bb25ede7239d18e9f81622f1cc5
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: ^2.0.1
    color-string: ^1.9.0
  checksum: 0579629c02c631b426780038da929cca8e8d80a40158b09811a0112a107c62e10e4aad719843b791b1e658ab4e800558f2e87ca4522c8b32349d497ecb6adeb4
  languageName: node
  linkType: hard

"colorette@npm:^2.0.16":
  version: 2.0.16
  resolution: "colorette@npm:2.0.16"
  checksum: cd55596a3a2d1071c1a28eee7fd8a5387593ff1bd10a3e8d0a6221499311fe34a9f2b9272d77c391e0e003dcdc8934fb2f8d106e7ef1f7516f8060c901d41a27
  languageName: node
  linkType: hard

"commander@npm:^3.0.2":
  version: 3.0.2
  resolution: "commander@npm:3.0.2"
  checksum: 6d14ad030d1904428139487ed31febcb04c1604db2b8d9fae711f60ee6718828dc0e11602249e91c8a97b0e721e9c6d53edbc166bad3cde1596851d59a8f824d
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 0f82321821fc27b83bd409510bb9deeebcfa799ff0bf5d102128b500b7af22872c0c92cb6a0ebc5a4cf19c6b550fba9cedfa7329d18c6442a625f851377bacf0
  languageName: node
  linkType: hard

"common-ancestor-path@npm:^1.0.1":
  version: 1.0.1
  resolution: "common-ancestor-path@npm:1.0.1"
  checksum: 1d2e4186067083d8cc413f00fc2908225f04ae4e19417ded67faa6494fb313c4fcd5b28a52326d1a62b466e2b3a4325e92c31133c5fee628cdf8856b3a57c3d7
  languageName: node
  linkType: hard

"compare-func@npm:^2.0.0":
  version: 2.0.0
  resolution: "compare-func@npm:2.0.0"
  dependencies:
    array-ify: ^1.0.0
    dot-prop: ^5.1.0
  checksum: fb71d70632baa1e93283cf9d80f30ac97f003aabee026e0b4426c9716678079ef5fea7519b84d012cbed938c476493866a38a79760564a9e21ae9433e40e6f0d
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"config-chain@npm:^1.1.11":
  version: 1.1.13
  resolution: "config-chain@npm:1.1.13"
  dependencies:
    ini: ^1.3.4
    proto-list: ~1.2.1
  checksum: 828137a28e7c2fc4b7fb229bd0cd6c1397bcf83434de54347e608154008f411749041ee392cbe42fab6307e02de4c12480260bf769b7d44b778fdea3839eafab
  languageName: node
  linkType: hard

"conventional-changelog-angular@npm:^5.0.11":
  version: 5.0.13
  resolution: "conventional-changelog-angular@npm:5.0.13"
  dependencies:
    compare-func: ^2.0.0
    q: ^1.5.1
  checksum: 6ed4972fce25a50f9f038c749cc9db501363131b0fb2efc1fccecba14e4b1c80651d0d758d4c350a609f32010c66fa343eefd49c02e79e911884be28f53f3f90
  languageName: node
  linkType: hard

"conventional-changelog-angular@npm:^8.0.0":
  version: 8.0.0
  resolution: "conventional-changelog-angular@npm:8.0.0"
  dependencies:
    compare-func: ^2.0.0
  checksum: 71f492cb4dccd46174430517177054be2e2097f1264c55419a79aa94fe4d163f98aeab7da6836473470fbfc920051a9554f46498989bdd6438648c2d7e32b42c
  languageName: node
  linkType: hard

"conventional-changelog-conventionalcommits@npm:^4.3.1":
  version: 4.6.3
  resolution: "conventional-changelog-conventionalcommits@npm:4.6.3"
  dependencies:
    compare-func: ^2.0.0
    lodash: ^4.17.15
    q: ^1.5.1
  checksum: 7b8e8a21ebb56f9aaa510e12917b7c609202072c3e71089e0a09630c37c2e8146cdb04364809839b0e3eb55f807fe84d03b2079500b37f6186d505848be5c562
  languageName: node
  linkType: hard

"conventional-changelog-writer@npm:^8.0.0":
  version: 8.0.0
  resolution: "conventional-changelog-writer@npm:8.0.0"
  dependencies:
    "@types/semver": ^7.5.5
    conventional-commits-filter: ^5.0.0
    handlebars: ^4.7.7
    meow: ^13.0.0
    semver: ^7.5.2
  bin:
    conventional-changelog-writer: dist/cli/index.js
  checksum: 6dd41a2b2c851ac387bb2570bbeecc41cd2d947da232f699becd430079f474e405cc192610e82f4bb50b2a3b83ea25717ac91fef11410b17d288215d90d3bcec
  languageName: node
  linkType: hard

"conventional-commits-filter@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-commits-filter@npm:5.0.0"
  checksum: 2345546ea9e40412558d508311d7729b38f8d4c0fd554837c10721a432e8598ec1152320f6b601a9c11c023a31bccbb5a12067736b2227de8591f4de707e11a7
  languageName: node
  linkType: hard

"conventional-commits-parser@npm:^3.2.2":
  version: 3.2.4
  resolution: "conventional-commits-parser@npm:3.2.4"
  dependencies:
    JSONStream: ^1.0.4
    is-text-path: ^1.0.1
    lodash: ^4.17.15
    meow: ^8.0.0
    split2: ^3.0.0
    through2: ^4.0.0
  bin:
    conventional-commits-parser: cli.js
  checksum: 1627ff203bc9586d89e47a7fe63acecf339aba74903b9114e23d28094f79d4e2d6389bf146ae561461dcba8fc42e7bc228165d2b173f15756c43f1d32bc50bfd
  languageName: node
  linkType: hard

"conventional-commits-parser@npm:^6.0.0":
  version: 6.0.0
  resolution: "conventional-commits-parser@npm:6.0.0"
  dependencies:
    meow: ^13.0.0
  bin:
    conventional-commits-parser: dist/cli/index.js
  checksum: 2331cb4559f610828857c353adec942cebe3f5ba7d050ad3b98406933593c42b48b407e95738ab7cafee2240c945495bb04fa26bbf6982fcbe8f0efd90fc6949
  languageName: node
  linkType: hard

"convert-hrtime@npm:^5.0.0":
  version: 5.0.0
  resolution: "convert-hrtime@npm:5.0.0"
  checksum: 5245ad1ac6dd57b2d87624ae0eeac1d2a74812a6631208c09368bef787a28e7dbfa736cddaa9c8a0c425cb240437ea506afec7b9684ff617004d06a551f26c87
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.7.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: dc55a1f28ddd0e9485ef13565f8f756b342f9a46c4ae18b843fe3c30c675d058d6a4823eff86d472f187b176f0adf51ea7b69ea38be34be4a63cbbf91b0593c8
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig-typescript-loader@npm:^1.0.0":
  version: 1.0.5
  resolution: "cosmiconfig-typescript-loader@npm:1.0.5"
  dependencies:
    cosmiconfig: ^7
    ts-node: ^10.5.0
  peerDependencies:
    "@types/node": "*"
    cosmiconfig: ">=7"
    typescript: ">=3"
  checksum: 5c9f87e195fb3408407e8ad27851360edeeff29412ec1ef287906ef8fd4ace69b25b34a86cbac9842cadf55bc836ae541b3f170ff3303876f475ca13e2275377
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7, cosmiconfig@npm:^7.0.0":
  version: 7.0.1
  resolution: "cosmiconfig@npm:7.0.1"
  dependencies:
    "@types/parse-json": ^4.0.0
    import-fresh: ^3.2.1
    parse-json: ^5.0.0
    path-type: ^4.0.0
    yaml: ^1.10.0
  checksum: 4be63e7117955fd88333d7460e4c466a90f556df6ef34efd59034d2463484e339666c41f02b523d574a797ec61f4a91918c5b89a316db2ea2f834e0d2d09465b
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.1.0":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
    path-type: ^4.0.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dc339ebea427898c9e03bf01b56ba7afbac07fc7d2a2d5a15d6e9c14de98275a9565da949375aee1809591c152c0a3877bb86dbeaf74d5bd5aaa79955ad9e7a0
  languageName: node
  linkType: hard

"cosmiconfig@npm:^9.0.0":
  version: 9.0.0
  resolution: "cosmiconfig@npm:9.0.0"
  dependencies:
    env-paths: ^2.2.1
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: a30c424b53d442ea0bdd24cb1b3d0d8687c8dda4a17ab6afcdc439f8964438801619cdb66e8e79f63b9caa3e6586b60d8bab9ce203e72df6c5e80179b971fe8f
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: a9a1503d4390d8b59ad86f4607de7870b39cad43d929813599a23714831e81c520bddf61bcdd1f8e30f05fd3a2b71ae8538e946eb2786dc65c2bbc520f692eff
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 0283879f55e7c16fdceacc181f87a0a65c53bc16ffe1d58b9d19a6277adcd71900d02bb2c4843dd55e78c51e30e89b0fec618a7f170ebcc95b33182c28f05fd6
  languageName: node
  linkType: hard

"crypto-random-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "crypto-random-string@npm:4.0.0"
  dependencies:
    type-fest: ^1.0.1
  checksum: 91f148f27bcc8582798f0fb3e75a09d9174557f39c3c40a89dd1bd70fb5a14a02548245aa26fa7d663c426ac5026f4729841231c84f9e30e8c8ece5e38656741
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.0
  resolution: "csstype@npm:3.1.0"
  checksum: 644e986cefab86525f0b674a06889cfdbb1f117e5b7d1ce0fc55b0423ecc58807a1ea42ecc75c4f18999d14fc42d1d255f84662a45003a52bb5840e977eb2ffd
  languageName: node
  linkType: hard

"cuint@npm:^0.2.2":
  version: 0.2.2
  resolution: "cuint@npm:0.2.2"
  checksum: b8127a93a7f16ce120ffcb22108014327c9808b258ee20e7dbb4c6740d7cb0f0c12d18a054eb716b0f2470090666abaae8a082d3cd5ef0e94fa447dd155842c4
  languageName: node
  linkType: hard

"dargs@npm:^7.0.0":
  version: 7.0.0
  resolution: "dargs@npm:7.0.0"
  checksum: b8f1e3cba59c42e1f13a114ad4848c3fc1cf7470f633ee9e9f1043762429bc97d91ae31b826fb135eefde203a3fdb20deb0c0a0222ac29d937b8046085d668d1
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.0, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4, debug@npm:^4.3.6":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: fb42df878dd0e22816fc56e1fdca9da73caa85212fbe40c868b1295a6878f9101ae684f4eeef516c13acfc700f5ea07f1136954f43d4cd2d477a811144136479
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"decamelize-keys@npm:^1.1.0":
  version: 1.1.0
  resolution: "decamelize-keys@npm:1.1.0"
  dependencies:
    decamelize: ^1.1.0
    map-obj: ^1.0.0
  checksum: 8bc5d32e035a072f5dffc1f1f3d26ca7ab1fb44a9cade34c97ab6cd1e62c81a87e718101e96de07d78cecda20a3fdb955df958e46671ccad01bb8dcf0de2e298
  languageName: node
  linkType: hard

"decamelize@npm:^1.1.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"default-browser-id@npm:^3.0.0":
  version: 3.0.0
  resolution: "default-browser-id@npm:3.0.0"
  dependencies:
    bplist-parser: ^0.2.0
    untildify: ^4.0.0
  checksum: 279c7ad492542e5556336b6c254a4eaf31b2c63a5433265655ae6e47301197b6cfb15c595a6fdc6463b2ff8e1a1a1ed3cba56038a60e1527ba4ab1628c6b9941
  languageName: node
  linkType: hard

"default-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "default-browser@npm:4.0.0"
  dependencies:
    bundle-name: ^3.0.0
    default-browser-id: ^3.0.0
    execa: ^7.1.1
    titleize: ^3.0.0
  checksum: 40c5af984799042b140300be5639c9742599bda76dc9eba5ac9ad5943c83dd36cebc4471eafcfddf8e0ec817166d5ba89d56f08e66a126c7c7908a179cead1a7
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: 54884f94caac0791bf6395a3ec530ce901cf71c47b0196b8754f3fd17edb6c0e80149c1214429d851873bb0d689dbe08dcedbb2306dc45c8534a5934723851b6
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.1.4, define-properties@npm:^1.2.0":
  version: 1.2.0
  resolution: "define-properties@npm:1.2.0"
  dependencies:
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: e60aee6a19b102df4e2b1f301816804e81ab48bb91f00d0d935f269bf4b3f79c88b39e4f89eaa132890d23267335fd1140dfcd8d5ccd61031a0a2c41a54e33a6
  languageName: node
  linkType: hard

"del@npm:^6.0.0":
  version: 6.0.0
  resolution: "del@npm:6.0.0"
  dependencies:
    globby: ^11.0.1
    graceful-fs: ^4.2.4
    is-glob: ^4.0.1
    is-path-cwd: ^2.2.0
    is-path-inside: ^3.0.2
    p-map: ^4.0.0
    rimraf: ^3.0.2
    slash: ^3.0.0
  checksum: 5742891627e91aaf62385714025233f4664da28bc55b6ab825649dcdea4691fed3cf329a2b1913fd2d2612e693e99e08a03c84cac7f36ef54bacac9390520192
  languageName: node
  linkType: hard

"denque@npm:^1.1.0":
  version: 1.5.1
  resolution: "denque@npm:1.5.1"
  checksum: 4375ad19d5cea99f90effa82a8cecdaa10f4eb261fbcd7e47cd753ff2737f037aac8f7f4e031cc77f3966314c491c86a0d3b20c128aeee57f791b4662c45108e
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.3":
  version: 2.0.3
  resolution: "detect-libc@npm:2.0.3"
  checksum: 2ba6a939ae55f189aea996ac67afceb650413c7a34726ee92c40fb0deb2400d57ef94631a8a3f052055eea7efb0f99a9b5e6ce923415daa3e68221f963cfc27d
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: f2c09b0ce4e6b301c221addd83bf3f454c0bc00caa3dd837cf6c127d6edf7223aa2bbe3b688feea110b7f262adbfc845b757c44c8a9f8c0c5b15d8fa9ce9d20d
  languageName: node
  linkType: hard

"diff@npm:^5.1.0":
  version: 5.2.0
  resolution: "diff@npm:5.2.0"
  checksum: 12b63ca9c36c72bafa3effa77121f0581b4015df18bc16bac1f8e263597735649f1a173c26f7eba17fb4162b073fee61788abe49610e6c70a2641fe1895443fd
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.0, dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dot-prop@npm:^5.1.0":
  version: 5.3.0
  resolution: "dot-prop@npm:5.3.0"
  dependencies:
    is-obj: ^2.0.0
  checksum: d5775790093c234ef4bfd5fbe40884ff7e6c87573e5339432870616331189f7f5d86575c5b5af2dcf0f61172990f4f734d07844b1f23482fff09e3c4bead05ea
  languageName: node
  linkType: hard

"duplexer2@npm:~0.1.0":
  version: 0.1.4
  resolution: "duplexer2@npm:0.1.4"
  dependencies:
    readable-stream: ^2.0.2
  checksum: 744961f03c7f54313f90555ac20284a3fb7bf22fdff6538f041a86c22499560eb6eac9d30ab5768054137cb40e6b18b40f621094e0261d7d8c35a37b7a5ad241
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.477":
  version: 1.4.479
  resolution: "electron-to-chromium@npm:1.4.479"
  checksum: a22b70435e163bf413bfb8f4da015c3f099b64fc1eebb0c39829c5ce66ee13a23a2d42c91a33524eb4d1e86e28cc046be940e4b78d74e43b54d883100711f7cd
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"emojilib@npm:^2.4.0":
  version: 2.4.0
  resolution: "emojilib@npm:2.4.0"
  checksum: ea241c342abda5a86ffd3a15d8f4871a616d485f700e03daea38c6ce38205847cea9f6ff8d5e962c00516b004949cc96c6e37b05559ea71a0a496faba53b56da
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.12.0":
  version: 5.15.0
  resolution: "enhanced-resolve@npm:5.15.0"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: fbd8cdc9263be71cc737aa8a7d6c57b43d6aa38f6cc75dde6fcd3598a130cc465f979d2f4d01bb3bf475acb43817749c79f8eef9be048683602ca91ab52e4f11
  languageName: node
  linkType: hard

"env-ci@npm:^11.0.0":
  version: 11.1.0
  resolution: "env-ci@npm:11.1.0"
  dependencies:
    execa: ^8.0.0
    java-properties: ^1.0.2
  checksum: ff72391694e7f9d8e44c5123ed1c9c50b64a4f6109b76db471380b04abe36cf8e213d8809f5a54313abfb086e0cfd2ede65f582d5b78959977c7df4a379a2ef9
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0, env-paths@npm:^2.2.1":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"environment@npm:^1.0.0":
  version: 1.1.0
  resolution: "environment@npm:1.1.0"
  checksum: dd3c1b9825e7f71f1e72b03c2344799ac73f2e9ef81b78ea8b373e55db021786c6b9f3858ea43a436a2c4611052670ec0afe85bc029c384cc71165feee2f4ba6
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1, error-ex@npm:^1.3.2":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"es-abstract@npm:^1.19.0, es-abstract@npm:^1.19.2, es-abstract@npm:^1.20.4, es-abstract@npm:^1.21.2":
  version: 1.22.1
  resolution: "es-abstract@npm:1.22.1"
  dependencies:
    array-buffer-byte-length: ^1.0.0
    arraybuffer.prototype.slice: ^1.0.1
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    es-set-tostringtag: ^2.0.1
    es-to-primitive: ^1.2.1
    function.prototype.name: ^1.1.5
    get-intrinsic: ^1.2.1
    get-symbol-description: ^1.0.0
    globalthis: ^1.0.3
    gopd: ^1.0.1
    has: ^1.0.3
    has-property-descriptors: ^1.0.0
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
    internal-slot: ^1.0.5
    is-array-buffer: ^3.0.2
    is-callable: ^1.2.7
    is-negative-zero: ^2.0.2
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.2
    is-string: ^1.0.7
    is-typed-array: ^1.1.10
    is-weakref: ^1.0.2
    object-inspect: ^1.12.3
    object-keys: ^1.1.1
    object.assign: ^4.1.4
    regexp.prototype.flags: ^1.5.0
    safe-array-concat: ^1.0.0
    safe-regex-test: ^1.0.0
    string.prototype.trim: ^1.2.7
    string.prototype.trimend: ^1.0.6
    string.prototype.trimstart: ^1.0.6
    typed-array-buffer: ^1.0.0
    typed-array-byte-length: ^1.0.0
    typed-array-byte-offset: ^1.0.0
    typed-array-length: ^1.0.4
    unbox-primitive: ^1.0.2
    which-typed-array: ^1.1.10
  checksum: 614e2c1c3717cb8d30b6128ef12ea110e06fd7d75ad77091ca1c5dbfb00da130e62e4bbbbbdda190eada098a22b27fe0f99ae5a1171dac2c8663b1e8be8a3a9b
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.1":
  version: 2.0.1
  resolution: "es-set-tostringtag@npm:2.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
    has: ^1.0.3
    has-tostringtag: ^1.0.0
  checksum: ec416a12948cefb4b2a5932e62093a7cf36ddc3efd58d6c58ca7ae7064475ace556434b869b0bbeb0c365f1032a8ccd577211101234b69837ad83ad204fff884
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-shim-unscopables@npm:1.0.0"
  dependencies:
    has: ^1.0.3
  checksum: 83e95cadbb6ee44d3644dfad60dcad7929edbc42c85e66c3e99aefd68a3a5c5665f2686885cddb47dfeabfd77bd5ea5a7060f2092a955a729bbd8834f0d86fa1
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-to-primitive@npm:1.2.1"
  dependencies:
    is-callable: ^1.1.4
    is-date-object: ^1.0.1
    is-symbol: ^1.0.2
  checksum: 4ead6671a2c1402619bdd77f3503991232ca15e17e46222b0a41a5d81aebc8740a77822f5b3c965008e631153e9ef0580540007744521e72de8e33599fca2eed
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: a3e2a99f07acb74b3ad4989c48ca0c3140f69f923e56d0cba0526240ee470b91010f9d39001f2a4a313841d237ede70a729e92125191ba5d21e74b106800b133
  languageName: node
  linkType: hard

"escape-string-regexp@npm:5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 20daabe197f3cb198ec28546deebcf24b3dbb1a5a269184381b3116d12f0532e06007f4bc8da25669d6a7f8efb68db0758df4cd981f57bc5b57f521a3e12c59e
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^8.8.0":
  version: 8.9.0
  resolution: "eslint-config-prettier@npm:8.9.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: a675d0dabd76b700ef2d062b5ec6a634e105a8e8c070f95281fd2ccb614527fac60b4c758132058c50f0521fd19313f1f5be45ce9ebf081f2e5f77ae6eb7d8db
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.7":
  version: 0.3.7
  resolution: "eslint-import-resolver-node@npm:0.3.7"
  dependencies:
    debug: ^3.2.7
    is-core-module: ^2.11.0
    resolve: ^1.22.1
  checksum: 3379aacf1d2c6952c1b9666c6fa5982c3023df695430b0d391c0029f6403a7775414873d90f397e98ba6245372b6c8960e16e74d9e4a3b0c0a4582f3bdbe3d6e
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:^3.5.5":
  version: 3.5.5
  resolution: "eslint-import-resolver-typescript@npm:3.5.5"
  dependencies:
    debug: ^4.3.4
    enhanced-resolve: ^5.12.0
    eslint-module-utils: ^2.7.4
    get-tsconfig: ^4.5.0
    globby: ^13.1.3
    is-core-module: ^2.11.0
    is-glob: ^4.0.3
    synckit: ^0.8.5
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
  checksum: 27e6276fdff5d377c9036362ff736ac29852106e883ff589ea9092dc57d4bc2a67a82d75134221124f05045f9a7e2114a159b2c827d1f9f64d091f7afeab0f58
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.7.4, eslint-module-utils@npm:^2.8.0":
  version: 2.8.0
  resolution: "eslint-module-utils@npm:2.8.0"
  dependencies:
    debug: ^3.2.7
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 74c6dfea7641ebcfe174be61168541a11a14aa8d72e515f5f09af55cd0d0862686104b0524aa4b8e0ce66418a44aa38a94d2588743db5fd07a6b49ffd16921d2
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.27.5":
  version: 2.28.0
  resolution: "eslint-plugin-import@npm:2.28.0"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.findlastindex: ^1.2.2
    array.prototype.flat: ^1.3.1
    array.prototype.flatmap: ^1.3.1
    debug: ^3.2.7
    doctrine: ^2.1.0
    eslint-import-resolver-node: ^0.3.7
    eslint-module-utils: ^2.8.0
    has: ^1.0.3
    is-core-module: ^2.12.1
    is-glob: ^4.0.3
    minimatch: ^3.1.2
    object.fromentries: ^2.0.6
    object.groupby: ^1.0.0
    object.values: ^1.1.6
    resolve: ^1.22.3
    semver: ^6.3.1
    tsconfig-paths: ^3.14.2
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
  checksum: f9eba311b93ca1bb89311856b1f7285bd79e0181d7eb70fe115053ff77e2235fea749b30f538b78927dc65769340b5be61f4c9581d1c82bcdcccb2061f440ad1
  languageName: node
  linkType: hard

"eslint-plugin-next@npm:^0.0.0":
  version: 0.0.0
  resolution: "eslint-plugin-next@npm:0.0.0"
  checksum: c083aa1c42df347d7940b07916493c0be6f30c5df9c87200827f71c574958d3e1362e1042fd15f9164e63b673cb7ca94491ea3e8318fc1dcbe4f686e9d9166d5
  languageName: node
  linkType: hard

"eslint-plugin-prefer-arrow-functions@npm:^3.1.4":
  version: 3.1.4
  resolution: "eslint-plugin-prefer-arrow-functions@npm:3.1.4"
  peerDependencies:
    eslint: ">=5.0.0"
  checksum: 581f1be4ea056d70aad1866ee16a0def7ecece050e03ff3bc2a6695b8bdca3751d6360fcd0e4efccd0068a0e0557b7ab36586a301afa38957acc8c58d293560c
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.0.0":
  version: 5.0.0
  resolution: "eslint-plugin-prettier@npm:5.0.0"
  dependencies:
    prettier-linter-helpers: ^1.0.0
    synckit: ^0.8.5
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 84e88744b9050f2d5ef31b94e85294dda16f3a53c2449f9d33eac8ae6264889b459bf35a68e438fb6b329c2a1d6491aac4bfa00d86317e7009de3dad0311bec6
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^4.6.0":
  version: 4.6.0
  resolution: "eslint-plugin-react-hooks@npm:4.6.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
  checksum: 23001801f14c1d16bf0a837ca7970d9dd94e7b560384b41db378b49b6e32dc43d6e2790de1bd737a652a86f81a08d6a91f402525061b47719328f586a57e86c3
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.33.0":
  version: 7.33.1
  resolution: "eslint-plugin-react@npm:7.33.1"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flatmap: ^1.3.1
    array.prototype.tosorted: ^1.1.1
    doctrine: ^2.1.0
    estraverse: ^5.3.0
    jsx-ast-utils: ^2.4.1 || ^3.0.0
    minimatch: ^3.1.2
    object.entries: ^1.1.6
    object.fromentries: ^2.0.6
    object.hasown: ^1.1.2
    object.values: ^1.1.6
    prop-types: ^15.8.1
    resolve: ^2.0.0-next.4
    semver: ^6.3.1
    string.prototype.matchall: ^4.0.8
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8
  checksum: 0427bd24acb87422b7298686203167123ba289ba563384983f3d99fad7817eae7f63157fd2e9b868bdcf0760719c319ab1e22a44764a98302034b0c844763e57
  languageName: node
  linkType: hard

"eslint-plugin-sort-destructure-keys@npm:^1.5.0":
  version: 1.5.0
  resolution: "eslint-plugin-sort-destructure-keys@npm:1.5.0"
  dependencies:
    natural-compare-lite: ^1.4.0
  peerDependencies:
    eslint: 3 - 8
  checksum: dafa189d79f6de7c32ae4e100b4d7e40dddf0eee33bd270c78eb8c9b8b81aa48245832d2a891de48c5f0dad8b2bd06b63b1a7994d4b6d8b69f1559351b10e1c0
  languageName: node
  linkType: hard

"eslint-plugin-tsc@npm:^2.0.0":
  version: 2.0.0
  resolution: "eslint-plugin-tsc@npm:2.0.0"
  dependencies:
    typescript-service: ^2.0.3
  checksum: a6483fec0152c9c10542c0e51e36177623c233dc9715228f1f7063d181129084830b4b49f16f922759acb9e711d167e05ed7bc49d88afd21bcb6a24ec82d6369
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: ec97dbf5fb04b94e8f4c5a91a7f0a6dd3c55e46bfc7bbcd0e3138c3a76977570e02ed89a1810c778dcd72072ff0e9621ba1379b4babe53921d71e2e4486fda3e
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^2.1.0":
  version: 2.1.0
  resolution: "eslint-visitor-keys@npm:2.1.0"
  checksum: e3081d7dd2611a35f0388bbdc2f5da60b3a3c5b8b6e928daffff7391146b434d691577aa95064c8b7faad0b8a680266bcda0a42439c18c717b80e6718d7e267d
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.2":
  version: 3.4.2
  resolution: "eslint-visitor-keys@npm:3.4.2"
  checksum: 9e0e7e4aaea705c097ae37c97410e5f167d4d2193be2edcb1f0760762ede3df01545e4820ae314f42dcec687745f2c6dcaf6d83575c4a2a241eb0c8517d724f2
  languageName: node
  linkType: hard

"eslint@npm:^8.45.0, eslint@npm:^8.46.0":
  version: 8.46.0
  resolution: "eslint@npm:8.46.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.6.1
    "@eslint/eslintrc": ^2.1.1
    "@eslint/js": ^8.46.0
    "@humanwhocodes/config-array": ^0.11.10
    "@humanwhocodes/module-importer": ^1.0.1
    "@nodelib/fs.walk": ^1.2.8
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.3.2
    doctrine: ^3.0.0
    escape-string-regexp: ^4.0.0
    eslint-scope: ^7.2.2
    eslint-visitor-keys: ^3.4.2
    espree: ^9.6.1
    esquery: ^1.4.2
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^6.0.1
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    globals: ^13.19.0
    graphemer: ^1.4.0
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    is-path-inside: ^3.0.3
    js-yaml: ^4.1.0
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
    strip-ansi: ^6.0.1
    text-table: ^0.2.0
  bin:
    eslint: bin/eslint.js
  checksum: 7a7d36b1a3bbc12e08fbb5bc36fd482a7a5a1797e62e762499dd45601b9e45aaa53a129f31ce0b4444551a9639b8b681ad535f379893dd1e3ae37b31dccd82aa
  languageName: node
  linkType: hard

"espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: ^8.9.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^3.4.1
  checksum: eb8c149c7a2a77b3f33a5af80c10875c3abd65450f60b8af6db1bfcfa8f101e21c1e56a561c6dc13b848e18148d43469e7cd208506238554fb5395a9ea5a1ab9
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.5.0
  resolution: "esquery@npm:1.5.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: aefb0d2596c230118656cd4ec7532d447333a410a48834d80ea648b1e7b5c9bc9ed8b5e33a89cb04e487b60d622f44cf5713bf4abed7c97343edefdc84a35900
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"eventsource@npm:^1.1.2":
  version: 1.1.2
  resolution: "eventsource@npm:1.1.2"
  checksum: fe8f2ac3c70b1b63ee3cef5c0a28680cb00b5747bfda1d9835695fab3ed602be41c5c799b1fc997b34b02633573fead25b12b036bdf5212f23a6aa9f59212e9b
  languageName: node
  linkType: hard

"execa@npm:^5.0.0, execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: fba9022c8c8c15ed862847e94c252b3d946036d7547af310e344a527e59021fd8b6bb0723883ea87044dc4f0201f949046993124a42ccb0855cae5bf8c786343
  languageName: node
  linkType: hard

"execa@npm:^7.1.1":
  version: 7.2.0
  resolution: "execa@npm:7.2.0"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.1
    human-signals: ^4.3.0
    is-stream: ^3.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^5.1.0
    onetime: ^6.0.0
    signal-exit: ^3.0.7
    strip-final-newline: ^3.0.0
  checksum: 14fd17ba0ca8c87b277584d93b1d9fc24f2a65e5152b31d5eb159a3b814854283eaae5f51efa9525e304447e2f757c691877f7adff8fde5746aae67eb1edd1cc
  languageName: node
  linkType: hard

"execa@npm:^8.0.0, execa@npm:^8.0.1":
  version: 8.0.1
  resolution: "execa@npm:8.0.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^8.0.1
    human-signals: ^5.0.0
    is-stream: ^3.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^5.1.0
    onetime: ^6.0.0
    signal-exit: ^4.1.0
    strip-final-newline: ^3.0.0
  checksum: cac1bf86589d1d9b73bdc5dda65c52012d1a9619c44c526891956745f7b366ca2603d29fe3f7460bacc2b48c6eab5d6a4f7afe0534b31473d3708d1265545e1f
  languageName: node
  linkType: hard

"execa@npm:^9.0.0":
  version: 9.5.2
  resolution: "execa@npm:9.5.2"
  dependencies:
    "@sindresorhus/merge-streams": ^4.0.0
    cross-spawn: ^7.0.3
    figures: ^6.1.0
    get-stream: ^9.0.0
    human-signals: ^8.0.0
    is-plain-obj: ^4.1.0
    is-stream: ^4.0.1
    npm-run-path: ^6.0.0
    pretty-ms: ^9.0.0
    signal-exit: ^4.1.0
    strip-final-newline: ^4.0.0
    yoctocolors: ^2.0.0
  checksum: dbe18d07ef58d88116c94450e3f8422ad1ce498bdbfed51b0ed0e7c219d7842d2bccab36b1c0d61d58e99a944cf3c5f60ba08e6a3c5dfe51e1d6e84b0b4f82aa
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: a50a8309ca65ea5d426382ff09f33586527882cf532931cb08ca786ea3146c0553310bda688710ff61d7668eba9f96b923fe1420cdf56a2c3eaf30fcab87b515
  languageName: node
  linkType: hard

"fast-content-type-parse@npm:^2.0.0":
  version: 2.0.1
  resolution: "fast-content-type-parse@npm:2.0.1"
  checksum: 0ea4c7dce77c579d19805ea874d128832f535086465c57994a49a28a4784538ea4eeaa49261a5c675a4764c634e12a74bae26e09d64e886cb826c0b97e4c621d
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: d22d371b994fdc8cce9ff510d7b8dc4da70ac327bcba20df607dd5b9cae9f908f4d1028f5fe467650f058d1e7270235ae0b8230809a262b4df587a3b3aa216c3
  languageName: node
  linkType: hard

"fast-glob@npm:^3.0.3, fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.0, fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.8
  checksum: 0704d7b85c0305fd2cef37777337dfa26230fdd072dce9fb5c82a4b03156f3ffb8ed3e636033e65d45d2a5805a4e475825369a27404c0307f2db0c8eb3366fbd
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fastest-levenshtein@npm:^1.0.16":
  version: 1.0.16
  resolution: "fastest-levenshtein@npm:1.0.16"
  checksum: a78d44285c9e2ae2c25f3ef0f8a73f332c1247b7ea7fb4a191e6bb51aa6ee1ef0dfb3ed113616dcdc7023e18e35a8db41f61c8d88988e877cf510df8edafbc71
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.13.0
  resolution: "fastq@npm:1.13.0"
  dependencies:
    reusify: ^1.0.4
  checksum: 32cf15c29afe622af187d12fc9cd93e160a0cb7c31a3bb6ace86b7dea3b28e7b72acde89c882663f307b2184e14782c6c664fa315973c03626c7d4bff070bb0b
  languageName: node
  linkType: hard

"figures@npm:^2.0.0":
  version: 2.0.0
  resolution: "figures@npm:2.0.0"
  dependencies:
    escape-string-regexp: ^1.0.5
  checksum: 081beb16ea57d1716f8447c694f637668322398b57017b20929376aaf5def9823b35245b734cdd87e4832dc96e9c6f46274833cada77bfe15e5f980fea1fd21f
  languageName: node
  linkType: hard

"figures@npm:^6.0.0, figures@npm:^6.1.0":
  version: 6.1.0
  resolution: "figures@npm:6.1.0"
  dependencies:
    is-unicode-supported: ^2.0.0
  checksum: 35c81239d4fa40b75c2c7c010833b0bc8861c27187e4c9388fca1d9731103ec9989b70ee3b664ef426ddd9abe02ec5f4fd973424aa8c6fd3ea5d3bf57a2d01b4
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: ^3.0.4
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-url@npm:^3.0.0":
  version: 3.0.0
  resolution: "file-url@npm:3.0.0"
  checksum: 4724f669ee22468f23a39e37b8349a14f94dd9abda8385920db9900a2b2ae5ad90a314d85ea0089b6f45e9d0850833a6d1e41ac15a81a5618685129c6d7c7629
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"find-up-simple@npm:^1.0.0":
  version: 1.0.0
  resolution: "find-up-simple@npm:1.0.0"
  checksum: 91c3d51c1111b5eb4e6e6d71d21438f6571a37a69dc288d4222b98996756e2f472fa5393a4dddb5e1a84929405d87e86f4bdce798ba84ee513b79854960ec140
  languageName: node
  linkType: hard

"find-up@npm:^2.0.0":
  version: 2.1.0
  resolution: "find-up@npm:2.1.0"
  dependencies:
    locate-path: ^2.0.0
  checksum: 43284fe4da09f89011f08e3c32cd38401e786b19226ea440b75386c1b12a4cb738c94969808d53a84f564ede22f732c8409e3cfc3f7fb5b5c32378ad0bbf28bd
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: ^3.0.0
  checksum: 38eba3fe7a66e4bc7f0f5a1366dc25508b7cfc349f852640e3678d26ad9a6d7e2c43eff0a472287de4a9753ef58f066a0ea892a256fa3636ad51b3fe1e17fae9
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"find-versions@npm:^6.0.0":
  version: 6.0.0
  resolution: "find-versions@npm:6.0.0"
  dependencies:
    semver-regex: ^4.0.5
    super-regex: ^1.0.0
  checksum: d622e711bd17099015506bafd18b13e51fcc54f80ad073cf819ce4598d6b485774f55708ca356235770bed0148ae55a7daf3ef6deb72730c5b1e2f32b432fed5
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.0.4
  resolution: "flat-cache@npm:3.0.4"
  dependencies:
    flatted: ^3.1.0
    rimraf: ^3.0.2
  checksum: 4fdd10ecbcbf7d520f9040dd1340eb5dfe951e6f0ecf2252edeec03ee68d989ec8b9a20f4434270e71bcfd57800dc09b3344fca3966b2eb8f613072c7d9a2365
  languageName: node
  linkType: hard

"flatted@npm:^3.1.0":
  version: 3.2.5
  resolution: "flatted@npm:3.2.5"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 1989698488f725b05b26bc9afc8a08f08ec41807cd7b92ad85d96004ddf8243fd3e79486b8348c64a3011ae5cc2c9f0936af989e1f28339805d8bc178a75b451
  languageName: node
  linkType: hard

"from2@npm:^2.3.0":
  version: 2.3.0
  resolution: "from2@npm:2.3.0"
  dependencies:
    inherits: ^2.0.1
    readable-stream: ^2.0.0
  checksum: 6080eba0793dce32f475141fb3d54cc15f84ee52e420ee22ac3ab0ad639dc95a1875bc6eb9c0e1140e94972a36a89dc5542491b85f1ab8df0c126241e0f1a61b
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0, fs-extra@npm:^10.0.1":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: dc94ab37096f813cc3ca12f0f1b5ad6744dfed9ed21e953d72530d103cea193c2f81584a39e9dee1bea36de5ee66805678c0dddc048e8af1427ac19c00fffc50
  languageName: node
  linkType: hard

"fs-extra@npm:^11.0.0, fs-extra@npm:^11.1.0":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: f983c706e0c22b0c0747a8e9c76aed6f391ba2d76734cf2757cd84da13417b402ed68fe25bace65228856c61d36d3b41da198f1ffbf33d0b34283a2f7a62c6e9
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: ^1.0.0
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: ba71ba32e0faa74ab931b7a0031d1523c66a73e225de7426e275e238e312d07313d2da2d33e34a52aa406c8763ade5712eb3ec9ba4d9edce652bcacdc29e6b20
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0, fs-minipass@npm:^3.0.3":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: b32fbaebb3f8ec4969f033073b43f5c8befbb58f1a79e12f1d7490358150359ebd92f49e72ff0144f65f2c48ea2a605bff2d07965f548f6474fd8efd95bf361a
  languageName: node
  linkType: hard

"function-timeout@npm:^1.0.1":
  version: 1.0.2
  resolution: "function-timeout@npm:1.0.2"
  checksum: 3afedebacaaf237ba9aaef925886fcf5abd434ca12a18c1c7cecb001e57bf9b30434278edcc977a127baeb5b6361f7c278243c1dbf8bf349aa8b30500c57a699
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.5":
  version: 1.1.5
  resolution: "function.prototype.name@npm:1.1.5"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
    es-abstract: ^1.19.0
    functions-have-names: ^1.2.2
  checksum: acd21d733a9b649c2c442f067567743214af5fa248dbeee69d8278ce7df3329ea5abac572be9f7470b4ec1cd4d8f1040e3c5caccf98ebf2bf861a0deab735c27
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.2, functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"fuse.js@npm:^7.0.0":
  version: 7.0.0
  resolution: "fuse.js@npm:7.0.0"
  checksum: d15750efec1808370c0cae92ec9473aa7261c59bca1f15f1cf60039ba6f804b8f95340b5cabd83a4ef55839c1034764856e0128e443921f072aa0d8a20e4cacf
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.1, get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.0, get-intrinsic@npm:^1.2.1":
  version: 1.2.1
  resolution: "get-intrinsic@npm:1.2.1"
  dependencies:
    function-bind: ^1.1.1
    has: ^1.0.3
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
  checksum: 5b61d88552c24b0cf6fa2d1b3bc5459d7306f699de060d76442cce49a4721f52b8c560a33ab392cf5575b7810277d54ded9d4d39a1ea61855619ebc005aa7e5f
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0, get-stream@npm:^6.0.1":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"get-stream@npm:^7.0.0":
  version: 7.0.1
  resolution: "get-stream@npm:7.0.1"
  checksum: 107083c25faf274136a246fa72faea65aa8cea0db54c2dc8c70d3cfe2dcf0d036356927d870dc83fccea8fa32f183ce3696a04eca9617f3e19119f87c5fc0807
  languageName: node
  linkType: hard

"get-stream@npm:^8.0.1":
  version: 8.0.1
  resolution: "get-stream@npm:8.0.1"
  checksum: 01e3d3cf29e1393f05f44d2f00445c5f9ec3d1c49e8179b31795484b9c117f4c695e5e07b88b50785d5c8248a788c85d9913a79266fc77e3ef11f78f10f1b974
  languageName: node
  linkType: hard

"get-stream@npm:^9.0.0":
  version: 9.0.1
  resolution: "get-stream@npm:9.0.1"
  dependencies:
    "@sec-ant/readable-stream": ^0.4.1
    is-stream: ^4.0.1
  checksum: 631df71d7bd60a7f373094d3c352e2ce412b82d30b1b0ec562e5a4aced976173a4cc0dabef019050e1aceaffb1f0e086349ab3d14377b0b7280510bd75bd3e1e
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.0":
  version: 1.0.0
  resolution: "get-symbol-description@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.1
  checksum: 9ceff8fe968f9270a37a1f73bf3f1f7bda69ca80f4f80850670e0e7b9444ff99323f7ac52f96567f8b5f5fbe7ac717a0d81d3407c7313e82810c6199446a5247
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.5.0":
  version: 4.6.2
  resolution: "get-tsconfig@npm:4.6.2"
  dependencies:
    resolve-pkg-maps: ^1.0.0
  checksum: e791e671a9b55e91efea3ca819ecd7a25beae679e31c83234bf3dd62ddd93df070c1b95ae7e29d206358ebb6408f6f79ac6d83a32a3bbd6a6d217babe23de077
  languageName: node
  linkType: hard

"git-log-parser@npm:^1.2.0":
  version: 1.2.0
  resolution: "git-log-parser@npm:1.2.0"
  dependencies:
    argv-formatter: ~1.0.0
    spawn-error-forwarder: ~1.0.0
    split2: ~1.0.0
    stream-combiner2: ~1.1.1
    through2: ~2.0.0
    traverse: ~0.6.6
  checksum: 57294e72f91920d3262ff51fb0fd81dba1465c9e1b25961e19c757ae39bb38e72dd4a5da40649eeb368673b08be449a0844a2bafc0c0ded7375a8a56a6af8640
  languageName: node
  linkType: hard

"git-raw-commits@npm:^2.0.0":
  version: 2.0.11
  resolution: "git-raw-commits@npm:2.0.11"
  dependencies:
    dargs: ^7.0.0
    lodash: ^4.17.15
    meow: ^8.0.0
    split2: ^3.0.0
    through2: ^4.0.0
  bin:
    git-raw-commits: cli.js
  checksum: c178af43633684106179793b6e3473e1d2bb50bb41d04e2e285ea4eef342ca4090fee6bc8a737552fde879d22346c90de5c49f18c719a0f38d4c934f258a0f79
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:7.1.7":
  version: 7.1.7
  resolution: "glob@npm:7.1.7"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: b61f48973bbdcf5159997b0874a2165db572b368b931135832599875919c237fc05c12984e38fe828e69aa8a921eb0e8a4997266211c517c9cfaae8a93988bb8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7, glob@npm:^10.4.5":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"glob@npm:^7.1.3":
  version: 7.2.0
  resolution: "glob@npm:7.2.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 78a8ea942331f08ed2e055cb5b9e40fe6f46f579d7fd3d694f3412fe5db23223d29b7fee1575440202e9a7ff9a72ab106a39fee39934c7bedafe5e5f8ae20134
  languageName: node
  linkType: hard

"global-dirs@npm:^0.1.1":
  version: 0.1.1
  resolution: "global-dirs@npm:0.1.1"
  dependencies:
    ini: ^1.3.4
  checksum: 10624f5a8ddb8634c22804c6b24f93fb591c3639a6bc78e3584e01a238fc6f7b7965824184e57d63f6df36980b6c191484ad7bc6c35a1599b8f1d64be64c2a4a
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.20.0
  resolution: "globals@npm:13.20.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: ad1ecf914bd051325faad281d02ea2c0b1df5d01bd94d368dcc5513340eac41d14b3c61af325768e3c7f8d44576e72780ec0b6f2d366121f8eec6e03c3a3b97a
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.3":
  version: 1.0.3
  resolution: "globalthis@npm:1.0.3"
  dependencies:
    define-properties: ^1.1.3
  checksum: fbd7d760dc464c886d0196166d92e5ffb4c84d0730846d6621a39fbbc068aeeb9c8d1421ad330e94b7bca4bb4ea092f5f21f3d36077812af5d098b4dc006c998
  languageName: node
  linkType: hard

"globby@npm:^10.0.1":
  version: 10.0.2
  resolution: "globby@npm:10.0.2"
  dependencies:
    "@types/glob": ^7.1.1
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.0.3
    glob: ^7.1.3
    ignore: ^5.1.1
    merge2: ^1.2.3
    slash: ^3.0.0
  checksum: 167cd067f2cdc030db2ec43232a1e835fa06217577d545709dbf29fd21631b30ff8258705172069c855dc4d5766c3b2690834e35b936fbff01ad0329fb95a26f
  languageName: node
  linkType: hard

"globby@npm:^11.0.1, globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"globby@npm:^13.1.3":
  version: 13.2.2
  resolution: "globby@npm:13.2.2"
  dependencies:
    dir-glob: ^3.0.1
    fast-glob: ^3.3.0
    ignore: ^5.2.4
    merge2: ^1.4.1
    slash: ^4.0.0
  checksum: f3d84ced58a901b4fcc29c846983108c426631fe47e94872868b65565495f7bee7b3defd68923bd480582771fd4bbe819217803a164a618ad76f1d22f666f41e
  languageName: node
  linkType: hard

"globby@npm:^14.0.0":
  version: 14.0.2
  resolution: "globby@npm:14.0.2"
  dependencies:
    "@sindresorhus/merge-streams": ^2.1.0
    fast-glob: ^3.3.2
    ignore: ^5.2.4
    path-type: ^5.0.0
    slash: ^5.1.0
    unicorn-magic: ^0.1.0
  checksum: 2cee79efefca4383a825fc2fcbdb37e5706728f2d39d4b63851927c128fff62e6334ef7d4d467949d411409ad62767dc2d214e0f837a0f6d4b7290b6711d485c
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
  checksum: a5ccfb8806e0917a94e0b3de2af2ea4979c1da920bc381667c260e00e7cafdbe844e2cb9c5bcfef4e5412e8bf73bab837285bc35c7ba73aaaf0134d4583393a6
  languageName: node
  linkType: hard

"graceful-fs@npm:4.2.10":
  version: 4.2.10
  resolution: "graceful-fs@npm:4.2.10"
  checksum: 3f109d70ae123951905d85032ebeae3c2a5a7a997430df00ea30df0e3a6c60cf6689b109654d6fdacd28810a053348c4d14642da1d075049e6be1ba5216218da
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"handlebars@npm:^4.7.7":
  version: 4.7.7
  resolution: "handlebars@npm:4.7.7"
  dependencies:
    minimist: ^1.2.5
    neo-async: ^2.6.0
    source-map: ^0.6.1
    uglify-js: ^3.1.4
    wordwrap: ^1.0.0
  dependenciesMeta:
    uglify-js:
      optional: true
  bin:
    handlebars: bin/handlebars
  checksum: 1e79a43f5e18d15742977cb987923eab3e2a8f44f2d9d340982bcb69e1735ed049226e534d7c1074eaddaf37e4fb4f471a8adb71cddd5bc8cf3f894241df5cee
  languageName: node
  linkType: hard

"hard-rejection@npm:^2.1.0":
  version: 2.1.0
  resolution: "hard-rejection@npm:2.1.0"
  checksum: 7baaf80a0c7fff4ca79687b4060113f1529589852152fa935e6787a2bc96211e784ad4588fb3048136ff8ffc9dfcf3ae385314a5b24db32de20bea0d1597f9dc
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.1, has-bigints@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-bigints@npm:1.0.2"
  checksum: 390e31e7be7e5c6fe68b81babb73dfc35d413604d7ee5f56da101417027a4b4ce6a27e46eff97ad040c835b5d228676eae99a9b5c3bc0e23c8e81a49241ff45b
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-property-descriptors@npm:1.0.0"
  dependencies:
    get-intrinsic: ^1.1.1
  checksum: a6d3f0a266d0294d972e354782e872e2fe1b6495b321e6ef678c9b7a06a40408a6891817350c62e752adced73a94ac903c54734fee05bf65b1905ee1368194bb
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-proto@npm:1.0.1"
  checksum: febc5b5b531de8022806ad7407935e2135f1cc9e64636c3916c6842bd7995994ca3b29871ecd7954bd35f9e2986c17b3b227880484d22259e2f8e6ce63fd383e
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.2, has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: a054c40c631c0d5741a8285010a0777ea0c068f99ed43e5d6eb12972da223f8af553a455132fdb0801bdcfa0e0f443c0c03a68d8555aa529b3144b446c3f2410
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-tostringtag@npm:1.0.0"
  dependencies:
    has-symbols: ^1.0.2
  checksum: cc12eb28cb6ae22369ebaad3a8ab0799ed61270991be88f208d508076a1e99abe4198c965935ce85ea90b60c94ddda73693b0920b58e7ead048b4a391b502c1c
  languageName: node
  linkType: hard

"has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"highlight.js@npm:^10.7.1":
  version: 10.7.3
  resolution: "highlight.js@npm:10.7.3"
  checksum: defeafcd546b535d710d8efb8e650af9e3b369ef53e28c3dc7893eacfe263200bba4c5fcf43524ae66d5c0c296b1af0870523ceae3e3104d24b7abf6374a4fea
  languageName: node
  linkType: hard

"hook-std@npm:^3.0.0":
  version: 3.0.0
  resolution: "hook-std@npm:3.0.0"
  checksum: f1f0ca88bbbca2306b9c2c342f45fbecb318ad5496bcbde1fcfc2a64dab0feabd50278a613f683edf07225c4b8b75b3c64ad3f1fca090dd0cae426fdec374a56
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: c955394bdab888a1e9bb10eb33029e0f7ce5a2ac7b3f158099dc8c486c99e73809dca609f5694b223920ca2174db33d32b12f9a2a47141dc59607c29da5a62dd
  languageName: node
  linkType: hard

"hosted-git-info@npm:^4.0.1":
  version: 4.1.0
  resolution: "hosted-git-info@npm:4.1.0"
  dependencies:
    lru-cache: ^6.0.0
  checksum: c3f87b3c2f7eb8c2748c8f49c0c2517c9a95f35d26f4bf54b2a8cba05d2e668f3753548b6ea366b18ec8dadb4e12066e19fa382a01496b0ffa0497eb23cbe461
  languageName: node
  linkType: hard

"hosted-git-info@npm:^7.0.0":
  version: 7.0.2
  resolution: "hosted-git-info@npm:7.0.2"
  dependencies:
    lru-cache: ^10.0.1
  checksum: 467cf908a56556417b18e86ae3b8dee03c2360ef1d51e61c4028fe87f6f309b6ff038589c94b5666af207da9d972d5107698906aabeb78aca134641962a5c6f8
  languageName: node
  linkType: hard

"hosted-git-info@npm:^8.0.0, hosted-git-info@npm:^8.0.2":
  version: 8.0.2
  resolution: "hosted-git-info@npm:8.0.2"
  dependencies:
    lru-cache: ^10.0.1
  checksum: b66ef2cacfe8a8dae156e86eddebd0b9e220fbdb6fa866e273982ab9e5b273e6cab317e1f344c5e63e15c0c8774beb268691cd3af5a02e4c3ef574115e8e60bf
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.0, https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: b87fd89fce72391625271454e70f67fe405277415b48bcc0117ca73d31fa23a4241787afdc8d67f5a116cf37258c052f59ea82daffa72364d61351423848e3b8
  languageName: node
  linkType: hard

"human-signals@npm:^4.3.0":
  version: 4.3.1
  resolution: "human-signals@npm:4.3.1"
  checksum: 6f12958df3f21b6fdaf02d90896c271df00636a31e2bbea05bddf817a35c66b38a6fdac5863e2df85bd52f34958997f1f50350ff97249e1dff8452865d5235d1
  languageName: node
  linkType: hard

"human-signals@npm:^5.0.0":
  version: 5.0.0
  resolution: "human-signals@npm:5.0.0"
  checksum: 6504560d5ed91444f16bea3bd9dfc66110a339442084e56c3e7fa7bbdf3f406426d6563d662bdce67064b165eac31eeabfc0857ed170aaa612cf14ec9f9a464c
  languageName: node
  linkType: hard

"human-signals@npm:^8.0.0":
  version: 8.0.0
  resolution: "human-signals@npm:8.0.0"
  checksum: ccaca470e8b5509d89cd9af82e88fc497a4b4b9149b7964bcd9dd1463f9d9676fb5488f50cd1bc0f12ed8875a7c1c5e7019cbe238992b444919e8cf056688eba
  languageName: node
  linkType: hard

"husky@npm:^7.0.4":
  version: 7.0.4
  resolution: "husky@npm:7.0.4"
  bin:
    husky: lib/bin.js
  checksum: c6ec4af63da2c9522da8674a20ad9b48362cc92704896cc8a58c6a2a39d797feb2b806f93fbd83a6d653fbdceb2c3b6e0b602c6b2e8565206ffc2882ef7db9e9
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ignore-walk@npm:^7.0.0":
  version: 7.0.0
  resolution: "ignore-walk@npm:7.0.0"
  dependencies:
    minimatch: ^9.0.0
  checksum: 509a2a5f10e6ec17b24ae4d23bb774c9243a1590aee3795c8787fb3f2d94f3d6f83f3e6b15614a0c93f1a2f43c7d978a4e4f45ea83fe25dd81da395417bb19ea
  languageName: node
  linkType: hard

"ignore@npm:^5.1.1, ignore@npm:^5.2.0, ignore@npm:^5.2.4":
  version: 5.2.4
  resolution: "ignore@npm:5.2.4"
  checksum: 3d4c309c6006e2621659311783eaea7ebcd41fe4ca1d78c91c473157ad6666a57a2df790fe0d07a12300d9aac2888204d7be8d59f9aaf665b1c7fcdb432517ef
  languageName: node
  linkType: hard

"immediate@npm:~3.0.5":
  version: 3.0.6
  resolution: "immediate@npm:3.0.6"
  checksum: f9b3486477555997657f70318cc8d3416159f208bec4cca3ff3442fd266bc23f50f0c9bd8547e1371a6b5e82b821ec9a7044a4f7b944798b25aa3cc6d5e63e62
  languageName: node
  linkType: hard

"import-fresh@npm:^3.0.0, import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"import-from-esm@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-from-esm@npm:2.0.0"
  dependencies:
    debug: ^4.3.4
    import-meta-resolve: ^4.0.0
  checksum: 6a679eaa4edf7eed44272bb0bd81d784a0d7ee22f3714069eb1506444eb7a3baadaaf7a6ee1dc0e85b6ac001051a14123da418214cb1b34b91e79e945b96c965
  languageName: node
  linkType: hard

"import-meta-resolve@npm:^4.0.0":
  version: 4.1.0
  resolution: "import-meta-resolve@npm:4.1.0"
  checksum: 6497af27bf3ee384ad4efd4e0ec3facf9a114863f35a7b35f248659f32faa5e1ae07baa74d603069f35734ae3718a78b3f66926f98dc9a62e261e7df37854a62
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"indent-string@npm:^5.0.0":
  version: 5.0.0
  resolution: "indent-string@npm:5.0.0"
  checksum: e466c27b6373440e6d84fbc19e750219ce25865cb82d578e41a6053d727e5520dc5725217d6eb1cc76005a1bb1696a0f106d84ce7ebda3033b963a38583fb3b3
  languageName: node
  linkType: hard

"index-to-position@npm:^0.1.2":
  version: 0.1.2
  resolution: "index-to-position@npm:0.1.2"
  checksum: ce0ab15544b154d6821b4f8b3fdb5dc410d560d20e43bcb0fb8ea2ccc5f93dc04caeee6b3ebd4abc7091e437156db4caaaef934ce20f05f079a1dbc73755f7e7
  languageName: node
  linkType: hard

"infer-owner@npm:^1.0.4":
  version: 1.0.4
  resolution: "infer-owner@npm:1.0.4"
  checksum: 181e732764e4a0611576466b4b87dac338972b839920b2a8cde43642e4ed6bd54dc1fb0b40874728f2a2df9a1b097b8ff83b56d5f8f8e3927f837fdcb47d8a89
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ini@npm:^1.3.4, ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"ini@npm:^5.0.0":
  version: 5.0.0
  resolution: "ini@npm:5.0.0"
  checksum: a1cd2a06bf4d995b072ebe97132d8d50a6630798cc3a1c56d325d7b3aaf1f236b3301816f0079e4d47a9887f08e60a6fb95673f19bcafe4f0f9c4a5b5e30aff4
  languageName: node
  linkType: hard

"init-package-json@npm:^7.0.2":
  version: 7.0.2
  resolution: "init-package-json@npm:7.0.2"
  dependencies:
    "@npmcli/package-json": ^6.0.0
    npm-package-arg: ^12.0.0
    promzard: ^2.0.0
    read: ^4.0.0
    semver: ^7.3.5
    validate-npm-package-license: ^3.0.4
    validate-npm-package-name: ^6.0.0
  checksum: b8321d8c32f689665e9a8669fd16e46ba38fe4c5dfb6acd4f695bbc1178f1960942839c99ff8ab1766ba77b0c676aee47b88f5105532b35e7a82f98b2f70a27c
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.3, internal-slot@npm:^1.0.5":
  version: 1.0.5
  resolution: "internal-slot@npm:1.0.5"
  dependencies:
    get-intrinsic: ^1.2.0
    has: ^1.0.3
    side-channel: ^1.0.4
  checksum: 97e84046bf9e7574d0956bd98d7162313ce7057883b6db6c5c7b5e5f05688864b0978ba07610c726d15d66544ffe4b1050107d93f8a39ebc59b15d8b429b497a
  languageName: node
  linkType: hard

"into-stream@npm:^7.0.0":
  version: 7.0.0
  resolution: "into-stream@npm:7.0.0"
  dependencies:
    from2: ^2.3.0
    p-is-promise: ^3.0.0
  checksum: 10c259101237622b2f90a3a30388f2e997f7c4cb16d7236da0380f2e5691b8f9ce32ea2614ae5d1d3b5ad4eba89e2adac0e3d3d24f8494bff69de145432c2d94
  languageName: node
  linkType: hard

"ioredis@npm:^4.28.0":
  version: 4.28.5
  resolution: "ioredis@npm:4.28.5"
  dependencies:
    cluster-key-slot: ^1.1.0
    debug: ^4.3.1
    denque: ^1.1.0
    lodash.defaults: ^4.2.0
    lodash.flatten: ^4.4.0
    lodash.isarguments: ^3.1.0
    p-map: ^2.1.0
    redis-commands: 1.7.0
    redis-errors: ^1.2.0
    redis-parser: ^3.0.0
    standard-as-callback: ^2.1.0
  checksum: a8793c3324cd69fa55b4baacbda118ce6724e574260157761276b31411dd3e168c75490f7155c6ce34d79e01488efa98e0cdb162991970fd56da7cbcdafb8fb8
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"ip-regex@npm:^5.0.0":
  version: 5.0.0
  resolution: "ip-regex@npm:5.0.0"
  checksum: 4098b2df89c015f1484a5946e733ec126af8c1828719d90e09f04af23ce487e1a852670e4d3f51b0dc6dfbaf7d8bfab23fd7893ca60e69833da99b7b1ee3623b
  languageName: node
  linkType: hard

"is-alphabetical@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-alphabetical@npm:1.0.4"
  checksum: 6508cce44fd348f06705d377b260974f4ce68c74000e7da4045f0d919e568226dc3ce9685c5a2af272195384df6930f748ce9213fc9f399b5d31b362c66312cb
  languageName: node
  linkType: hard

"is-alphanumerical@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-alphanumerical@npm:1.0.4"
  dependencies:
    is-alphabetical: ^1.0.0
    is-decimal: ^1.0.0
  checksum: e2e491acc16fcf5b363f7c726f666a9538dba0a043665740feb45bba1652457a73441e7c5179c6768a638ed396db3437e9905f403644ec7c468fb41f4813d03f
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.1, is-array-buffer@npm:^3.0.2":
  version: 3.0.2
  resolution: "is-array-buffer@npm:3.0.2"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.0
    is-typed-array: ^1.1.10
  checksum: dcac9dda66ff17df9cabdc58214172bf41082f956eab30bb0d86bc0fab1e44b690fc8e1f855cf2481245caf4e8a5a006a982a71ddccec84032ed41f9d8da8c14
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-bigint@npm:^1.0.1":
  version: 1.0.4
  resolution: "is-bigint@npm:1.0.4"
  dependencies:
    has-bigints: ^1.0.1
  checksum: c56edfe09b1154f8668e53ebe8252b6f185ee852a50f9b41e8d921cb2bed425652049fbe438723f6cb48a63ca1aa051e948e7e401e093477c99c84eba244f666
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.1.0":
  version: 1.1.2
  resolution: "is-boolean-object@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: c03b23dbaacadc18940defb12c1c0e3aaece7553ef58b162a0f6bba0c2a7e1551b59f365b91e00d2dbac0522392d576ef322628cb1d036a0fe51eb466db67222
  languageName: node
  linkType: hard

"is-buffer@npm:^2.0.0, is-buffer@npm:^2.0.5":
  version: 2.0.5
  resolution: "is-buffer@npm:2.0.5"
  checksum: 764c9ad8b523a9f5a32af29bdf772b08eb48c04d2ad0a7240916ac2688c983bf5f8504bf25b35e66240edeb9d9085461f9b5dae1f3d2861c6b06a65fe983de42
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3, is-callable@npm:^1.1.4, is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-cidr@npm:^5.1.0":
  version: 5.1.0
  resolution: "is-cidr@npm:5.1.0"
  dependencies:
    cidr-regex: ^4.1.1
  checksum: 58ef580c2ac78d744a1cc58155e3bf31af0d7e302dc6a90fb8509f8e7b4c363845dfd6746436f9641993cd8580c3256d16e58297c5b1684ae14edb4db32ccc30
  languageName: node
  linkType: hard

"is-core-module@npm:^2.11.0, is-core-module@npm:^2.12.0, is-core-module@npm:^2.12.1, is-core-module@npm:^2.5.0, is-core-module@npm:^2.9.0":
  version: 2.12.1
  resolution: "is-core-module@npm:2.12.1"
  dependencies:
    has: ^1.0.3
  checksum: f04ea30533b5e62764e7b2e049d3157dc0abd95ef44275b32489ea2081176ac9746ffb1cdb107445cf1ff0e0dfcad522726ca27c27ece64dadf3795428b8e468
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.1":
  version: 1.0.5
  resolution: "is-date-object@npm:1.0.5"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: baa9077cdf15eb7b58c79398604ca57379b2fc4cf9aa7a9b9e295278648f628c9b201400c01c5e0f7afae56507d741185730307cbe7cad3b9f90a77e5ee342fc
  languageName: node
  linkType: hard

"is-decimal@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-decimal@npm:1.0.4"
  checksum: ed483a387517856dc395c68403a10201fddcc1b63dc56513fbe2fe86ab38766120090ecdbfed89223d84ca8b1cd28b0641b93cb6597b6e8f4c097a7c24e3fb96
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: b698118f04feb7eaf3338922bd79cba064ea54a1c3db6ec8c0c8d8ee7613e7e5854d802d3ef646812a8a3ace81182a085dfa0a71cc68b06f3fa794b9783b3c90
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-fullwidth-code-point@npm:4.0.0"
  checksum: 8ae89bf5057bdf4f57b346fb6c55e9c3dd2549983d54191d722d5c739397a903012cc41a04ee3403fd872e811243ef91a7c5196da7b5841dc6b6aae31a264a8d
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-hexadecimal@npm:^1.0.0":
  version: 1.0.4
  resolution: "is-hexadecimal@npm:1.0.4"
  checksum: a452e047587b6069332d83130f54d30da4faf2f2ebaa2ce6d073c27b5703d030d58ed9e0b729c8e4e5b52c6f1dab26781bb77b7bc6c7805f14f320e328ff8cd5
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: ^3.0.0
  bin:
    is-inside-container: cli.js
  checksum: c50b75a2ab66ab3e8b92b3bc534e1ea72ca25766832c0623ac22d134116a98bcf012197d1caabe1d1c4bd5f84363d4aa5c36bb4b585fbcaf57be172cd10a1a03
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-negative-zero@npm:2.0.2"
  checksum: f3232194c47a549da60c3d509c9a09be442507616b69454716692e37ae9f37c4dea264fb208ad0c9f3efd15a796a46b79df07c7e53c6227c32170608b809149a
  languageName: node
  linkType: hard

"is-number-object@npm:^1.0.4":
  version: 1.0.6
  resolution: "is-number-object@npm:1.0.6"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: c697704e8fc2027fc41cb81d29805de4e8b6dc9c3efee93741dbf126a8ecc8443fef85adbc581415ae7e55d325e51d0a942324ae35c829131748cce39cba55f3
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-obj@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-obj@npm:2.0.0"
  checksum: c9916ac8f4621962a42f5e80e7ffdb1d79a3fab7456ceaeea394cd9e0858d04f985a9ace45be44433bf605673c8be8810540fe4cc7f4266fc7526ced95af5a08
  languageName: node
  linkType: hard

"is-path-cwd@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-path-cwd@npm:2.2.0"
  checksum: 46a840921bb8cc0dc7b5b423a14220e7db338072a4495743a8230533ce78812dc152548c86f4b828411fe98c5451959f07cf841c6a19f611e46600bd699e8048
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.2, is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-plain-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-plain-obj@npm:1.1.0"
  checksum: 0ee04807797aad50859652a7467481816cbb57e5cc97d813a7dcd8915da8195dc68c436010bf39d195226cde6a2d352f4b815f16f26b7bf486a5754290629931
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: cec9100678b0a9fe0248a81743041ed990c2d4c99f893d935545cfbc42876cbe86d207f3b895700c690ad2fa520e568c44afc1605044b535a7820c1d40e38daa
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.1.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 6dc45da70d04a81f35c9310971e78a6a3c7a63547ef782e3a07ee3674695081b6ca4e977fbb8efc48dae3375e0b34558d2bcd722aec9bddfa2d7db5b041be8ce
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4":
  version: 1.1.4
  resolution: "is-regex@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 362399b33535bc8f386d96c45c9feb04cf7f8b41c182f54174c1a45c9abbbe5e31290bbad09a458583ff6bf3b2048672cdb1881b13289569a7c548370856a652
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-shared-array-buffer@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 9508929cf14fdc1afc9d61d723c6e8d34f5e117f0bffda4d97e7a5d88c3a8681f633a74f8e3ad1fe92d5113f9b921dc5ca44356492079612f9a247efbce7032a
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-stream@npm:3.0.0"
  checksum: 172093fe99119ffd07611ab6d1bcccfe8bc4aa80d864b15f43e63e54b7abc71e779acd69afdb854c4e2a67fdc16ae710e370eda40088d1cfc956a50ed82d8f16
  languageName: node
  linkType: hard

"is-stream@npm:^4.0.1":
  version: 4.0.1
  resolution: "is-stream@npm:4.0.1"
  checksum: cbea3f1fc271b21ceb228819d0c12a0965a02b57f39423925f99530b4eb86935235f258f06310b67cd02b2d10b49e9a0998f5ececf110ab7d3760bae4055ad23
  languageName: node
  linkType: hard

"is-string@npm:^1.0.5, is-string@npm:^1.0.7":
  version: 1.0.7
  resolution: "is-string@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: 323b3d04622f78d45077cf89aab783b2f49d24dc641aa89b5ad1a72114cfeff2585efc8c12ef42466dff32bde93d839ad321b26884cf75e5a7892a938b089989
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.2, is-symbol@npm:^1.0.3":
  version: 1.0.4
  resolution: "is-symbol@npm:1.0.4"
  dependencies:
    has-symbols: ^1.0.2
  checksum: 92805812ef590738d9de49d677cd17dfd486794773fb6fa0032d16452af46e9b91bb43ffe82c983570f015b37136f4b53b28b8523bfb10b0ece7a66c31a54510
  languageName: node
  linkType: hard

"is-text-path@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-text-path@npm:1.0.1"
  dependencies:
    text-extensions: ^1.0.0
  checksum: fb5d78752c22b3f73a7c9540768f765ffcfa38c9e421e2b9af869565307fa1ae5e3d3a2ba016a43549742856846566d327da406e94a5846ec838a288b1704fd2
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.10, is-typed-array@npm:^1.1.9":
  version: 1.1.12
  resolution: "is-typed-array@npm:1.1.12"
  dependencies:
    which-typed-array: ^1.1.11
  checksum: 4c89c4a3be07186caddadf92197b17fda663a9d259ea0d44a85f171558270d36059d1c386d34a12cba22dfade5aba497ce22778e866adc9406098c8fc4771796
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-unicode-supported@npm:2.1.0"
  checksum: f254e3da6b0ab1a57a94f7273a7798dd35d1d45b227759f600d0fa9d5649f9c07fa8d3c8a6360b0e376adf916d151ec24fc9a50c5295c58bae7ca54a76a063f9
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-weakref@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 95bd9a57cdcb58c63b1c401c60a474b0f45b94719c30f548c891860f051bc2231575c290a6b420c6bc6e7ed99459d424c652bd5bf9a1d5259505dc35b4bf83de
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"issue-parser@npm:^7.0.0":
  version: 7.0.1
  resolution: "issue-parser@npm:7.0.1"
  dependencies:
    lodash.capitalize: ^4.2.1
    lodash.escaperegexp: ^4.1.2
    lodash.isplainobject: ^4.0.6
    lodash.isstring: ^4.0.1
    lodash.uniqby: ^4.7.0
  checksum: baf2831baa84c214a8c9f095889476f2ad7a6511fef7d096941ecf4666a822fbce298baac38510c4be782fc562488d4909535e81fb7a28c55779fcc88e3ec595
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"java-properties@npm:^1.0.2":
  version: 1.0.2
  resolution: "java-properties@npm:1.0.2"
  checksum: 9a086778346e3adbe2395e370f5c779033ed60360055a15e2cead49e3d676d2c73786cf2f6563a1860277dea3dd0a859432e546ed89c03ee08c1f53e31a5d420
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 19c94095ea026725540c0d29da33ab03144f6bcf2d4159e4833d534976e99e0c09c38cefa9a575279a51fc36b31166f8d6d05c9fe2645d5f15851d690b41f17f
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: ff2b5ba2a70e88fd97a3cb28c1840144c5ce8fae9cbeeddba15afa333a5c407cf0e42300cd0a2885dbb055227fe68d405070faad941beeffbfde9cf3b2c78c5d
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^3.0.0":
  version: 3.0.2
  resolution: "json-parse-even-better-errors@npm:3.0.2"
  checksum: 6f04ea6c9ccb783630a59297959247e921cc90b917b8351197ca7fd058fccc7079268fd9362be21ba876fc26aa5039369dd0a2280aae49aae425784794a94927
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^4.0.0":
  version: 4.0.0
  resolution: "json-parse-even-better-errors@npm:4.0.0"
  checksum: da1ae7ef0cc9db02972a06a71322f26bdcda5d7f648c23b28ce7f158ba35707461bcbd91945d8aace10d8d79c383b896725c65ffa410242352692328aa9b5edf
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json-stringify-nice@npm:^1.1.4":
  version: 1.1.4
  resolution: "json-stringify-nice@npm:1.1.4"
  checksum: 6ddf781148b46857ab04e97f47be05f14c4304b86eb5478369edbeacd070c21c697269964b982fc977e8989d4c59091103b1d9dc291aba40096d6cbb9a392b72
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: 866458a8c58a95a49bef3adba929c625e82532bcff1fe93f01d29cb02cac7c3fe1f4b79951b7792c2da9de0b32871a8401a6e3c5b36778ad852bf5b8a61165d7
  languageName: node
  linkType: hard

"json5@npm:^2.2.0, json5@npm:^2.2.2":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"jsonparse@npm:^1.2.0, jsonparse@npm:^1.3.1":
  version: 1.3.1
  resolution: "jsonparse@npm:1.3.1"
  checksum: 6514a7be4674ebf407afca0eda3ba284b69b07f9958a8d3113ef1005f7ec610860c312be067e450c569aab8b89635e332cee3696789c750692bb60daba627f4d
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flat: ^1.3.1
    object.assign: ^4.1.4
    object.values: ^1.1.6
  checksum: f4b05fa4d7b5234230c905cfa88d36dc8a58a6666975a3891429b1a8cdc8a140bca76c297225cb7a499fad25a2c052ac93934449a2c31a44fc9edd06c773780a
  languageName: node
  linkType: hard

"jszip@npm:^3.10.1":
  version: 3.10.1
  resolution: "jszip@npm:3.10.1"
  dependencies:
    lie: ~3.3.0
    pako: ~1.0.2
    readable-stream: ~2.3.6
    setimmediate: ^1.0.5
  checksum: abc77bfbe33e691d4d1ac9c74c8851b5761fba6a6986630864f98d876f3fcc2d36817dfc183779f32c00157b5d53a016796677298272a714ae096dfe6b1c8b60
  languageName: node
  linkType: hard

"just-diff-apply@npm:^5.2.0":
  version: 5.5.0
  resolution: "just-diff-apply@npm:5.5.0"
  checksum: ed6bbd59781542ccb786bd843038e4591e8390aa788075beb69d358051f68fbeb122bda050b7f42515d51fb64b907d5c7bea694a0543b87b24ce406cfb5f5bfa
  languageName: node
  linkType: hard

"just-diff@npm:^6.0.0":
  version: 6.0.2
  resolution: "just-diff@npm:6.0.2"
  checksum: 1a0c7524f640cb88ab013862733e710f840927834208fd3b85cbc5da2ced97acc75e7dcfe493268ac6a6514c51dd8624d2fd9d057050efba3c02b81a6dcb7ff9
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.3":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"libnpmaccess@npm:^9.0.0":
  version: 9.0.0
  resolution: "libnpmaccess@npm:9.0.0"
  dependencies:
    npm-package-arg: ^12.0.0
    npm-registry-fetch: ^18.0.1
  checksum: e7b9dfe0c5be3a8911f87471c87055aafdcb31aa31a91442f62459a955e1dae738c559e615c13658eb5ea869719a15f2df165baefcdad02a020d50612c02a79e
  languageName: node
  linkType: hard

"libnpmdiff@npm:^7.0.0":
  version: 7.0.0
  resolution: "libnpmdiff@npm:7.0.0"
  dependencies:
    "@npmcli/arborist": ^8.0.0
    "@npmcli/installed-package-contents": ^3.0.0
    binary-extensions: ^2.3.0
    diff: ^5.1.0
    minimatch: ^9.0.4
    npm-package-arg: ^12.0.0
    pacote: ^19.0.0
    tar: ^6.2.1
  checksum: 46851ab349329212f6ed7b52f539f91958d1b2e6c0041afb342bb197769c9313bf2eff46f1fa1d8f5b15ca0e506b0d9ce8518ce05f606a44e1bba083630945b8
  languageName: node
  linkType: hard

"libnpmexec@npm:^9.0.0":
  version: 9.0.0
  resolution: "libnpmexec@npm:9.0.0"
  dependencies:
    "@npmcli/arborist": ^8.0.0
    "@npmcli/run-script": ^9.0.1
    ci-info: ^4.0.0
    npm-package-arg: ^12.0.0
    pacote: ^19.0.0
    proc-log: ^5.0.0
    read: ^4.0.0
    read-package-json-fast: ^4.0.0
    semver: ^7.3.7
    walk-up-path: ^3.0.1
  checksum: 4a88e887ae3cc763f1723f88aa8946fd09b94e764f19ed3efe7f27f5b588c555f5d4c8907dba6d5d46a3d47d0ff82eabcf503793853bc9770c67a16561c66e6b
  languageName: node
  linkType: hard

"libnpmfund@npm:^6.0.0":
  version: 6.0.0
  resolution: "libnpmfund@npm:6.0.0"
  dependencies:
    "@npmcli/arborist": ^8.0.0
  checksum: a31b63587b94452d5473e76497fa5be4fa2354874eb28ab687a45454281f07b970e569219c27553ce8a924459f6f6dc383b07e7bd3e0da75c5491ece8e22c406
  languageName: node
  linkType: hard

"libnpmhook@npm:^11.0.0":
  version: 11.0.0
  resolution: "libnpmhook@npm:11.0.0"
  dependencies:
    aproba: ^2.0.0
    npm-registry-fetch: ^18.0.1
  checksum: 5920fd52f32278c0883dd221609e8849a7f4a54263cea3753249befa4a262960714617e590c0ebb643ac7ab178f8b1e432443a5b85e83e6eca7a14d850b099c8
  languageName: node
  linkType: hard

"libnpmorg@npm:^7.0.0":
  version: 7.0.0
  resolution: "libnpmorg@npm:7.0.0"
  dependencies:
    aproba: ^2.0.0
    npm-registry-fetch: ^18.0.1
  checksum: bf880369912262be2099d6ce82c047c4c3bd0bc39cb811e3ef27c97cf2bc84160b2e6fd7b77dd23c608335ae97c0b638696a1efe8a293178abbc9ff9fe540ebd
  languageName: node
  linkType: hard

"libnpmpack@npm:^8.0.0":
  version: 8.0.0
  resolution: "libnpmpack@npm:8.0.0"
  dependencies:
    "@npmcli/arborist": ^8.0.0
    "@npmcli/run-script": ^9.0.1
    npm-package-arg: ^12.0.0
    pacote: ^19.0.0
  checksum: f3cf78c6dcf8fcf2f0464cd54162970b020a50601a364f9e3164f050bdb8e7c0f5a8a0ddb9ff886001af47d2af29abd12b60b018fe42192b24e27156b7d3d2b4
  languageName: node
  linkType: hard

"libnpmpublish@npm:^10.0.1":
  version: 10.0.1
  resolution: "libnpmpublish@npm:10.0.1"
  dependencies:
    ci-info: ^4.0.0
    normalize-package-data: ^7.0.0
    npm-package-arg: ^12.0.0
    npm-registry-fetch: ^18.0.1
    proc-log: ^5.0.0
    semver: ^7.3.7
    sigstore: ^3.0.0
    ssri: ^12.0.0
  checksum: 43ca190d6521b3c31ae399fbf95cca1c45428b5c887b479f8a843705b27f523081c6e2e57932ac8495909aad53f933ba8b56e7a22c657a747c63e719e4478574
  languageName: node
  linkType: hard

"libnpmsearch@npm:^8.0.0":
  version: 8.0.0
  resolution: "libnpmsearch@npm:8.0.0"
  dependencies:
    npm-registry-fetch: ^18.0.1
  checksum: cc84730ea720e5057525b1a392f2aae3e09128634d31d6638ae7eb42f7d771ee497a7370b113b6aebd13a224c2e2965e3f7f3c0d1f50495c8c9f21d3c6bf2b0e
  languageName: node
  linkType: hard

"libnpmteam@npm:^7.0.0":
  version: 7.0.0
  resolution: "libnpmteam@npm:7.0.0"
  dependencies:
    aproba: ^2.0.0
    npm-registry-fetch: ^18.0.1
  checksum: d9ed3b113b4d73294dce4065d03f7834008a01181dce16089d9601a2cbbe333ff6b726c9cc32a969700aac3d129f44e9be6633ab5dd0585806c8d2d8e74a765f
  languageName: node
  linkType: hard

"libnpmversion@npm:^7.0.0":
  version: 7.0.0
  resolution: "libnpmversion@npm:7.0.0"
  dependencies:
    "@npmcli/git": ^6.0.1
    "@npmcli/run-script": ^9.0.1
    json-parse-even-better-errors: ^4.0.0
    proc-log: ^5.0.0
    semver: ^7.3.7
  checksum: fb2c772ec8fdecf01a04c97fee2c534db3b244f5ee8d326f395f304aa2e0e258e4bbfe020c7bd99c555aeca86fcca5232bcd5e30566e6d9d069130914681a7c0
  languageName: node
  linkType: hard

"lie@npm:~3.3.0":
  version: 3.3.0
  resolution: "lie@npm:3.3.0"
  dependencies:
    immediate: ~3.0.5
  checksum: 33102302cf19766f97919a6a98d481e01393288b17a6aa1f030a3542031df42736edde8dab29ffdbf90bebeffc48c761eb1d064dc77592ca3ba3556f9fe6d2a8
  languageName: node
  linkType: hard

"lilconfig@npm:2.0.4":
  version: 2.0.4
  resolution: "lilconfig@npm:2.0.4"
  checksum: 02ae530aa49218d782eb79e92c600ea5220828987f85aa3403fa512cadc7efe38c0ac7d0cd2edf600ad3fae1f6c1752f5b4bb78c0d9950435b044d53d507c9e1
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"lines-and-columns@npm:^2.0.3":
  version: 2.0.4
  resolution: "lines-and-columns@npm:2.0.4"
  checksum: f5e3e207467d3e722280c962b786dc20ebceb191821dcd771d14ab3146b6744cae28cf305ee4638805bec524ac54800e15698c853fcc53243821f88df37e4975
  languageName: node
  linkType: hard

"lint-staged@npm:^12.3.4":
  version: 12.3.4
  resolution: "lint-staged@npm:12.3.4"
  dependencies:
    cli-truncate: ^3.1.0
    colorette: ^2.0.16
    commander: ^8.3.0
    debug: ^4.3.3
    execa: ^5.1.1
    lilconfig: 2.0.4
    listr2: ^4.0.1
    micromatch: ^4.0.4
    normalize-path: ^3.0.0
    object-inspect: ^1.12.0
    string-argv: ^0.3.1
    supports-color: ^9.2.1
    yaml: ^1.10.2
  bin:
    lint-staged: bin/lint-staged.js
  checksum: 4e0b4b9da4183a0daeab35d41e4d243fb1270a39db997efa0c3745fa148a7a4b8b723a51cb757595fc8bb118796ca1465e67aede72f25e9ea48165aec36cec3b
  languageName: node
  linkType: hard

"listr2@npm:^4.0.1":
  version: 4.0.4
  resolution: "listr2@npm:4.0.4"
  dependencies:
    cli-truncate: ^2.1.0
    colorette: ^2.0.16
    log-update: ^4.0.0
    p-map: ^4.0.0
    rfdc: ^1.3.0
    rxjs: ^7.5.4
    through: ^2.3.8
    wrap-ansi: ^7.0.0
  peerDependencies:
    enquirer: ">= 2.3.0 < 3"
  peerDependenciesMeta:
    enquirer:
      optional: true
  checksum: 1e6e44a3a0337f47d0a1bc90712b0001129d3ca3fae56dc5b834da556b634862a211d8c638528600daf1c1899a3f2366f822ecb03dba5327ff2c017578815e61
  languageName: node
  linkType: hard

"load-json-file@npm:^4.0.0":
  version: 4.0.0
  resolution: "load-json-file@npm:4.0.0"
  dependencies:
    graceful-fs: ^4.1.2
    parse-json: ^4.0.0
    pify: ^3.0.0
    strip-bom: ^3.0.0
  checksum: 8f5d6d93ba64a9620445ee9bde4d98b1eac32cf6c8c2d20d44abfa41a6945e7969456ab5f1ca2fb06ee32e206c9769a20eec7002fe290de462e8c884b6b8b356
  languageName: node
  linkType: hard

"locate-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "locate-path@npm:2.0.0"
  dependencies:
    p-locate: ^2.0.0
    path-exists: ^3.0.0
  checksum: 02d581edbbbb0fa292e28d96b7de36b5b62c2fa8b5a7e82638ebb33afa74284acf022d3b1e9ae10e3ffb7658fbc49163fcd5e76e7d1baaa7801c3e05a81da755
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: ^3.0.0
    path-exists: ^3.0.0
  checksum: 53db3996672f21f8b0bf2a2c645ae2c13ffdae1eeecfcd399a583bce8516c0b88dcb4222ca6efbbbeb6949df7e46860895be2c02e8d3219abd373ace3bfb4e11
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 05cbffad6e2adbb331a4e16fbd826e7faee403a1a04873b82b42c0f22090f280839f85b95393f487c1303c8a3d2a010048bf06151a6cbe03eee4d388fb0a12d2
  languageName: node
  linkType: hard

"lodash.capitalize@npm:^4.2.1":
  version: 4.2.1
  resolution: "lodash.capitalize@npm:4.2.1"
  checksum: d9195f31d48c105206f1099946d8bbc8ab71435bc1c8708296992a31a992bb901baf120fdcadd773098ac96e62a79e6b023ee7d26a2deb0d6c6aada930e6ad0a
  languageName: node
  linkType: hard

"lodash.defaults@npm:^4.2.0":
  version: 4.2.0
  resolution: "lodash.defaults@npm:4.2.0"
  checksum: 84923258235592c8886e29de5491946ff8c2ae5c82a7ac5cddd2e3cb697e6fbdfbbb6efcca015795c86eec2bb953a5a2ee4016e3735a3f02720428a40efbb8f1
  languageName: node
  linkType: hard

"lodash.eq@npm:^4.0.0":
  version: 4.0.0
  resolution: "lodash.eq@npm:4.0.0"
  checksum: c46d45b8da669151cdf4fcb996056c8847c1c32723fa3c60ef87433ee41b01b8e1ee615c4c433b6f27391fa9c1793ad6d892dd30bac4c9c3508be3fa5f3d5def
  languageName: node
  linkType: hard

"lodash.escaperegexp@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.escaperegexp@npm:4.1.2"
  checksum: 6d99452b1cfd6073175a9b741a9b09ece159eac463f86f02ea3bee2e2092923fce812c8d2bf446309cc52d1d61bf9af51c8118b0d7421388e6cead7bd3798f0f
  languageName: node
  linkType: hard

"lodash.flatten@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.flatten@npm:4.4.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"lodash.indexof@npm:^4.0.5":
  version: 4.0.5
  resolution: "lodash.indexof@npm:4.0.5"
  checksum: 4f3ead786c2941f18f1ef250459bcc6182ac00f04e76a2b94d3933349c879cd4b79444d59b0de835ad93d7b01626c1bc8ee55d7776879f099c0617d896a29f28
  languageName: node
  linkType: hard

"lodash.isarguments@npm:^3.1.0":
  version: 3.1.0
  resolution: "lodash.isarguments@npm:3.1.0"
  checksum: ae1526f3eb5c61c77944b101b1f655f846ecbedcb9e6b073526eba6890dc0f13f09f72e11ffbf6540b602caee319af9ac363d6cdd6be41f4ee453436f04f13b5
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: 29c6351f281e0d9a1d58f1a4c8f4400924b4c79f18dfc4613624d7d54784df07efaff97c1ff2659f3e085ecf4fff493300adc4837553104cef2634110b0d5337
  languageName: node
  linkType: hard

"lodash.isstring@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.isstring@npm:4.0.1"
  checksum: eaac87ae9636848af08021083d796e2eea3d02e80082ab8a9955309569cb3a463ce97fd281d7dc119e402b2e7d8c54a23914b15d2fc7fff56461511dc8937ba0
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.uniqby@npm:^4.7.0":
  version: 4.7.0
  resolution: "lodash.uniqby@npm:4.7.0"
  checksum: 659264545a95726d1493123345aad8cbf56e17810fa9a0b029852c6d42bc80517696af09d99b23bef1845d10d95e01b8b4a1da578f22aeba7a30d3e0022a4938
  languageName: node
  linkType: hard

"lodash.zipobject@npm:^4.1.3":
  version: 4.1.3
  resolution: "lodash.zipobject@npm:4.1.3"
  checksum: 1ab635b665c0488a905779705a6683e9024115176e9e947d75d2a6b1e8673230fdb11c417788fbaf26d71e1cac5ad8e59a558924612cbf7d6615780836048883
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15, lodash@npm:^4.17.19, lodash@npm:^4.17.21, lodash@npm:^4.17.4":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-update@npm:^4.0.0":
  version: 4.0.0
  resolution: "log-update@npm:4.0.0"
  dependencies:
    ansi-escapes: ^4.3.0
    cli-cursor: ^3.1.0
    slice-ansi: ^4.0.0
    wrap-ansi: ^6.2.0
  checksum: ae2f85bbabc1906034154fb7d4c4477c79b3e703d22d78adee8b3862fa913942772e7fa11713e3d96fb46de4e3cabefbf5d0a544344f03b58d3c4bff52aa9eb2
  languageName: node
  linkType: hard

"long@npm:^5.2.0":
  version: 5.2.3
  resolution: "long@npm:5.2.3"
  checksum: 885ede7c3de4facccbd2cacc6168bae3a02c3e836159ea4252c87b6e34d40af819824b2d4edce330bfb5c4d6e8ce3ec5864bdcf9473fa1f53a4f8225860e5897
  languageName: node
  linkType: hard

"longest-streak@npm:^2.0.0":
  version: 2.0.4
  resolution: "longest-streak@npm:2.0.4"
  checksum: 28b8234a14963002c5c71035dee13a0a11e9e9d18ffa320fdc8796ed7437399204495702ed69cd2a7087b0af041a2a8b562829b7c1e2042e73a3374d1ecf6580
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.2.2":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: b86e5e0e25f7f777b77fabd8e2cbf15737972869d852a22b7e73c17623928fccb826d8e46b9951501d3f20e51ad74ba8c59ed584f610526a48f8ccf88aaec402
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.0, make-fetch-happen@npm:^14.0.1, make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"map-obj@npm:^1.0.0":
  version: 1.0.1
  resolution: "map-obj@npm:1.0.1"
  checksum: 9949e7baec2a336e63b8d4dc71018c117c3ce6e39d2451ccbfd3b8350c547c4f6af331a4cbe1c83193d7c6b786082b6256bde843db90cb7da2a21e8fcc28afed
  languageName: node
  linkType: hard

"map-obj@npm:^4.0.0":
  version: 4.3.0
  resolution: "map-obj@npm:4.3.0"
  checksum: fbc554934d1a27a1910e842bc87b177b1a556609dd803747c85ece420692380827c6ae94a95cce4407c054fa0964be3bf8226f7f2cb2e9eeee432c7c1985684e
  languageName: node
  linkType: hard

"markdown-table@npm:^2.0.0":
  version: 2.0.0
  resolution: "markdown-table@npm:2.0.0"
  dependencies:
    repeat-string: ^1.0.0
  checksum: 9bb634a9300016cbb41216c1eab44c74b6b7083ac07872e296f900a29449cf0e260ece03fa10c3e9784ab94c61664d1d147da0315f95e1336e2bdcc025615c90
  languageName: node
  linkType: hard

"marked-terminal@npm:^7.0.0":
  version: 7.2.1
  resolution: "marked-terminal@npm:7.2.1"
  dependencies:
    ansi-escapes: ^7.0.0
    ansi-regex: ^6.1.0
    chalk: ^5.3.0
    cli-highlight: ^2.1.11
    cli-table3: ^0.6.5
    node-emoji: ^2.1.3
    supports-hyperlinks: ^3.1.0
  peerDependencies:
    marked: ">=1 <15"
  checksum: f6eea47b692955175407fe89ac2c944dc674c681da186ebe71f23816c9f10bd7f2f4ed3e2507a2c2e0a2c92d49dff8cc618caea7cb61e0d2ac2d1b1f3061759e
  languageName: node
  linkType: hard

"marked@npm:^12.0.0":
  version: 12.0.2
  resolution: "marked@npm:12.0.2"
  bin:
    marked: bin/marked.js
  checksum: 966422e2ba519294aa657bacb2e51784e4b641c1c8f15bdf9315878993c4ea09fe0d00ba2da761e443a3c52cc285c452644fd107ab0f356669bd5aac08d5c0bd
  languageName: node
  linkType: hard

"mdast-util-find-and-replace@npm:^1.1.0":
  version: 1.1.1
  resolution: "mdast-util-find-and-replace@npm:1.1.1"
  dependencies:
    escape-string-regexp: ^4.0.0
    unist-util-is: ^4.0.0
    unist-util-visit-parents: ^3.0.0
  checksum: e4c9e50d9bce5ae4c728a925bd60080b94d16aaa312c27e2b70b16ddc29a5d0a0844d6e18efaef08aeb22c68303ec528f20183d1b0420504a0c2c1710cebd76f
  languageName: node
  linkType: hard

"mdast-util-from-markdown@npm:^0.8.0":
  version: 0.8.5
  resolution: "mdast-util-from-markdown@npm:0.8.5"
  dependencies:
    "@types/mdast": ^3.0.0
    mdast-util-to-string: ^2.0.0
    micromark: ~2.11.0
    parse-entities: ^2.0.0
    unist-util-stringify-position: ^2.0.0
  checksum: 5a9d0d753a42db763761e874c22365d0c7c9934a5a18b5ff76a0643610108a208a041ffdb2f3d3dd1863d3d915225a4020a0aade282af0facfd0df110601eee6
  languageName: node
  linkType: hard

"mdast-util-gfm-autolink-literal@npm:^0.1.0":
  version: 0.1.3
  resolution: "mdast-util-gfm-autolink-literal@npm:0.1.3"
  dependencies:
    ccount: ^1.0.0
    mdast-util-find-and-replace: ^1.1.0
    micromark: ^2.11.3
  checksum: 9f7b888678631fd8c0a522b0689a750aead2b05d57361dbdf02c10381557f1ce874f746226141f3ace1e0e7952495e8d5ce8f9af423a7a66bb300d4635a918eb
  languageName: node
  linkType: hard

"mdast-util-gfm-strikethrough@npm:^0.2.0":
  version: 0.2.3
  resolution: "mdast-util-gfm-strikethrough@npm:0.2.3"
  dependencies:
    mdast-util-to-markdown: ^0.6.0
  checksum: 51aa11ca8f1a5745f1eb9ccddb0eca797b3ede6f0c7bf355d594ad57c02c98d95260f00b1c4b07504018e0b22708531eabb76037841f09ce8465444706a06522
  languageName: node
  linkType: hard

"mdast-util-gfm-table@npm:^0.1.0":
  version: 0.1.6
  resolution: "mdast-util-gfm-table@npm:0.1.6"
  dependencies:
    markdown-table: ^2.0.0
    mdast-util-to-markdown: ~0.6.0
  checksum: eeb43faf833753315b4ccf8d7bc8a6845b31562b2d2dd12a92aa40f9cee1b1954643c7515399a98f9b2e143c95cf6b5c0aac5941a4f609d6a57335587cee99ac
  languageName: node
  linkType: hard

"mdast-util-gfm-task-list-item@npm:^0.1.0":
  version: 0.1.6
  resolution: "mdast-util-gfm-task-list-item@npm:0.1.6"
  dependencies:
    mdast-util-to-markdown: ~0.6.0
  checksum: c10480c0ae86547980b38b49fba2ecd36a50bf1f3478d3f12810a0d8e8f821585c2bd7d805dd735518e84493b5eef314afdb8d59807021e2d9aa22d077eb7588
  languageName: node
  linkType: hard

"mdast-util-gfm@npm:^0.1.0":
  version: 0.1.2
  resolution: "mdast-util-gfm@npm:0.1.2"
  dependencies:
    mdast-util-gfm-autolink-literal: ^0.1.0
    mdast-util-gfm-strikethrough: ^0.2.0
    mdast-util-gfm-table: ^0.1.0
    mdast-util-gfm-task-list-item: ^0.1.0
    mdast-util-to-markdown: ^0.6.1
  checksum: 368ed535b2c2e0f33d0225a9e9c985468bf4825a06896815369aea585f6defaccb555ac40ba911e02c8e8c47e79f7efb4348de532de50bca2638a1e568f2d3c9
  languageName: node
  linkType: hard

"mdast-util-to-markdown@npm:^0.6.0, mdast-util-to-markdown@npm:^0.6.1, mdast-util-to-markdown@npm:^0.6.2, mdast-util-to-markdown@npm:~0.6.0":
  version: 0.6.5
  resolution: "mdast-util-to-markdown@npm:0.6.5"
  dependencies:
    "@types/unist": ^2.0.0
    longest-streak: ^2.0.0
    mdast-util-to-string: ^2.0.0
    parse-entities: ^2.0.0
    repeat-string: ^1.0.0
    zwitch: ^1.0.0
  checksum: 7ebc47533bff6e8669f85ae124dc521ea570e9df41c0d9e4f0f43c19ef4a8c9928d741f3e4afa62fcca1927479b714582ff5fd684ef240d84ee5b75ab9d863cf
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-to-string@npm:2.0.0"
  checksum: 0b2113ada10e002fbccb014170506dabe2f2ddacaacbe4bc1045c33f986652c5a162732a2c057c5335cdb58419e2ad23e368e5be226855d4d4e280b81c4e9ec2
  languageName: node
  linkType: hard

"memoize-one@npm:^5.1.1":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: a3cba7b824ebcf24cdfcd234aa7f86f3ad6394b8d9be4c96ff756dafb8b51c7f71320785fbc2304f1af48a0467cbbd2a409efc9333025700ed523f254cb52e3d
  languageName: node
  linkType: hard

"meow@npm:^13.0.0":
  version: 13.2.0
  resolution: "meow@npm:13.2.0"
  checksum: 79c61dc02ad448ff5c29bbaf1ef42181f1eae9947112c0e23db93e84cbc2708ecda53e54bfc6689f1e55255b2cea26840ec76e57a5773a16ca45f4fe2163ec1c
  languageName: node
  linkType: hard

"meow@npm:^8.0.0":
  version: 8.1.2
  resolution: "meow@npm:8.1.2"
  dependencies:
    "@types/minimist": ^1.2.0
    camelcase-keys: ^6.2.2
    decamelize-keys: ^1.1.0
    hard-rejection: ^2.1.0
    minimist-options: 4.1.0
    normalize-package-data: ^3.0.0
    read-pkg-up: ^7.0.1
    redent: ^3.0.0
    trim-newlines: ^3.0.0
    type-fest: ^0.18.0
    yargs-parser: ^20.2.3
  checksum: bc23bf1b4423ef6a821dff9734406bce4b91ea257e7f10a8b7f896f45b59649f07adc0926e2917eacd8cf1df9e4cd89c77623cf63dfd0f8bf54de07a32ee5a85
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.2.3, merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromark-extension-gfm-autolink-literal@npm:~0.5.0":
  version: 0.5.7
  resolution: "micromark-extension-gfm-autolink-literal@npm:0.5.7"
  dependencies:
    micromark: ~2.11.3
  checksum: 319ec793c2e374e4cc0cbbb07326c1affb78819e507c7c1577f9d14b972852a6bb55e664332ec51f7cca24bdddd43429c5dd55f11e9200b1a00bab1bf494fb2d
  languageName: node
  linkType: hard

"micromark-extension-gfm-strikethrough@npm:~0.6.5":
  version: 0.6.5
  resolution: "micromark-extension-gfm-strikethrough@npm:0.6.5"
  dependencies:
    micromark: ~2.11.0
  checksum: 67711633590d3e688759a46aaed9f9d04bcaf29b6615eec17af082eabe1059fbca4beb41ba13db418ae7be3ac90198742fbabe519a70f9b6bb615598c5d6ef1a
  languageName: node
  linkType: hard

"micromark-extension-gfm-table@npm:~0.4.0":
  version: 0.4.3
  resolution: "micromark-extension-gfm-table@npm:0.4.3"
  dependencies:
    micromark: ~2.11.0
  checksum: 12c78de985944dd66aae409871c45d801cc65704f55ea5cc8afac422042c6d3b5e777b154c079ae81298b30b83434b257b54981bda51c220a102042dd2524a63
  languageName: node
  linkType: hard

"micromark-extension-gfm-tagfilter@npm:~0.3.0":
  version: 0.3.0
  resolution: "micromark-extension-gfm-tagfilter@npm:0.3.0"
  checksum: 9369736a203836b2933dfdeacab863e7a4976139b9dd46fa5bd6c2feeef50c7dbbcdd641ae95f0481f577d8aa22396bfa7ed9c38515647d4cf3f2c727cc094a3
  languageName: node
  linkType: hard

"micromark-extension-gfm-task-list-item@npm:~0.3.0":
  version: 0.3.3
  resolution: "micromark-extension-gfm-task-list-item@npm:0.3.3"
  dependencies:
    micromark: ~2.11.0
  checksum: e4ccbe6b440234c8ee05d89315e1204c78773724241af31ac328194470a8a61bc6606eab3ce2d9a83da4401b06e07936038654493da715d40522133d1556dda4
  languageName: node
  linkType: hard

"micromark-extension-gfm@npm:^0.3.0":
  version: 0.3.3
  resolution: "micromark-extension-gfm@npm:0.3.3"
  dependencies:
    micromark: ~2.11.0
    micromark-extension-gfm-autolink-literal: ~0.5.0
    micromark-extension-gfm-strikethrough: ~0.6.5
    micromark-extension-gfm-table: ~0.4.0
    micromark-extension-gfm-tagfilter: ~0.3.0
    micromark-extension-gfm-task-list-item: ~0.3.0
  checksum: 7957a1afd8c92daa0fc165342902729334b22d59feacd85b20a0d9cc453c90bbdd5b5ba85a3d177c01802060aeb3326daf05d3e6d95932fcbc8371827c98336e
  languageName: node
  linkType: hard

"micromark@npm:^2.11.3, micromark@npm:~2.11.0, micromark@npm:~2.11.3":
  version: 2.11.4
  resolution: "micromark@npm:2.11.4"
  dependencies:
    debug: ^4.0.0
    parse-entities: ^2.0.0
  checksum: f8a5477d394908a5d770227aea71657a76423d420227c67ea0699e659a5f62eb39d504c1f7d69ec525a6af5aaeb6a7bffcdba95614968c03d41d3851edecb0d6
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.0, micromatch@npm:^4.0.2, micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"mime@npm:^4.0.0":
  version: 4.0.6
  resolution: "mime@npm:4.0.6"
  bin:
    mime: bin/cli.js
  checksum: b18389c63c5120fbae32b82bc33a0d2ae26443bff25c63c88a7283cdc09e610909403faafd91e461b9fd20650642e37a612fe7982131a909ab1219aaaff2c0a0
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-fn@npm:4.0.0"
  checksum: 995dcece15ee29aa16e188de6633d43a3db4611bcf93620e7e62109ec41c79c0f34277165b8ce5e361205049766e371851264c21ac64ca35499acb5421c2ba56
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: bfc6dd03c5eaf623a4963ebd94d087f6f4bbbfd8c41329a7f09706b0cb66969c4ddd336abeb587bc44bc6f08e13bf90f0b374f9d71f9f01e04adc2cd6f083ef1
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.0, minimatch@npm:^9.0.4, minimatch@npm:^9.0.5":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist-options@npm:4.1.0":
  version: 4.1.0
  resolution: "minimist-options@npm:4.1.0"
  dependencies:
    arrify: ^1.0.1
    is-plain-obj: ^1.1.0
    kind-of: ^6.0.3
  checksum: 8c040b3068811e79de1140ca2b708d3e203c8003eb9a414c1ab3cd467fc5f17c9ca02a5aef23bedc51a7f8bfbe77f87e9a7e31ec81fba304cda675b019496f4e
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.5, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 7d59a31011ab9e4d1af6562dd4c4440e425b2baf4c5edbdd2e22fb25a88629e1cdceca39953ff209da504a46021df520f18fd9a519f36efae4750ff724ddadea
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.1.6
  resolution: "minipass@npm:3.1.6"
  dependencies:
    yallist: ^4.0.0
  checksum: 57a04041413a3531a65062452cb5175f93383ef245d6f4a2961d34386eb9aa8ac11ac7f16f791f5e8bbaf1dfb1ef01596870c88e8822215db57aa591a5bb0a77
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.1, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: ^7.0.4
    rimraf: ^5.0.5
  checksum: da0a53899252380475240c587e52c824f8998d9720982ba5c4693c68e89230718884a209858c156c6e08d51aad35700a3589987e540593c36f6713fe30cd7338
  languageName: node
  linkType: hard

"mkdirp-infer-owner@npm:^1.0.2":
  version: 1.0.2
  resolution: "mkdirp-infer-owner@npm:1.0.2"
  dependencies:
    chownr: ^1.1.3
    infer-owner: ^1.0.4
    mkdirp: ^1.0.3
  checksum: 759dbc3c4266e25028bb051ca0d5003860bf68249a0d2c3417dc26a0d0f1b883936866e19840f93a0ffceaf8d3c1982cc0fad6667e55418d63eed0af684bcb54
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.2, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mute-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "mute-stream@npm:2.0.0"
  checksum: d2e4fd2f5aa342b89b98134a8d899d8ef9b0a6d69274c4af9df46faa2d97aeb1f2ce83d867880d6de63643c52386579b99139801e24e7526c3b9b0a6d1e18d6c
  languageName: node
  linkType: hard

"mz@npm:^2.4.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: ^1.0.0
    object-assign: ^4.0.1
    thenify-all: ^1.0.0
  checksum: 8427de0ece99a07e9faed3c0c6778820d7543e3776f9a84d22cf0ec0a8eb65f6e9aee9c9d353ff9a105ff62d33a9463c6ca638974cc652ee8140cd1e35951c87
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 3be20d8866a57a6b6d218e82549711c8352ed969f9ab3c45379da28f405363ad4c9aeb0b39e9abc101a529ca65a72ff9502b00bf74a912c4b64a9d62dfd26c29
  languageName: node
  linkType: hard

"natural-compare-lite@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare-lite@npm:1.4.0"
  checksum: 5222ac3986a2b78dd6069ac62cbb52a7bf8ffc90d972ab76dfe7b01892485d229530ed20d0c62e79a6b363a663b273db3bde195a1358ce9e5f779d4453887225
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.0":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"nerf-dart@npm:^1.0.0":
  version: 1.0.0
  resolution: "nerf-dart@npm:1.0.0"
  checksum: 0e5508d83eae21a6ed0bd32b3a048c849741023811f06efa972800f4ad55eaa8205442e81c406ad051771f232c4ed3d3ee262f6c850bbcad9660f54a6471a4b9
  languageName: node
  linkType: hard

"next@npm:^15.2.4":
  version: 15.2.4
  resolution: "next@npm:15.2.4"
  dependencies:
    "@next/env": 15.2.4
    "@next/swc-darwin-arm64": 15.2.4
    "@next/swc-darwin-x64": 15.2.4
    "@next/swc-linux-arm64-gnu": 15.2.4
    "@next/swc-linux-arm64-musl": 15.2.4
    "@next/swc-linux-x64-gnu": 15.2.4
    "@next/swc-linux-x64-musl": 15.2.4
    "@next/swc-win32-arm64-msvc": 15.2.4
    "@next/swc-win32-x64-msvc": 15.2.4
    "@swc/counter": 0.1.3
    "@swc/helpers": 0.5.15
    busboy: 1.6.0
    caniuse-lite: ^1.0.30001579
    postcss: 8.4.31
    sharp: ^0.33.5
    styled-jsx: 5.1.6
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    "@playwright/test": ^1.41.2
    babel-plugin-react-compiler: "*"
    react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
    sharp:
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    "@playwright/test":
      optional: true
    babel-plugin-react-compiler:
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: a235fbe6a77e7b81ea8d9372a85ce02a1e1ecc5666b19f60b071acaa3135bf42fa4a29ad616257e3a2e60fadb9400cb4041da0e8217d46c76d7703c197896bd4
  languageName: node
  linkType: hard

"node-emoji@npm:^2.1.3":
  version: 2.2.0
  resolution: "node-emoji@npm:2.2.0"
  dependencies:
    "@sindresorhus/is": ^4.6.0
    char-regex: ^1.0.2
    emojilib: ^2.4.0
    skin-tone: ^2.0.0
  checksum: 9642bee0b8c5f2124580e6a2d4c5ec868987bc77b6ce3a335bbec8db677082cbe1a9b72c11aac60043396a8d36e0afad4bcc33d92105d103d2d1b6a59106219a
  languageName: node
  linkType: hard

"node-fetch@npm:^2.3.0, node-fetch@npm:^2.6.7":
  version: 2.6.7
  resolution: "node-fetch@npm:2.6.7"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 8d816ffd1ee22cab8301c7756ef04f3437f18dace86a1dae22cf81db8ef29c0bf6655f3215cb0cdb22b420b6fe141e64b26905e7f33f9377a7fa59135ea3e10b
  languageName: node
  linkType: hard

"node-gyp@npm:^11.0.0":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: d7d5055ccc88177f721c7cd4f8f9440c29a0eb40e7b79dba89ef882ec957975dfc1dcb8225e79ab32481a02016eb13bbc051a913ea88d482d3cbdf2131156af4
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.13":
  version: 2.0.13
  resolution: "node-releases@npm:2.0.13"
  checksum: 17ec8f315dba62710cae71a8dad3cd0288ba943d2ece43504b3b1aa8625bf138637798ab470b1d9035b0545996f63000a8a926e0f6d35d0996424f8b6d36dda3
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.0.0
  resolution: "nopt@npm:8.0.0"
  dependencies:
    abbrev: ^2.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 2cfc65e7ee38af2e04aea98f054753b0230011c0eeca4ecf131bd7d25984cbbf6f214586e0ae5dfcc2e830bc0bffa5a7fb28ea8d0b306ffd4ae8ea2d814c1ab3
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.5.0":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: ^2.1.4
    resolve: ^1.10.0
    semver: 2 || 3 || 4 || 5
    validate-npm-package-license: ^3.0.1
  checksum: 7999112efc35a6259bc22db460540cae06564aa65d0271e3bdfa86876d08b0e578b7b5b0028ee61b23f1cae9fc0e7847e4edc0948d3068a39a2a82853efc8499
  languageName: node
  linkType: hard

"normalize-package-data@npm:^3.0.0":
  version: 3.0.3
  resolution: "normalize-package-data@npm:3.0.3"
  dependencies:
    hosted-git-info: ^4.0.1
    is-core-module: ^2.5.0
    semver: ^7.3.4
    validate-npm-package-license: ^3.0.1
  checksum: bbcee00339e7c26fdbc760f9b66d429258e2ceca41a5df41f5df06cc7652de8d82e8679ff188ca095cad8eff2b6118d7d866af2b68400f74602fbcbce39c160a
  languageName: node
  linkType: hard

"normalize-package-data@npm:^6.0.0":
  version: 6.0.2
  resolution: "normalize-package-data@npm:6.0.2"
  dependencies:
    hosted-git-info: ^7.0.0
    semver: ^7.3.5
    validate-npm-package-license: ^3.0.4
  checksum: ea35f8de68e03fc845f545c8197857c0cd256207fdb809ca63c2b39fe76ae77765ee939eb21811fb6c3b533296abf49ebe3cd617064f98a775adaccb24ff2e03
  languageName: node
  linkType: hard

"normalize-package-data@npm:^7.0.0":
  version: 7.0.0
  resolution: "normalize-package-data@npm:7.0.0"
  dependencies:
    hosted-git-info: ^8.0.0
    semver: ^7.3.5
    validate-npm-package-license: ^3.0.4
  checksum: e355670aa1bfa757cfa380c3ea66ad25e8c62001b44c9fa7a9ea3fa8a69f24259fa3e0614bc9330ba1d16052b4feaa2558f4778d65bc34a1b78f73397a418ec0
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-url@npm:^8.0.0":
  version: 8.0.1
  resolution: "normalize-url@npm:8.0.1"
  checksum: 43ea9ef0d6d135dd1556ab67aa4b74820f0d9d15aa504b59fa35647c729f1147dfce48d3ad504998fd1010f089cfb82c86c6d9126eb5c5bd2e9bd25f3a97749b
  languageName: node
  linkType: hard

"npm-audit-report@npm:^6.0.0":
  version: 6.0.0
  resolution: "npm-audit-report@npm:6.0.0"
  checksum: 732dc8711cde3e3cbbfd6baca68d47a5fd18b599141ec85c47963418e9d309d40bd1526823e132005b985f925cd65a7d1fc67945b837e80584f28b3405fd045e
  languageName: node
  linkType: hard

"npm-bundled@npm:^4.0.0":
  version: 4.0.0
  resolution: "npm-bundled@npm:4.0.0"
  dependencies:
    npm-normalize-package-bin: ^4.0.0
  checksum: 028711cda73d162c01abc39ee2caddacf80c3bfc258092b4112250515f084888780aee6fdfed0dc727be3b4f5d56b8736367485aca19a641255868200860459f
  languageName: node
  linkType: hard

"npm-install-checks@npm:^7.1.0, npm-install-checks@npm:^7.1.1":
  version: 7.1.1
  resolution: "npm-install-checks@npm:7.1.1"
  dependencies:
    semver: ^7.1.1
  checksum: b90cbca3ac34ed2ed0bf05867dc5b4eac45afa626b71ce8b6bf3955c6c7a1e22cbbb4718f5d19b0b19bca89f16a13bf3b6ca191afef12ac6c90120cef70b7f1d
  languageName: node
  linkType: hard

"npm-normalize-package-bin@npm:^4.0.0":
  version: 4.0.0
  resolution: "npm-normalize-package-bin@npm:4.0.0"
  checksum: e1a0971e5640bc116c5197f9707d86dc404b6d8e13da2c7ea82baa5583b8da279a3c8607234aa1d733c2baac3b3eba87b156f021f20ae183dc4806530e61675d
  languageName: node
  linkType: hard

"npm-package-arg@npm:^12.0.0":
  version: 12.0.1
  resolution: "npm-package-arg@npm:12.0.1"
  dependencies:
    hosted-git-info: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    validate-npm-package-name: ^6.0.0
  checksum: 01c1c620e07940c9c6c243fc3811f1ce745f217800d9d94982519adac1e283790fcc84183e59b0fe10151bbf9967708891eb58256198b153820d79988db98c32
  languageName: node
  linkType: hard

"npm-packlist@npm:^9.0.0":
  version: 9.0.0
  resolution: "npm-packlist@npm:9.0.0"
  dependencies:
    ignore-walk: ^7.0.0
  checksum: 1286dcec2e53503ce7133088f82fb0840405a623f035487eafcdaf0865dc1632c970ad3e24234eb13ccd33f41ba2b95d13585038ef76817dfd74dd93c1b73eae
  languageName: node
  linkType: hard

"npm-pick-manifest@npm:^10.0.0":
  version: 10.0.0
  resolution: "npm-pick-manifest@npm:10.0.0"
  dependencies:
    npm-install-checks: ^7.1.0
    npm-normalize-package-bin: ^4.0.0
    npm-package-arg: ^12.0.0
    semver: ^7.3.5
  checksum: 2139bd612ee853d86b6420a223dd19dd562cfc7c875ae27895a2d18a9b980e48fe9e895acf69224010b20d01d00150d8da35569d87f09047cc938927ffa2c282
  languageName: node
  linkType: hard

"npm-profile@npm:^11.0.1":
  version: 11.0.1
  resolution: "npm-profile@npm:11.0.1"
  dependencies:
    npm-registry-fetch: ^18.0.0
    proc-log: ^5.0.0
  checksum: 78f82281dc58106c0419688a7ff33f23f7f67980fbfeebeda0a9c3517ce7133e2097e372a466c6442366f49e502f71e8dad909c1455284abcc45de602b826d86
  languageName: node
  linkType: hard

"npm-registry-fetch@npm:^18.0.0, npm-registry-fetch@npm:^18.0.1, npm-registry-fetch@npm:^18.0.2":
  version: 18.0.2
  resolution: "npm-registry-fetch@npm:18.0.2"
  dependencies:
    "@npmcli/redact": ^3.0.0
    jsonparse: ^1.3.1
    make-fetch-happen: ^14.0.0
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minizlib: ^3.0.1
    npm-package-arg: ^12.0.0
    proc-log: ^5.0.0
  checksum: 99d11962674f56ebf2e3a4623e486ec45db6cbc2bc3e1678afb3fbe0fe827ab668aeb04ee3e5aea0534e293a6ac98d01fd5a15dab8a3647e36c9c34342ff5211
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"npm-run-path@npm:^5.1.0":
  version: 5.1.0
  resolution: "npm-run-path@npm:5.1.0"
  dependencies:
    path-key: ^4.0.0
  checksum: dc184eb5ec239d6a2b990b43236845332ef12f4e0beaa9701de724aa797fe40b6bbd0157fb7639d24d3ab13f5d5cf22d223a19c6300846b8126f335f788bee66
  languageName: node
  linkType: hard

"npm-run-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "npm-run-path@npm:6.0.0"
  dependencies:
    path-key: ^4.0.0
    unicorn-magic: ^0.3.0
  checksum: 1a1b50aba6e6af7fd34a860ba2e252e245c4a59b316571a990356417c0cdf0414cabf735f7f52d9c330899cb56f0ab804a8e21fb12a66d53d7843e39ada4a3b6
  languageName: node
  linkType: hard

"npm-user-validate@npm:^3.0.0":
  version: 3.0.0
  resolution: "npm-user-validate@npm:3.0.0"
  checksum: b7baf5615c999b95e53622b4303df9b6b9cd01954df18713b4ec8246fa30ecfc23358da535353b649ab284ab9c9dd5b40dab41224e273935992f995ebe1bfa22
  languageName: node
  linkType: hard

"npm@npm:^10.5.0":
  version: 10.9.2
  resolution: "npm@npm:10.9.2"
  dependencies:
    "@isaacs/string-locale-compare": ^1.1.0
    "@npmcli/arborist": ^8.0.0
    "@npmcli/config": ^9.0.0
    "@npmcli/fs": ^4.0.0
    "@npmcli/map-workspaces": ^4.0.2
    "@npmcli/package-json": ^6.1.0
    "@npmcli/promise-spawn": ^8.0.2
    "@npmcli/redact": ^3.0.0
    "@npmcli/run-script": ^9.0.1
    "@sigstore/tuf": ^3.0.0
    abbrev: ^3.0.0
    archy: ~1.0.0
    cacache: ^19.0.1
    chalk: ^5.3.0
    ci-info: ^4.1.0
    cli-columns: ^4.0.0
    fastest-levenshtein: ^1.0.16
    fs-minipass: ^3.0.3
    glob: ^10.4.5
    graceful-fs: ^4.2.11
    hosted-git-info: ^8.0.2
    ini: ^5.0.0
    init-package-json: ^7.0.2
    is-cidr: ^5.1.0
    json-parse-even-better-errors: ^4.0.0
    libnpmaccess: ^9.0.0
    libnpmdiff: ^7.0.0
    libnpmexec: ^9.0.0
    libnpmfund: ^6.0.0
    libnpmhook: ^11.0.0
    libnpmorg: ^7.0.0
    libnpmpack: ^8.0.0
    libnpmpublish: ^10.0.1
    libnpmsearch: ^8.0.0
    libnpmteam: ^7.0.0
    libnpmversion: ^7.0.0
    make-fetch-happen: ^14.0.3
    minimatch: ^9.0.5
    minipass: ^7.1.1
    minipass-pipeline: ^1.2.4
    ms: ^2.1.2
    node-gyp: ^11.0.0
    nopt: ^8.0.0
    normalize-package-data: ^7.0.0
    npm-audit-report: ^6.0.0
    npm-install-checks: ^7.1.1
    npm-package-arg: ^12.0.0
    npm-pick-manifest: ^10.0.0
    npm-profile: ^11.0.1
    npm-registry-fetch: ^18.0.2
    npm-user-validate: ^3.0.0
    p-map: ^4.0.0
    pacote: ^19.0.1
    parse-conflict-json: ^4.0.0
    proc-log: ^5.0.0
    qrcode-terminal: ^0.12.0
    read: ^4.0.0
    semver: ^7.6.3
    spdx-expression-parse: ^4.0.0
    ssri: ^12.0.0
    supports-color: ^9.4.0
    tar: ^6.2.1
    text-table: ~0.2.0
    tiny-relative-date: ^1.3.0
    treeverse: ^3.0.0
    validate-npm-package-name: ^6.0.0
    which: ^5.0.0
    write-file-atomic: ^6.0.0
  bin:
    npm: bin/npm-cli.js
    npx: bin/npx-cli.js
  checksum: 38bfc63f64610f2b8d879719b102827e0423a13291cfda0196b3ea25bfbe01518d1f9aa24ec44beb462769e1bbc6ab854ca0449def21e94008c8d582b7b7866f
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.12.0, object-inspect@npm:^1.12.3, object-inspect@npm:^1.9.0":
  version: 1.12.3
  resolution: "object-inspect@npm:1.12.3"
  checksum: dabfd824d97a5f407e6d5d24810d888859f6be394d8b733a77442b277e0808860555176719c5905e765e3743a7cada6b8b0a3b85e5331c530fd418cc8ae991db
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4":
  version: 4.1.4
  resolution: "object.assign@npm:4.1.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    has-symbols: ^1.0.3
    object-keys: ^1.1.1
  checksum: 76cab513a5999acbfe0ff355f15a6a125e71805fcf53de4e9d4e082e1989bdb81d1e329291e1e4e0ae7719f0e4ef80e88fb2d367ae60500d79d25a6224ac8864
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.6":
  version: 1.1.6
  resolution: "object.entries@npm:1.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 0f8c47517e6a9a980241eafe3b73de11e59511883173c2b93d67424a008e47e11b77c80e431ad1d8a806f6108b225a1cab9223e53e555776c612a24297117d28
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.6":
  version: 2.0.6
  resolution: "object.fromentries@npm:2.0.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 453c6d694180c0c30df451b60eaf27a5b9bca3fb43c37908fd2b78af895803dc631242bcf05582173afa40d8d0e9c96e16e8874b39471aa53f3ac1f98a085d85
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.0":
  version: 1.0.0
  resolution: "object.groupby@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.21.2
    get-intrinsic: ^1.2.1
  checksum: 64b00b287d57580111c958e7ff375c9b61811fa356f2cf0d35372d43cab61965701f00fac66c19fd8f49c4dfa28744bee6822379c69a73648ad03e09fcdeae70
  languageName: node
  linkType: hard

"object.hasown@npm:^1.1.2":
  version: 1.1.2
  resolution: "object.hasown@npm:1.1.2"
  dependencies:
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: b936572536db0cdf38eb30afd2f1026a8b6f2cc5d2c4497c9d9bbb01eaf3e980dead4fd07580cfdd098e6383e5a9db8212d3ea0c6bdd2b5e68c60aa7e3b45566
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6":
  version: 1.1.6
  resolution: "object.values@npm:1.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: f6fff9fd817c24cfd8107f50fb33061d81cd11bacc4e3dbb3852e9ff7692fde4dbce823d4333ea27cd9637ef1b6690df5fbb61f1ed314fa2959598dc3ae23d8e
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"onetime@npm:^6.0.0":
  version: 6.0.0
  resolution: "onetime@npm:6.0.0"
  dependencies:
    mimic-fn: ^4.0.0
  checksum: 0846ce78e440841335d4e9182ef69d5762e9f38aa7499b19f42ea1c4cd40f0b4446094c455c713f9adac3f4ae86f613bb5e30c99e52652764d06a89f709b3788
  languageName: node
  linkType: hard

"open@npm:^9.1.0":
  version: 9.1.0
  resolution: "open@npm:9.1.0"
  dependencies:
    default-browser: ^4.0.0
    define-lazy-prop: ^3.0.0
    is-inside-container: ^1.0.0
    is-wsl: ^2.2.0
  checksum: 3993c0f61d51fed8ac290e99c9c3cf45d3b6cfb3e2aa2b74cafd312c3486c22fd81df16ac8f3ab91dd8a4e3e729a16fc2480cfc406c4833416cf908acf1ae7c9
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.3
  resolution: "optionator@npm:0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap": ^1.2.3
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
  checksum: 09281999441f2fe9c33a5eeab76700795365a061563d66b098923eb719251a42bdbe432790d35064d0816ead9296dbeb1ad51a733edf4167c96bd5d0882e428a
  languageName: node
  linkType: hard

"p-each-series@npm:^2.1.0":
  version: 2.2.0
  resolution: "p-each-series@npm:2.2.0"
  checksum: 5fbe2f1f1966f55833bd401fe36f7afe410707d5e9fb6032c6dde8aa716d50521c3bb201fdb584130569b5941d5e84993e09e0b3f76a474288e0ede8f632983c
  languageName: node
  linkType: hard

"p-each-series@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-each-series@npm:3.0.0"
  checksum: e61b76cf94ddf9766a97698f103d1e3901f118e03a275f5f7bc46f828679a672c2b2a4e74657396a7ba98e80677b2cd7f8ce107950054cad88103848702cac9b
  languageName: node
  linkType: hard

"p-filter@npm:^4.0.0":
  version: 4.1.0
  resolution: "p-filter@npm:4.1.0"
  dependencies:
    p-map: ^7.0.1
  checksum: a8c783f6f783d2cf2b1b23f128576abee9545942961d1a242d0bb673eaf5390e51acd887d526e468d23fb08546ba7c958222464e75a25ac502f2951aeffcbb72
  languageName: node
  linkType: hard

"p-is-promise@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-is-promise@npm:3.0.0"
  checksum: 74e511225fde5eeda7a120d51c60c284de90d68dec7c73611e7e59e8d1c44cc7e2246686544515849149b74ed0571ad470a456ac0d00314f8d03d2cc1ad43aae
  languageName: node
  linkType: hard

"p-limit@npm:^1.1.0":
  version: 1.3.0
  resolution: "p-limit@npm:1.3.0"
  dependencies:
    p-try: ^1.0.0
  checksum: 281c1c0b8c82e1ac9f81acd72a2e35d402bf572e09721ce5520164e9de07d8274451378a3470707179ad13240535558f4b277f02405ad752e08c7d5b0d54fbfd
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0, p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2, p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^2.0.0":
  version: 2.0.0
  resolution: "p-locate@npm:2.0.0"
  dependencies:
    p-limit: ^1.1.0
  checksum: e2dceb9b49b96d5513d90f715780f6f4972f46987dc32a0e18bc6c3fc74a1a5d73ec5f81b1398af5e58b99ea1ad03fd41e9181c01fa81b4af2833958696e3081
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: ^2.0.0
  checksum: 83991734a9854a05fe9dbb29f707ea8a0599391f52daac32b86f08e21415e857ffa60f0e120bfe7ce0cc4faf9274a50239c7895fc0d0579d08411e513b83a4ae
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^2.1.0":
  version: 2.1.0
  resolution: "p-map@npm:2.1.0"
  checksum: 9e3ad3c9f6d75a5b5661bcad78c91f3a63849189737cd75e4f1225bf9ac205194e5c44aac2ef6f09562b1facdb9bd1425584d7ac375bfaa17b3f1a142dab936d
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-map@npm:^7.0.1, p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"p-reduce@npm:^2.0.0":
  version: 2.1.0
  resolution: "p-reduce@npm:2.1.0"
  checksum: 99b26d36066a921982f25c575e78355824da0787c486e3dd9fc867460e8bf17d5fb3ce98d006b41bdc81ffc0aa99edf5faee53d11fe282a20291fb721b0cb1c7
  languageName: node
  linkType: hard

"p-reduce@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-reduce@npm:3.0.0"
  checksum: 387de355e906c07159d5e6270f3b58b7c7c7349ec7294ba0a9cff2a2e2faa8c602b841b079367685d3fa166a3ee529db7aaa73fadc936987c35e90f0ba64d955
  languageName: node
  linkType: hard

"p-try@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-try@npm:1.0.0"
  checksum: 3b5303f77eb7722144154288bfd96f799f8ff3e2b2b39330efe38db5dd359e4fb27012464cd85cb0a76e9b7edd1b443568cb3192c22e7cffc34989df0bafd605
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"pacote@npm:^19.0.0, pacote@npm:^19.0.1":
  version: 19.0.1
  resolution: "pacote@npm:19.0.1"
  dependencies:
    "@npmcli/git": ^6.0.0
    "@npmcli/installed-package-contents": ^3.0.0
    "@npmcli/package-json": ^6.0.0
    "@npmcli/promise-spawn": ^8.0.0
    "@npmcli/run-script": ^9.0.0
    cacache: ^19.0.0
    fs-minipass: ^3.0.0
    minipass: ^7.0.2
    npm-package-arg: ^12.0.0
    npm-packlist: ^9.0.0
    npm-pick-manifest: ^10.0.0
    npm-registry-fetch: ^18.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    sigstore: ^3.0.0
    ssri: ^12.0.0
    tar: ^6.1.11
  bin:
    pacote: bin/index.js
  checksum: 860f8c54c7fa40d2340810b0219040a283944a382e41344e4493623fa3d0cb1129331da2570126e073659af953542e37bf774f29e7fda7a561288315a1651746
  languageName: node
  linkType: hard

"pacote@npm:^20.0.0":
  version: 20.0.0
  resolution: "pacote@npm:20.0.0"
  dependencies:
    "@npmcli/git": ^6.0.0
    "@npmcli/installed-package-contents": ^3.0.0
    "@npmcli/package-json": ^6.0.0
    "@npmcli/promise-spawn": ^8.0.0
    "@npmcli/run-script": ^9.0.0
    cacache: ^19.0.0
    fs-minipass: ^3.0.0
    minipass: ^7.0.2
    npm-package-arg: ^12.0.0
    npm-packlist: ^9.0.0
    npm-pick-manifest: ^10.0.0
    npm-registry-fetch: ^18.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    sigstore: ^3.0.0
    ssri: ^12.0.0
    tar: ^6.1.11
  bin:
    pacote: bin/index.js
  checksum: 6fc395b579799da4bafa1d1b309df03a0b2540dfb29c312ee17e60afdec872d4da11398fc2be081184c0b73def935bb5ebf57b193623926ec2e502e4b98fe6ea
  languageName: node
  linkType: hard

"pako@npm:~1.0.2":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 1be2bfa1f807608c7538afa15d6f25baa523c30ec870a3228a89579e474a4d992f4293859524e46d5d87fd30fa17c5edf34dbef0671251d9749820b488660b16
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-conflict-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-conflict-json@npm:4.0.0"
  dependencies:
    json-parse-even-better-errors: ^4.0.0
    just-diff: ^6.0.0
    just-diff-apply: ^5.2.0
  checksum: ee4e1da52a54a127460713c82a12fffc1071dd2945350ebd9e203337b944901d086d515f7b15f42c6c5d9cf031de76eae0ebf5338fe8339d5695a7882d0aeba9
  languageName: node
  linkType: hard

"parse-entities@npm:^2.0.0":
  version: 2.0.0
  resolution: "parse-entities@npm:2.0.0"
  dependencies:
    character-entities: ^1.0.0
    character-entities-legacy: ^1.0.0
    character-reference-invalid: ^1.0.0
    is-alphanumerical: ^1.0.0
    is-decimal: ^1.0.0
    is-hexadecimal: ^1.0.0
  checksum: 7addfd3e7d747521afac33c8121a5f23043c6973809756920d37e806639b4898385d386fcf4b3c8e2ecf1bc28aac5ae97df0b112d5042034efbe80f44081ebce
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: ^1.3.1
    json-parse-better-errors: ^1.0.1
  checksum: 0fe227d410a61090c247e34fa210552b834613c006c2c64d9a05cfe9e89cf8b4246d1246b1a99524b53b313e9ac024438d0680f67e33eaed7e6f38db64cfe7b5
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0, parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse-json@npm:^7.0.0":
  version: 7.1.1
  resolution: "parse-json@npm:7.1.1"
  dependencies:
    "@babel/code-frame": ^7.21.4
    error-ex: ^1.3.2
    json-parse-even-better-errors: ^3.0.0
    lines-and-columns: ^2.0.3
    type-fest: ^3.8.0
  checksum: 187275c7ac097dcfb3c7420bca2399caa4da33bcd5d5aac3604bda0e2b8eee4df61cc26aa0d79fab97f0d67bf42d41d332baa9f9f56ad27636ad785f1ae639e5
  languageName: node
  linkType: hard

"parse-json@npm:^8.0.0":
  version: 8.1.0
  resolution: "parse-json@npm:8.1.0"
  dependencies:
    "@babel/code-frame": ^7.22.13
    index-to-position: ^0.1.2
    type-fest: ^4.7.1
  checksum: efc4256c91e835b1340e2b4f535272247f174fcba85eead15ff938be23b3ca2d521a04c76e564d1dc2f61c0c9ebcb6157d5433d459c7e736c81d014b49577b31
  languageName: node
  linkType: hard

"parse-ms@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-ms@npm:4.0.0"
  checksum: 673c801d9f957ff79962d71ed5a24850163f4181a90dd30c4e3666b3a804f53b77f1f0556792e8b2adbb5d58757907d1aa51d7d7dc75997c2a56d72937cbc8b7
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^6.0.0":
  version: 6.0.1
  resolution: "parse5-htmlparser2-tree-adapter@npm:6.0.1"
  dependencies:
    parse5: ^6.0.1
  checksum: 1848378b355d027915645c13f13f982e60502d201f53bc2067a508bf2dba4aac08219fc781dcd160167f5f50f0c73f58d20fa4fb3d90ee46762c20234fa90a6d
  languageName: node
  linkType: hard

"parse5@npm:^5.1.1":
  version: 5.1.1
  resolution: "parse5@npm:5.1.1"
  checksum: 613a714af4c1101d1cb9f7cece2558e35b9ae8a0c03518223a4a1e35494624d9a9ad5fad4c13eab66a0e0adccd9aa3d522fc8f5f9cc19789e0579f3fa0bdfc65
  languageName: node
  linkType: hard

"parse5@npm:^6.0.1":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 7d569a176c5460897f7c8f3377eff640d54132b9be51ae8a8fa4979af940830b2b0c296ce75e5bd8f4041520aadde13170dbdec44889975f906098ea0002f4bd
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 96e92643aa34b4b28d0de1cd2eba52a1c5313a90c6542d03f62750d82480e20bfa62bc865d5cfc6165f5fcd5aeb0851043c40a39be5989646f223300021bae0a
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 8e6c314ae6d16b83e93032c61020129f6f4484590a777eed709c4a01b50e498822b00f76ceaf94bc64dbd90b327df56ceadce27da3d83393790f1219e07721d7
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"path-type@npm:^5.0.0":
  version: 5.0.0
  resolution: "path-type@npm:5.0.0"
  checksum: 15ec24050e8932c2c98d085b72cfa0d6b4eeb4cbde151a0a05726d8afae85784fc5544f733d8dfc68536587d5143d29c0bd793623fad03d7e61cc00067291cd5
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 6cdcbc3567d5c412450c53261a3f10991665d660961e06605decf4544a61a97a54fefe70a68d5c37080ff9d6f4cf51444c90198d1ba9f9309a6c0d6e9f5c4fde
  languageName: node
  linkType: hard

"pkg-conf@npm:^2.1.0":
  version: 2.1.0
  resolution: "pkg-conf@npm:2.1.0"
  dependencies:
    find-up: ^2.0.0
    load-json-file: ^4.0.0
  checksum: b50775157262abd1bfb4d3d948f3fc6c009d10266c6507d4de296af4e2cbb6d2738310784432185886d83144466fbb286b6e8ff0bc23dc5ee7d81810dc6c4788
  languageName: node
  linkType: hard

"pkg-up@npm:^3.1.0":
  version: 3.1.0
  resolution: "pkg-up@npm:3.1.0"
  dependencies:
    find-up: ^3.0.0
  checksum: 5bac346b7c7c903613c057ae3ab722f320716199d753f4a7d053d38f2b5955460f3e6ab73b4762c62fd3e947f58e04f1343e92089e7bb6091c90877406fcd8c8
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.1.2":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: ce9440fc42a5419d103f4c7c1847cb75488f3ac9cbe81093b408ee9701193a509f664b4d10a2b4d82c694ee7495e022f8f482d254f92b7ffd9ed9dea696c6f84
  languageName: node
  linkType: hard

"postcss@npm:8.4.31":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: 1d8611341b073143ad90486fcdfeab49edd243377b1f51834dc4f6d028e82ce5190e4f11bb2633276864503654fb7cab28e67abdc0fbf9d1f88cad4a0ff0beea
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: ^1.1.2
  checksum: 00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier@npm:^3.0.0":
  version: 3.0.0
  resolution: "prettier@npm:3.0.0"
  bin:
    prettier: bin/prettier.cjs
  checksum: 6a832876a1552dc58330d2467874e5a0b46b9ccbfc5d3531eb69d15684743e7f83dc9fbd202db6270446deba9c82b79d24383d09924c462b457136a759425e33
  languageName: node
  linkType: hard

"pretty-ms@npm:^9.0.0":
  version: 9.2.0
  resolution: "pretty-ms@npm:9.2.0"
  dependencies:
    parse-ms: ^4.0.0
  checksum: d3a5a5b1c8a3417f64a877dba5ee2bacee404b59bc12083466e5e6dce2745e4bd716e1f9860624c7dceb1b4a532e808e4f2a7a03903a132344b3818951e2d125
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"proggy@npm:^3.0.0":
  version: 3.0.0
  resolution: "proggy@npm:3.0.0"
  checksum: 8c274b56e8eaaa1f59ea938df3c501d80d24fbfd3dffd1de42a66e2657d131f5e0b7165127457a3a7b38e1dcc71a81060a685c6840001136ecad36cec900bfc8
  languageName: node
  linkType: hard

"promise-all-reject-late@npm:^1.0.0":
  version: 1.0.1
  resolution: "promise-all-reject-late@npm:1.0.1"
  checksum: d7d61ac412352e2c8c3463caa5b1c3ca0f0cc3db15a09f180a3da1446e33d544c4261fc716f772b95e4c27d559cfd2388540f44104feb356584f9c73cfb9ffcb
  languageName: node
  linkType: hard

"promise-call-limit@npm:^3.0.1":
  version: 3.0.2
  resolution: "promise-call-limit@npm:3.0.2"
  checksum: e1e2d57658bd57574959bd89733958f4e6940a6a5788d2f380a81f62f5660f88f93a7dd9f9eb3d09dc7c4927387e25c00ca941a3bdfce8fb050987d2d0ffe59a
  languageName: node
  linkType: hard

"promise-inflight@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-inflight@npm:1.0.1"
  checksum: 22749483091d2c594261517f4f80e05226d4d5ecc1fc917e1886929da56e22b5718b7f2a75f3807e7a7d471bc3be2907fe92e6e8f373ddf5c64bae35b5af3981
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"promzard@npm:^2.0.0":
  version: 2.0.0
  resolution: "promzard@npm:2.0.0"
  dependencies:
    read: ^4.0.0
  checksum: 599ccf47b82df7b01dbef0fe833350436a9762c92237a684525733918179e7ae36151218d6a51d36f9cfffb83966d553cf1308de443836cf97d8be13fda1f57e
  languageName: node
  linkType: hard

"prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"proto-list@npm:~1.2.1":
  version: 1.2.4
  resolution: "proto-list@npm:1.2.4"
  checksum: 4d4826e1713cbfa0f15124ab0ae494c91b597a3c458670c9714c36e8baddf5a6aad22842776f2f5b137f259c8533e741771445eb8df82e861eea37a6eaba03f7
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.1.1
  resolution: "punycode@npm:2.1.1"
  checksum: 823bf443c6dd14f669984dea25757b37993f67e8d94698996064035edd43bed8a5a17a9f12e439c2b35df1078c6bec05a6c86e336209eb1061e8025c481168e8
  languageName: node
  linkType: hard

"q@npm:^1.5.1":
  version: 1.5.1
  resolution: "q@npm:1.5.1"
  checksum: 147baa93c805bc1200ed698bdf9c72e9e42c05f96d007e33a558b5fdfd63e5ea130e99313f28efc1783e90e6bdb4e48b67a36fcc026b7b09202437ae88a1fb12
  languageName: node
  linkType: hard

"qrcode-terminal@npm:^0.12.0":
  version: 0.12.0
  resolution: "qrcode-terminal@npm:0.12.0"
  bin:
    qrcode-terminal: ./bin/qrcode-terminal.js
  checksum: 51638d11d080e06ef79ef2d5cfe911202159e48d2873d6a80a3c5489b4b767acf4754811ceba4e113db8f41f61a06c163bcb17e6e18e6b34e04a7a5155dac974
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"quick-lru@npm:^4.0.1":
  version: 4.0.1
  resolution: "quick-lru@npm:4.0.1"
  checksum: bea46e1abfaa07023e047d3cf1716a06172c4947886c053ede5c50321893711577cb6119360f810cc3ffcd70c4d7db4069c3cee876b358ceff8596e062bd1154
  languageName: node
  linkType: hard

"ramda@npm:^0.27.2":
  version: 0.27.2
  resolution: "ramda@npm:0.27.2"
  checksum: 28d6735dd1eea1a796c56cf6111f3673c6105bbd736e521cdd7826c46a18eeff337c2dba4668f6eed990d539b9961fd6db19aa46ccc1530ba67a396c0a9f580d
  languageName: node
  linkType: hard

"rc@npm:^1.2.8":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: ^0.6.0
    ini: ~1.3.0
    minimist: ^1.2.0
    strip-json-comments: ~2.0.1
  bin:
    rc: ./cli.js
  checksum: 2e26e052f8be2abd64e6d1dabfbd7be03f80ec18ccbc49562d31f617d0015fbdbcf0f9eed30346ea6ab789e0fdfe4337f033f8016efdbee0df5354751842080e
  languageName: node
  linkType: hard

"react-dom@npm:^18.3.1":
  version: 18.3.1
  resolution: "react-dom@npm:18.3.1"
  dependencies:
    loose-envify: ^1.1.0
    scheduler: ^0.23.2
  peerDependencies:
    react: ^18.3.1
  checksum: 298954ecd8f78288dcaece05e88b570014d8f6dce5db6f66e6ee91448debeb59dcd31561dddb354eee47e6c1bb234669459060deb238ed0213497146e555a0b9
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react@npm:^18.3.1":
  version: 18.3.1
  resolution: "react@npm:18.3.1"
  dependencies:
    loose-envify: ^1.1.0
  checksum: a27bcfa8ff7c15a1e50244ad0d0c1cb2ad4375eeffefd266a64889beea6f6b64c4966c9b37d14ee32d6c9fcd5aa6ba183b6988167ab4d127d13e7cb5b386a376
  languageName: node
  linkType: hard

"read-cmd-shim@npm:^5.0.0":
  version: 5.0.0
  resolution: "read-cmd-shim@npm:5.0.0"
  checksum: 7b403e009373d0e441c4ed3364f791680c6846fb6d7c4041e5af2f4da45b07a0325c43c60b3066e16e567d2c3a37f1b6096ed0e93a7b5e575806df0b860ff308
  languageName: node
  linkType: hard

"read-package-json-fast@npm:^4.0.0":
  version: 4.0.0
  resolution: "read-package-json-fast@npm:4.0.0"
  dependencies:
    json-parse-even-better-errors: ^4.0.0
    npm-normalize-package-bin: ^4.0.0
  checksum: bf0becd7d0b652dcc5874b466d1dbd98313180e89505c072f35ff48a1ad6bdaf2427143301e1924d64e4af5064cda8be5df16f14de882f03130e29051bbaab87
  languageName: node
  linkType: hard

"read-package-up@npm:^11.0.0":
  version: 11.0.0
  resolution: "read-package-up@npm:11.0.0"
  dependencies:
    find-up-simple: ^1.0.0
    read-pkg: ^9.0.0
    type-fest: ^4.6.0
  checksum: 535b7554d47fae5fb5c2e7aceebd48b5de4142cdfe7b21f942fa9a0f56db03d3b53cce298e19438e1149292279c285e6ba6722eca741d590fd242519c4bdbc17
  languageName: node
  linkType: hard

"read-pkg-up@npm:^7.0.1":
  version: 7.0.1
  resolution: "read-pkg-up@npm:7.0.1"
  dependencies:
    find-up: ^4.1.0
    read-pkg: ^5.2.0
    type-fest: ^0.8.1
  checksum: e4e93ce70e5905b490ca8f883eb9e48b5d3cebc6cd4527c25a0d8f3ae2903bd4121c5ab9c5a3e217ada0141098eeb661313c86fa008524b089b8ed0b7f165e44
  languageName: node
  linkType: hard

"read-pkg@npm:^5.2.0":
  version: 5.2.0
  resolution: "read-pkg@npm:5.2.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.0
    normalize-package-data: ^2.5.0
    parse-json: ^5.0.0
    type-fest: ^0.6.0
  checksum: eb696e60528b29aebe10e499ba93f44991908c57d70f2d26f369e46b8b9afc208ef11b4ba64f67630f31df8b6872129e0a8933c8c53b7b4daf0eace536901222
  languageName: node
  linkType: hard

"read-pkg@npm:^8.0.0":
  version: 8.1.0
  resolution: "read-pkg@npm:8.1.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.1
    normalize-package-data: ^6.0.0
    parse-json: ^7.0.0
    type-fest: ^4.2.0
  checksum: f4cd164f096e78cf3e338a55f800043524e3055f9b0b826143290002fafc951025fc3cbd6ca683ebaf7945efcfb092d31c683dd252a7871a974662985c723b67
  languageName: node
  linkType: hard

"read-pkg@npm:^9.0.0":
  version: 9.0.1
  resolution: "read-pkg@npm:9.0.1"
  dependencies:
    "@types/normalize-package-data": ^2.4.3
    normalize-package-data: ^6.0.0
    parse-json: ^8.0.0
    type-fest: ^4.6.0
    unicorn-magic: ^0.1.0
  checksum: 5544bea2a58c6e5706db49a96137e8f0768c69395f25363f934064fbba00bdcdaa326fcd2f4281741df38cf81dbf27b76138240dc6de0ed718cf650475e0de3c
  languageName: node
  linkType: hard

"read@npm:^4.0.0":
  version: 4.0.0
  resolution: "read@npm:4.0.0"
  dependencies:
    mute-stream: ^2.0.0
  checksum: 01488ae8a73365879e946e5fbc32c3598ee79deb5b543e7e36c44d2e84185cd180e048871eb950a2166e10a3b38fda31033278576b77f1a2fbb3814522773cab
  languageName: node
  linkType: hard

"readable-stream@npm:3, readable-stream@npm:^3.0.0":
  version: 3.6.0
  resolution: "readable-stream@npm:3.6.0"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: d4ea81502d3799439bb955a3a5d1d808592cf3133350ed352aeaa499647858b27b1c4013984900238b0873ec8d0d8defce72469fb7a83e61d53f5ad61cb80dc8
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.0, readable-stream@npm:^2.0.2, readable-stream@npm:~2.3.6":
  version: 2.3.7
  resolution: "readable-stream@npm:2.3.7"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: e4920cf7549a60f8aaf694d483a0e61b2a878b969d224f89b3bc788b8d920075132c4b55a7494ee944c7b6a9a0eada28a7f6220d80b0312ece70bbf08eeca755
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: ^4.0.0
    strip-indent: ^3.0.0
  checksum: fa1ef20404a2d399235e83cc80bd55a956642e37dd197b4b612ba7327bf87fa32745aeb4a1634b2bab25467164ab4ed9c15be2c307923dd08b0fe7c52431ae6b
  languageName: node
  linkType: hard

"redis-commands@npm:1.7.0":
  version: 1.7.0
  resolution: "redis-commands@npm:1.7.0"
  checksum: d1ff7fbcb5e54768c77f731f1d49679d2a62c3899522c28addb4e2e5813aea8bcac3f22519d71d330224c3f2937f935dfc3d8dc65e90db0f5fe22dc2c1515aa7
  languageName: node
  linkType: hard

"redis-errors@npm:^1.0.0, redis-errors@npm:^1.2.0":
  version: 1.2.0
  resolution: "redis-errors@npm:1.2.0"
  checksum: f28ac2692113f6f9c222670735aa58aeae413464fd58ccf3fce3f700cae7262606300840c802c64f2b53f19f65993da24dc918afc277e9e33ac1ff09edb394f4
  languageName: node
  linkType: hard

"redis-parser@npm:^3.0.0":
  version: 3.0.0
  resolution: "redis-parser@npm:3.0.0"
  dependencies:
    redis-errors: ^1.0.0
  checksum: 89290ae530332f2ae37577647fa18208d10308a1a6ba750b9d9a093e7398f5e5253f19855b64c98757f7129cccce958e4af2573fdc33bad41405f87f1943459a
  languageName: node
  linkType: hard

"reflect-metadata@npm:^0.1.13":
  version: 0.1.13
  resolution: "reflect-metadata@npm:0.1.13"
  checksum: 798d379a7b6f6455501145419505c97dd11cbc23857a386add2b9ef15963ccf15a48d9d15507afe01d4cd74116df8a213247200bac00320bd7c11ddeaa5e8fb4
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.4.3, regexp.prototype.flags@npm:^1.5.0":
  version: 1.5.0
  resolution: "regexp.prototype.flags@npm:1.5.0"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    functions-have-names: ^1.2.3
  checksum: c541687cdbdfff1b9a07f6e44879f82c66bbf07665f9a7544c5fd16acdb3ec8d1436caab01662d2fbcad403f3499d49ab0b77fbc7ef29ef961d98cc4bc9755b4
  languageName: node
  linkType: hard

"registry-auth-token@npm:^5.0.0":
  version: 5.0.3
  resolution: "registry-auth-token@npm:5.0.3"
  dependencies:
    "@pnpm/npm-conf": ^2.1.0
  checksum: 5976f822d6a55267319b011dd4c64ef037ffee038c97529d09ea619835afe59cf89d545ec6b393098de4d7ba7a44664ce740bc1820215a2a90c7d66a1e676f9f
  languageName: node
  linkType: hard

"remark-gfm@npm:^1.0.0":
  version: 1.0.0
  resolution: "remark-gfm@npm:1.0.0"
  dependencies:
    mdast-util-gfm: ^0.1.0
    micromark-extension-gfm: ^0.3.0
  checksum: 877b0f6472a90a490b5d5a1393f46d22c4ab7451b1e83ebd7362e5be9c661b6ed03e76c28f76894f460bedf23345c589d3f412c273ce0d4d442c6a4d65b0eae4
  languageName: node
  linkType: hard

"remark-parse@npm:^9.0.0":
  version: 9.0.0
  resolution: "remark-parse@npm:9.0.0"
  dependencies:
    mdast-util-from-markdown: ^0.8.0
  checksum: 50104880549639b7dd7ae6f1e23c214915fe9c054f02f3328abdaee3f6de6d7282bf4357c3c5b106958fe75e644a3c248c2197755df34f9955e8e028fc74868f
  languageName: node
  linkType: hard

"remark-stringify@npm:^9.0.1":
  version: 9.0.1
  resolution: "remark-stringify@npm:9.0.1"
  dependencies:
    mdast-util-to-markdown: ^0.6.0
  checksum: 93f46076f4d96ab1946d13e7dd43e83088480ac6b1dfe05a65e2c2f0e33d1f52a50175199b464a81803fc0f5b3bf182037665f89720b30515eba37bec4d63d56
  languageName: node
  linkType: hard

"repeat-string@npm:^1.0.0":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 1b809fc6db97decdc68f5b12c4d1a671c8e3f65ec4a40c238bc5200e44e85bcc52a54f78268ab9c29fcf5fe4f1343e805420056d1f30fa9a9ee4c2d93e3cc6c0
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"resolve-from@npm:5.0.0, resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-global@npm:1.0.0, resolve-global@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-global@npm:1.0.0"
  dependencies:
    global-dirs: ^0.1.1
  checksum: c4e11d33e84bde7516b824503ffbe4b6cce863d5ce485680fd3db997b7c64da1df98321b1fd0703b58be8bc9bc83bc96bd83043f96194386b45eb47229efb6b6
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 1012afc566b3fdb190a6309cc37ef3b2dcc35dff5fa6683a9d00cd25c3247edfbc4691b91078c97adc82a29b77a2660c30d791d65dab4fc78bfc473f60289977
  languageName: node
  linkType: hard

"resolve@npm:^1.10.0, resolve@npm:^1.22.1, resolve@npm:^1.22.3":
  version: 1.22.3
  resolution: "resolve@npm:1.22.3"
  dependencies:
    is-core-module: ^2.12.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: fb834b81348428cb545ff1b828a72ea28feb5a97c026a1cf40aa1008352c72811ff4d4e71f2035273dc536dcfcae20c13604ba6283c612d70fa0b6e44519c374
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.4":
  version: 2.0.0-next.4
  resolution: "resolve@npm:2.0.0-next.4"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: c438ac9a650f2030fd074219d7f12ceb983b475da2d89ad3d6dd05fbf6b7a0a8cd37d4d10b43cb1f632bc19f22246ab7f36ebda54d84a29bfb2910a0680906d3
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.10.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.1#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.3#~builtin<compat/resolve>":
  version: 1.22.3
  resolution: "resolve@patch:resolve@npm%3A1.22.3#~builtin<compat/resolve>::version=1.22.3&hash=07638b"
  dependencies:
    is-core-module: ^2.12.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: ad59734723b596d0891321c951592ed9015a77ce84907f89c9d9307dd0c06e11a67906a3e628c4cae143d3e44898603478af0ddeb2bba3f229a9373efe342665
  languageName: node
  linkType: hard

"resolve@patch:resolve@^2.0.0-next.4#~builtin<compat/resolve>":
  version: 2.0.0-next.4
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.4#~builtin<compat/resolve>::version=2.0.0-next.4&hash=07638b"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 4bf9f4f8a458607af90518ff73c67a4bc1a38b5a23fef2bb0ccbd45e8be89820a1639b637b0ba377eb2be9eedfb1739a84cde24fe4cd670c8207d8fea922b011
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: f877dd8741796b909f2a82454ec111afb84eb45890eb49ac947d87991379406b3b83ff9673a46012fca0d7844bb989f45cc5b788254cf1a39b6b5a9659de0630
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rfdc@npm:^1.3.0":
  version: 1.3.0
  resolution: "rfdc@npm:1.3.0"
  checksum: fb2ba8512e43519983b4c61bd3fa77c0f410eff6bae68b08614437bc3f35f91362215f7b4a73cbda6f67330b5746ce07db5dd9850ad3edc91271ad6deea0df32
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: ^10.3.7
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 50e27388dd2b3fa6677385fc1e2966e9157c89c86853b96d02e6915663a96b7ff4d590e14f6f70e90f9b554093aa5dbc05ac3012876be558c06a65437337bc05
  languageName: node
  linkType: hard

"run-applescript@npm:^5.0.0":
  version: 5.0.0
  resolution: "run-applescript@npm:5.0.0"
  dependencies:
    execa: ^5.0.0
  checksum: d00c2dbfa5b2d774de7451194b8b125f40f65fc183de7d9dcae97f57f59433586d3c39b9001e111c38bfa24c3436c99df1bb4066a2a0c90d39a8c4cd6889af77
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rxjs@npm:^7.5.4":
  version: 7.5.4
  resolution: "rxjs@npm:7.5.4"
  dependencies:
    tslib: ^2.1.0
  checksum: 6f55f835f2543bc8214900f9e28b6320e6adc95875011fbca63e80a66eb18c9ff7cfdccb23b2180cbb6412762b98ed158c89fd51cb020799d127c66ea38c3c0e
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-array-concat@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.0
    has-symbols: ^1.0.3
    isarray: ^2.0.5
  checksum: f43cb98fe3b566327d0c09284de2b15fb85ae964a89495c1b1a5d50c7c8ed484190f4e5e71aacc167e16231940079b326f2c0807aea633d47cc7322f40a6b57f
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-regex-test@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.3
    is-regex: ^1.1.4
  checksum: bc566d8beb8b43c01b94e67de3f070fd2781685e835959bbbaaec91cc53381145ca91f69bd837ce6ec244817afa0a5e974fc4e40a2957f0aca68ac3add1ddd34
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.2":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 3e82d1f419e240ef6219d794ff29c7ee415fbdc19e038f680a10c067108e06284f1847450a210b29bbaf97b9d8a97ced5f624c31c681248ac84c80d56ad5a2c4
  languageName: node
  linkType: hard

"seedrandom@npm:^3.0.5":
  version: 3.0.5
  resolution: "seedrandom@npm:3.0.5"
  checksum: 728b56bc3bc1b9ddeabd381e449b51cb31bdc0aa86e27fcd0190cea8c44613d5bcb2f6bb63ed79f78180cbe791c20b8ec31a9627f7b7fc7f476fd2bdb7e2da9f
  languageName: node
  linkType: hard

"semantic-release-monorepo@npm:^8.0.2":
  version: 8.0.2
  resolution: "semantic-release-monorepo@npm:8.0.2"
  dependencies:
    debug: ^4.3.4
    execa: ^5.1.1
    file-url: ^3.0.0
    fs-extra: ^10.0.1
    get-stream: ^6.0.1
    git-log-parser: ^1.2.0
    p-each-series: ^2.1.0
    p-limit: ^3.1.0
    pkg-up: ^3.1.0
    ramda: ^0.27.2
    read-pkg: ^5.2.0
    semantic-release-plugin-decorators: ^4.0.0
    tempy: 1.0.1
  peerDependencies:
    semantic-release: ">=22.0.7"
  checksum: 777c3cf79e182168f764e118b4fe5201ec208634e15e3e6c95d59a522054c0bcc539526955c8b3c8a49a8592ac2848fc8ef82328b8abcc3bb671e573c51e2826
  languageName: node
  linkType: hard

"semantic-release-plugin-decorators@npm:^4.0.0":
  version: 4.0.0
  resolution: "semantic-release-plugin-decorators@npm:4.0.0"
  peerDependencies:
    semantic-release: ">20"
  checksum: 8feef5b24706569c84e2f07beb3fa50b2634207dac186978c7ea47853d3d99d63bd21bdf372508f8aae63424813e80f89d2cc0b1ed6062e7c83248f378044b75
  languageName: node
  linkType: hard

"semantic-release-slack-bot@npm:^4.0.2":
  version: 4.0.2
  resolution: "semantic-release-slack-bot@npm:4.0.2"
  dependencies:
    "@semantic-release/error": ^2.2.0
    micromatch: 4.0.2
    node-fetch: ^2.3.0
    slackify-markdown: ^4.3.0
  peerDependencies:
    semantic-release: ">=11.0.0"
  checksum: 9af9e2c47920358069d8cdb06f2881b434326a850cc0751bdad1fabfabee23a7462bc858e8fe95dd940f4fd6e8324bda52ccaf57d4bc4141ee8de6e515fe90d4
  languageName: node
  linkType: hard

"semantic-release-yarn@npm:^3.0.2":
  version: 3.0.2
  resolution: "semantic-release-yarn@npm:3.0.2"
  dependencies:
    "@semantic-release/error": ^4.0.0
    aggregate-error: ^5.0.0
    cosmiconfig: ^8.1.0
    execa: ^8.0.1
    fs-extra: ^11.1.0
    js-yaml: ^4.1.0
    lodash: ^4.17.21
    nerf-dart: ^1.0.0
    read-pkg: ^8.0.0
    semver: ^7.3.8
  peerDependencies:
    semantic-release: ">=19.0.0"
  checksum: ac89435f10891dc628e64422332871ede232428788470a0e086c92882b38d90122c0b3af353c28b4af3aeb6f2b4916429386fa0ce00abe4f803facad4c2313dd
  languageName: node
  linkType: hard

"semantic-release@npm:^24.2.1":
  version: 24.2.1
  resolution: "semantic-release@npm:24.2.1"
  dependencies:
    "@semantic-release/commit-analyzer": ^13.0.0-beta.1
    "@semantic-release/error": ^4.0.0
    "@semantic-release/github": ^11.0.0
    "@semantic-release/npm": ^12.0.0
    "@semantic-release/release-notes-generator": ^14.0.0-beta.1
    aggregate-error: ^5.0.0
    cosmiconfig: ^9.0.0
    debug: ^4.0.0
    env-ci: ^11.0.0
    execa: ^9.0.0
    figures: ^6.0.0
    find-versions: ^6.0.0
    get-stream: ^6.0.0
    git-log-parser: ^1.2.0
    hook-std: ^3.0.0
    hosted-git-info: ^8.0.0
    import-from-esm: ^2.0.0
    lodash-es: ^4.17.21
    marked: ^12.0.0
    marked-terminal: ^7.0.0
    micromatch: ^4.0.2
    p-each-series: ^3.0.0
    p-reduce: ^3.0.0
    read-package-up: ^11.0.0
    resolve-from: ^5.0.0
    semver: ^7.3.2
    semver-diff: ^4.0.0
    signale: ^1.2.1
    yargs: ^17.5.1
  bin:
    semantic-release: bin/semantic-release.js
  checksum: 295e2a8ab99a35763fb3c9e1ccce9aa3bc1e071a60b1ff1864566422f8ac216264172b68a891215b7e14fa9401c5227b55e4cd2dad39ef66fe3d1bd9120c41d4
  languageName: node
  linkType: hard

"semver-diff@npm:^4.0.0":
  version: 4.0.0
  resolution: "semver-diff@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 4a958d6f76c7e7858268e1e2cf936712542441c9e003e561b574167279eee0a9bd55cc7eae1bfb31d3e7ad06a9fc370e7dd412fcfefec8c0daf1ce5aea623559
  languageName: node
  linkType: hard

"semver-regex@npm:^4.0.5":
  version: 4.0.5
  resolution: "semver-regex@npm:4.0.5"
  checksum: b9e5c0573c4a997fb7e6e76321385d254797e86c8dba5e23f3cd8cf8f40b40414097a51514e5fead61dcb88ff10d3676355c01e2040f3c68f6c24bfd2073da2e
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:7.3.5":
  version: 7.3.5
  resolution: "semver@npm:7.3.5"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 5eafe6102bea2a7439897c1856362e31cc348ccf96efd455c8b5bc2c61e6f7e7b8250dc26b8828c1d76a56f818a7ee907a36ae9fb37a599d3d24609207001d60
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.1.1, semver@npm:^7.1.2, semver@npm:^7.3.2, semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.3.8, semver@npm:^7.5.2, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.3":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 586b825d36874007c9382d9e1ad8f93888d8670040add24a28e06a910aeebd673a2eb9e3bf169c6679d9245e66efb9057e0852e70d9daa6c27372aab1dda7104
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: c9a6f2c5b51a2dabdc0247db9c46460152ffc62ee139f3157440bd48e7c59425093f42719ac1d7931f054f153e2d26cf37dfeb8da17a794a58198a2705e527fd
  languageName: node
  linkType: hard

"shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: f4c1de0837f106d2dbbfd5d0720a5d059d1c66b42b580965c8f06bb1db684be8783538b684092648c981294bf817869f743a066538771dbecb293df78f765e00
  languageName: node
  linkType: hard

"sharp@npm:^0.33.5":
  version: 0.33.5
  resolution: "sharp@npm:0.33.5"
  dependencies:
    "@img/sharp-darwin-arm64": 0.33.5
    "@img/sharp-darwin-x64": 0.33.5
    "@img/sharp-libvips-darwin-arm64": 1.0.4
    "@img/sharp-libvips-darwin-x64": 1.0.4
    "@img/sharp-libvips-linux-arm": 1.0.5
    "@img/sharp-libvips-linux-arm64": 1.0.4
    "@img/sharp-libvips-linux-s390x": 1.0.4
    "@img/sharp-libvips-linux-x64": 1.0.4
    "@img/sharp-libvips-linuxmusl-arm64": 1.0.4
    "@img/sharp-libvips-linuxmusl-x64": 1.0.4
    "@img/sharp-linux-arm": 0.33.5
    "@img/sharp-linux-arm64": 0.33.5
    "@img/sharp-linux-s390x": 0.33.5
    "@img/sharp-linux-x64": 0.33.5
    "@img/sharp-linuxmusl-arm64": 0.33.5
    "@img/sharp-linuxmusl-x64": 0.33.5
    "@img/sharp-wasm32": 0.33.5
    "@img/sharp-win32-ia32": 0.33.5
    "@img/sharp-win32-x64": 0.33.5
    color: ^4.2.3
    detect-libc: ^2.0.3
    semver: ^7.6.3
  dependenciesMeta:
    "@img/sharp-darwin-arm64":
      optional: true
    "@img/sharp-darwin-x64":
      optional: true
    "@img/sharp-libvips-darwin-arm64":
      optional: true
    "@img/sharp-libvips-darwin-x64":
      optional: true
    "@img/sharp-libvips-linux-arm":
      optional: true
    "@img/sharp-libvips-linux-arm64":
      optional: true
    "@img/sharp-libvips-linux-s390x":
      optional: true
    "@img/sharp-libvips-linux-x64":
      optional: true
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
    "@img/sharp-linux-arm":
      optional: true
    "@img/sharp-linux-arm64":
      optional: true
    "@img/sharp-linux-s390x":
      optional: true
    "@img/sharp-linux-x64":
      optional: true
    "@img/sharp-linuxmusl-arm64":
      optional: true
    "@img/sharp-linuxmusl-x64":
      optional: true
    "@img/sharp-wasm32":
      optional: true
    "@img/sharp-win32-ia32":
      optional: true
    "@img/sharp-win32-x64":
      optional: true
  checksum: 04beae89910ac65c5f145f88de162e8466bec67705f497ace128de849c24d168993e016f33a343a1f3c30b25d2a90c3e62b017a9a0d25452371556f6cd2471e4
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.0
    get-intrinsic: ^1.0.2
    object-inspect: ^1.9.0
  checksum: 351e41b947079c10bd0858364f32bb3a7379514c399edb64ab3dce683933483fc63fb5e4efe0a15a2e8a7e3c436b6a91736ddb8d8c6591b0460a24bb4a1ee245
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1, signal-exit@npm:^4.1.0":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"signale@npm:^1.2.1":
  version: 1.4.0
  resolution: "signale@npm:1.4.0"
  dependencies:
    chalk: ^2.3.2
    figures: ^2.0.0
    pkg-conf: ^2.1.0
  checksum: a6a540e054096a1f4cf8b1f21fea62ca3e44a19faa63bd486723b736348609caab1fa59a87f16559de347dde8ae1fdebfc25a8b6723c88ae8239f176ffb0dda5
  languageName: node
  linkType: hard

"sigstore@npm:^3.0.0":
  version: 3.0.0
  resolution: "sigstore@npm:3.0.0"
  dependencies:
    "@sigstore/bundle": ^3.0.0
    "@sigstore/core": ^2.0.0
    "@sigstore/protobuf-specs": ^0.3.2
    "@sigstore/sign": ^3.0.0
    "@sigstore/tuf": ^3.0.0
    "@sigstore/verify": ^2.0.0
  checksum: 02fae890fce0ffc8e6cd572c0b69ce73ec7266b489be4585029df4b316b63326c73e3e1b8d42e220fd23aad39102f4228818bf4f7010a165e3d5598f96769ed7
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"skin-tone@npm:^2.0.0":
  version: 2.0.0
  resolution: "skin-tone@npm:2.0.0"
  dependencies:
    unicode-emoji-modifier-base: ^1.0.0
  checksum: 19de157586b8019cacc55eb25d9d640f00fc02415761f3e41a4527142970fd4e7f6af0333bc90e879858766c20a976107bb386ffd4c812289c01d51f2c8d182c
  languageName: node
  linkType: hard

"slackify-markdown@npm:^4.3.0":
  version: 4.3.1
  resolution: "slackify-markdown@npm:4.3.1"
  dependencies:
    mdast-util-to-markdown: ^0.6.2
    remark-gfm: ^1.0.0
    remark-parse: ^9.0.0
    remark-stringify: ^9.0.1
    unified: ^9.0.0
    unist-util-remove: ^2.0.1
    unist-util-visit: ^2.0.3
  checksum: 5a1658a442d7f44668c03e8c0b60b0cbf605bea001e1fe2e8d5e025621935eb1f78eb9bfc86ffcfe5ab653d43f3b5e7d95733050d708fd99cab63a8160fd32db
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slash@npm:^4.0.0":
  version: 4.0.0
  resolution: "slash@npm:4.0.0"
  checksum: da8e4af73712253acd21b7853b7e0dbba776b786e82b010a5bfc8b5051a1db38ed8aba8e1e8f400dd2c9f373be91eb1c42b66e91abb407ff42b10feece5e1d2d
  languageName: node
  linkType: hard

"slash@npm:^5.1.0":
  version: 5.1.0
  resolution: "slash@npm:5.1.0"
  checksum: 70434b34c50eb21b741d37d455110258c42d2cf18c01e6518aeb7299f3c6e626330c889c0c552b5ca2ef54a8f5a74213ab48895f0640717cacefeef6830a1ba4
  languageName: node
  linkType: hard

"slice-ansi@npm:^3.0.0":
  version: 3.0.0
  resolution: "slice-ansi@npm:3.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 5ec6d022d12e016347e9e3e98a7eb2a592213a43a65f1b61b74d2c78288da0aded781f665807a9f3876b9daa9ad94f64f77d7633a0458876c3a4fdc4eb223f24
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 4a82d7f085b0e1b070e004941ada3c40d3818563ac44766cca4ceadd2080427d337554f9f99a13aaeb3b4a94d9964d9466c807b3d7b7541d1ec37ee32d308756
  languageName: node
  linkType: hard

"slice-ansi@npm:^5.0.0":
  version: 5.0.0
  resolution: "slice-ansi@npm:5.0.0"
  dependencies:
    ansi-styles: ^6.0.0
    is-fullwidth-code-point: ^4.0.0
  checksum: 7e600a2a55e333a21ef5214b987c8358fe28bfb03c2867ff2cbf919d62143d1812ac27b4297a077fdaf27a03da3678e49551c93e35f9498a3d90221908a1180e
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 7a6b7f6eedf7482b9e4597d9a20e09505824208006ea8f2c49b71657427f3c137ca2ae662089baa73e1971c62322d535d9d0cf1c9235cf6f55e315c18203eadd
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.2":
  version: 1.0.2
  resolution: "source-map-js@npm:1.0.2"
  checksum: c049a7fc4deb9a7e9b481ae3d424cc793cb4845daa690bc5a05d428bf41bf231ced49b4cf0c9e77f9d42fdb3d20d6187619fc586605f5eabe995a316da8d377c
  languageName: node
  linkType: hard

"source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"spawn-error-forwarder@npm:~1.0.0":
  version: 1.0.0
  resolution: "spawn-error-forwarder@npm:1.0.0"
  checksum: ac7e69f980ce8dbcdd6323b7e30bc7dc6cbfcc7ebaefa63d71cb2150e153798f4ad20e5182f16137f1537fb8ecea386c3a1f241ade4711ef6c6e1f4a1bc971e5
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.1.1
  resolution: "spdx-correct@npm:3.1.1"
  dependencies:
    spdx-expression-parse: ^3.0.0
    spdx-license-ids: ^3.0.0
  checksum: 77ce438344a34f9930feffa61be0eddcda5b55fc592906ef75621d4b52c07400a97084d8701557b13f7d2aae0cb64f808431f469e566ef3fe0a3a131dcb775a6
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.3.0
  resolution: "spdx-exceptions@npm:2.3.0"
  checksum: cb69a26fa3b46305637123cd37c85f75610e8c477b6476fa7354eb67c08128d159f1d36715f19be6f9daf4b680337deb8c65acdcae7f2608ba51931540687ac0
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: a1c6e104a2cbada7a593eaa9f430bd5e148ef5290d4c0409899855ce8b1c39652bcc88a725259491a82601159d6dc790bedefc9016c7472f7de8de7361f8ccde
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^4.0.0":
  version: 4.0.0
  resolution: "spdx-expression-parse@npm:4.0.0"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: 936be681fbf5edeec3a79c023136479f70d6edb3fd3875089ac86cd324c6c8c81add47399edead296d1d0af17ae5ce88c7f88885eb150b62c2ff6e535841ca6a
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.11
  resolution: "spdx-license-ids@npm:3.0.11"
  checksum: 1da1acb090257773e60b022094050e810ae9fec874dc1461f65dc0400cd42dd830ab2df6e64fb49c2db3dce386dd0362110780e1b154db7c0bb413488836aaeb
  languageName: node
  linkType: hard

"split2@npm:^3.0.0":
  version: 3.2.2
  resolution: "split2@npm:3.2.2"
  dependencies:
    readable-stream: ^3.0.0
  checksum: 8127ddbedd0faf31f232c0e9192fede469913aa8982aa380752e0463b2e31c2359ef6962eb2d24c125bac59eeec76873678d723b1c7ff696216a1cd071e3994a
  languageName: node
  linkType: hard

"split2@npm:~1.0.0":
  version: 1.0.0
  resolution: "split2@npm:1.0.0"
  dependencies:
    through2: ~2.0.0
  checksum: 84cb1713a9b5ef7da06dbcb60780051f34a3b68f737a4bd5e807804ba742e3667f9e9e49eb589c1d7adb0bda4cf1eac9ea27a1040d480c785fc339c40b78396e
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"standard-as-callback@npm:^2.1.0":
  version: 2.1.0
  resolution: "standard-as-callback@npm:2.1.0"
  checksum: 88bec83ee220687c72d94fd86a98d5272c91d37ec64b66d830dbc0d79b62bfa6e47f53b71646011835fc9ce7fae62739545d13124262b53be4fbb3e2ebad551c
  languageName: node
  linkType: hard

"stream-combiner2@npm:~1.1.1":
  version: 1.1.1
  resolution: "stream-combiner2@npm:1.1.1"
  dependencies:
    duplexer2: ~0.1.0
    readable-stream: ^2.0.2
  checksum: dd32d179fa8926619c65471a7396fc638ec8866616c0b8747c4e05563ccdb0b694dd4e83cd799f1c52789c965a40a88195942b82b8cea2ee7a5536f1954060f9
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 1cce16cea8405d7a233d32ca5e00a00169cc0e19fbc02aa839959985f267335d435c07f96e5e0edd0eadc6d39c98d5435fb5bbbdefc62c41834eadc5622ad942
  languageName: node
  linkType: hard

"string-argv@npm:^0.3.1":
  version: 0.3.1
  resolution: "string-argv@npm:0.3.1"
  checksum: efbd0289b599bee808ce80820dfe49c9635610715429c6b7cc50750f0437e3c2f697c81e5c390208c13b5d5d12d904a1546172a88579f6ee5cbaaaa4dc9ec5cf
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.0, string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.8":
  version: 4.0.8
  resolution: "string.prototype.matchall@npm:4.0.8"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    get-intrinsic: ^1.1.3
    has-symbols: ^1.0.3
    internal-slot: ^1.0.3
    regexp.prototype.flags: ^1.4.3
    side-channel: ^1.0.4
  checksum: 952da3a818de42ad1c10b576140a5e05b4de7b34b8d9dbf00c3ac8c1293e9c0f533613a39c5cda53e0a8221f2e710bc2150e730b1c2278d60004a8a35726efb6
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.7":
  version: 1.2.7
  resolution: "string.prototype.trim@npm:1.2.7"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 05b7b2d6af63648e70e44c4a8d10d8cc457536df78b55b9d6230918bde75c5987f6b8604438c4c8652eb55e4fc9725d2912789eb4ec457d6995f3495af190c09
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.6":
  version: 1.0.6
  resolution: "string.prototype.trimend@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 0fdc34645a639bd35179b5a08227a353b88dc089adf438f46be8a7c197fc3f22f8514c1c9be4629b3cd29c281582730a8cbbad6466c60f76b5f99cf2addb132e
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.6":
  version: 1.0.6
  resolution: "string.prototype.trimstart@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 89080feef416621e6ef1279588994305477a7a91648d9436490d56010a1f7adc39167cddac7ce0b9884b8cdbef086987c4dcb2960209f2af8bac0d23ceff4f41
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.0.1
  resolution: "strip-ansi@npm:7.0.1"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 257f78fa433520e7f9897722731d78599cb3fce29ff26a20a5e12ba4957463b50a01136f37c43707f4951817a75e90820174853d6ccc240997adc5df8f966039
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-final-newline@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-final-newline@npm:3.0.0"
  checksum: 23ee263adfa2070cd0f23d1ac14e2ed2f000c9b44229aec9c799f1367ec001478469560abefd00c5c99ee6f0b31c137d53ec6029c53e9f32a93804e18c201050
  languageName: node
  linkType: hard

"strip-final-newline@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-final-newline@npm:4.0.0"
  checksum: b5fe48f695d74863153a3b3155220e6e9bf51f4447832998c8edec38e6559b3af87a9fe5ac0df95570a78a26f5fa91701358842eab3c15480e27980b154a145f
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: ^1.0.0
  checksum: 18f045d57d9d0d90cd16f72b2313d6364fd2cb4bf85b9f593523ad431c8720011a4d5f08b6591c9d580f446e78855c5334a30fb91aa1560f5d9f95ed1b4a0530
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.6":
  version: 5.1.6
  resolution: "styled-jsx@npm:5.1.6"
  dependencies:
    client-only: 0.0.1
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 879ad68e3e81adcf4373038aaafe55f968294955593660e173fbf679204aff158c59966716a60b29af72dc88795cfb2c479b6d2c3c87b2b2d282f3e27cc66461
  languageName: node
  linkType: hard

"super-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "super-regex@npm:1.0.0"
  dependencies:
    function-timeout: ^1.0.1
    time-span: ^5.1.0
  checksum: d99e90ee0950356b86b01ad327605080e72ee0712c7e5c66335e7e4e3bd2919206caea929fa2d5ca97c2afc1d1ab91466d09eadcf1101196edcfb94bebfea388
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^9.2.1, supports-color@npm:^9.4.0":
  version: 9.4.0
  resolution: "supports-color@npm:9.4.0"
  checksum: cb8ff8daeaf1db642156f69a9aa545b6c01dd9c4def4f90a49f46cbf24be0c245d392fcf37acd119cd1819b99dad2cc9b7e3260813f64bcfd7f5b18b5a1eefb8
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^3.1.0":
  version: 3.1.0
  resolution: "supports-hyperlinks@npm:3.1.0"
  dependencies:
    has-flag: ^4.0.0
    supports-color: ^7.0.0
  checksum: 051ffc31ae0d3334502decb6a17170ff89d870094d6835d93dfb2cda03e2a4504bf861a0954942af5e65fdd038b81cef5998696d0f4f4ff5f5bd3e40c7981874
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"swr@npm:^2.2.4":
  version: 2.2.4
  resolution: "swr@npm:2.2.4"
  dependencies:
    client-only: ^0.0.1
    use-sync-external-store: ^1.2.0
  peerDependencies:
    react: ^16.11.0 || ^17.0.0 || ^18.0.0
  checksum: d1398f89fd68af0e0cb6100a5769b1e17c0dbe8a778898643ad28456acda64add43344754c7d919e3f2ca82854adf86ff7d465aba25a2d555bcb669e457138f8
  languageName: node
  linkType: hard

"synckit@npm:^0.8.5":
  version: 0.8.5
  resolution: "synckit@npm:0.8.5"
  dependencies:
    "@pkgr/utils": ^2.3.1
    tslib: ^2.5.0
  checksum: 8a9560e5d8f3d94dc3cf5f7b9c83490ffa30d320093560a37b88f59483040771fd1750e76b9939abfbb1b5a23fd6dfbae77f6b338abffe7cae7329cd9b9bb86b
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.2.1":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: f1322768c9741a25356c11373bce918483f40fa9a25c69c59410c8a1247632487edef5fe76c5f12ac51a6356d2f1829e96d2bc34098668a2fc34d76050ac2b6c
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"temp-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: cc4f0404bf8d6ae1a166e0e64f3f409b423f4d1274d8c02814a59a5529f07db6cd070a749664141b992b2c1af337fa9bb451a460a43bb9bcddc49f235d3115aa
  languageName: node
  linkType: hard

"temp-dir@npm:^3.0.0":
  version: 3.0.0
  resolution: "temp-dir@npm:3.0.0"
  checksum: 577211e995d1d584dd60f1469351d45e8a5b4524e4a9e42d3bdd12cfde1d0bb8f5898311bef24e02aaafb69514c1feb58c7b4c33dcec7129da3b0861a4ca935b
  languageName: node
  linkType: hard

"tempy@npm:1.0.1":
  version: 1.0.1
  resolution: "tempy@npm:1.0.1"
  dependencies:
    del: ^6.0.0
    is-stream: ^2.0.0
    temp-dir: ^2.0.0
    type-fest: ^0.16.0
    unique-string: ^2.0.0
  checksum: e77ca4440af18e42dc64d8903b7ed0be673455b76680ff94a7d7c6ee7c16f7604bdcdee3c39436342b1082c23eda010dbe48f6094e836e0bd53c8b1aa63e5b95
  languageName: node
  linkType: hard

"tempy@npm:^3.0.0":
  version: 3.1.0
  resolution: "tempy@npm:3.1.0"
  dependencies:
    is-stream: ^3.0.0
    temp-dir: ^3.0.0
    type-fest: ^2.12.2
    unique-string: ^3.0.0
  checksum: c4ee8ce7700c6d0652f0828f15f7628e599e57f34352a7fe82abf8f1ebc36f10a5f83861b6c60cce55c321d8f7861d1fecbd9fb4c00de55bf460390bea42f7da
  languageName: node
  linkType: hard

"text-extensions@npm:^1.0.0":
  version: 1.9.0
  resolution: "text-extensions@npm:1.9.0"
  checksum: 56a9962c1b62d39b2bcb369b7558ca85c1b55e554b38dfd725edcc0a1babe5815782a60c17ff6b839093b163dfebb92b804208aaaea616ec7571c8059ae0cf44
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0, text-table@npm:~0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: ">= 3.1.0 < 4"
  checksum: dba7cc8a23a154cdcb6acb7f51d61511c37a6b077ec5ab5da6e8b874272015937788402fd271fdfc5f187f8cb0948e38d0a42dcc89d554d731652ab458f5343e
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: ^1.0.0
  checksum: 84e1b804bfec49f3531215f17b4a6e50fd4397b5f7c1bccc427b9c656e1ecfb13ea79d899930184f78bc2f57285c54d9a50a590c8868f4f0cef5c1d9f898b05e
  languageName: node
  linkType: hard

"through2@npm:^4.0.0":
  version: 4.0.2
  resolution: "through2@npm:4.0.2"
  dependencies:
    readable-stream: 3
  checksum: ac7430bd54ccb7920fd094b1c7ff3e1ad6edd94202e5528331253e5fde0cc56ceaa690e8df9895de2e073148c52dfbe6c4db74cacae812477a35660090960cc0
  languageName: node
  linkType: hard

"through2@npm:~2.0.0":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: ~2.3.6
    xtend: ~4.0.1
  checksum: beb0f338aa2931e5660ec7bf3ad949e6d2e068c31f4737b9525e5201b824ac40cac6a337224856b56bd1ddd866334bbfb92a9f57cd6f66bc3f18d3d86fc0fe50
  languageName: node
  linkType: hard

"through@npm:>=2.2.7 <3, through@npm:^2.3.8":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: a38c3e059853c494af95d50c072b83f8b676a9ba2818dcc5b108ef252230735c54e0185437618596c790bbba8fcdaef5b290405981ffa09dce67b1f1bf190cbd
  languageName: node
  linkType: hard

"time-span@npm:^5.1.0":
  version: 5.1.0
  resolution: "time-span@npm:5.1.0"
  dependencies:
    convert-hrtime: ^5.0.0
  checksum: 949c45fcb873f2d26fda3db1b7f7161ce65206f6e94a7c6c9bf3a5a07a373570dba57ca5c1f816efa6326adbc3f9e93bb6ef19a7a220f4259a917e1192d49418
  languageName: node
  linkType: hard

"tiny-relative-date@npm:^1.3.0":
  version: 1.3.0
  resolution: "tiny-relative-date@npm:1.3.0"
  checksum: 82a1fa2f3b00cd77c3ff0cf45380dad9e5befa8ee344d8de8076525efda4e6bd6af8f7f483e103b5834dc34bbed337fab7ac151f1d1a429a20f434a3744057b4
  languageName: node
  linkType: hard

"titleize@npm:^3.0.0":
  version: 3.0.0
  resolution: "titleize@npm:3.0.0"
  checksum: 71fbbeabbfb36ccd840559f67f21e356e1d03da2915b32d2ae1a60ddcc13a124be2739f696d2feb884983441d159a18649e8d956648d591bdad35c430a6b6d28
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"traverse@npm:~0.6.6":
  version: 0.6.6
  resolution: "traverse@npm:0.6.6"
  checksum: e2afa72f11efa9ba31ed763d2d9d2aa244612f22015d16c0ea3ba5f6ca8bf071de87f8108b721885cce06ea4a36ef4605d9228c67e431d9015ea4685cb364420
  languageName: node
  linkType: hard

"treeverse@npm:^3.0.0":
  version: 3.0.0
  resolution: "treeverse@npm:3.0.0"
  checksum: 73168d9887fa57b0719218f176c5a3cfbaaf310922879acb4adf76665bc17dcdb6ed3e4163f0c27eee17e346886186a1515ea6f87e96cdc10df1dce13bf622a0
  languageName: node
  linkType: hard

"trim-newlines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-newlines@npm:3.0.1"
  checksum: b530f3fadf78e570cf3c761fb74fef655beff6b0f84b29209bac6c9622db75ad1417f4a7b5d54c96605dcd72734ad44526fef9f396807b90839449eb543c6206
  languageName: node
  linkType: hard

"trough@npm:^1.0.0":
  version: 1.0.5
  resolution: "trough@npm:1.0.5"
  checksum: d6c8564903ed00e5258bab92134b020724dbbe83148dc72e4bf6306c03ed8843efa1bcc773fa62410dd89161ecb067432dd5916501793508a9506cacbc408e25
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "ts-api-utils@npm:1.0.1"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: 78794fc7270d295b36c1ac613465b5dc7e7226907a533125b30f177efef9dd630d4e503b00be31b44335eb2ebf9e136ebe97353f8fc5d383885d5fead9d54c09
  languageName: node
  linkType: hard

"ts-node@npm:^10.5.0":
  version: 10.5.0
  resolution: "ts-node@npm:10.5.0"
  dependencies:
    "@cspotcode/source-map-support": 0.7.0
    "@tsconfig/node10": ^1.0.7
    "@tsconfig/node12": ^1.0.7
    "@tsconfig/node14": ^1.0.0
    "@tsconfig/node16": ^1.0.2
    acorn: ^8.4.1
    acorn-walk: ^8.1.1
    arg: ^4.1.0
    create-require: ^1.1.0
    diff: ^4.0.1
    make-error: ^1.1.1
    v8-compile-cache-lib: ^3.0.0
    yn: 3.1.1
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: d51ac8a9b3582ce3705cef8d35f3372e40caa277dbd7c7baeb651961538f13d2f11f22402614348f78d9b10501bd1cb5f05ec4f2ec9a74bd0e288de769c32335
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.14.2":
  version: 3.14.2
  resolution: "tsconfig-paths@npm:3.14.2"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: a6162eaa1aed680537f93621b82399c7856afd10ec299867b13a0675e981acac4e0ec00896860480efc59fc10fd0b16fdc928c0b885865b52be62cadac692447
  languageName: node
  linkType: hard

"tsconfig-replace-paths@npm:^0.0.14":
  version: 0.0.14
  resolution: "tsconfig-replace-paths@npm:0.0.14"
  dependencies:
    commander: ^3.0.2
    globby: ^10.0.1
    json5: ^2.2.0
  bin:
    tsconfig-replace-paths: dist/commonjs/index.js
  checksum: 602b78bc36d884a53b0a10c7eed78454492a808a0e44c080103364112871b25eee00ac3aed2ab04b865858dc92d274fd0c515a258eafe85eb5fd09f1c6dd69cc
  languageName: node
  linkType: hard

"tslib@npm:^1.9.3":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.1.0, tslib@npm:^2.3.1, tslib@npm:^2.4.0, tslib@npm:^2.4.1, tslib@npm:^2.5.0, tslib@npm:^2.6.0, tslib@npm:^2.6.1, tslib@npm:^2.6.2, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: e4aba30e632b8c8902b47587fd13345e2827fa639e7c3121074d5ee0880723282411a8838f830b55100cbe4517672f84a2472667d355b81e8af165a55dc6203a
  languageName: node
  linkType: hard

"tuf-js@npm:^3.0.1":
  version: 3.0.1
  resolution: "tuf-js@npm:3.0.1"
  dependencies:
    "@tufjs/models": 3.0.1
    debug: ^4.3.6
    make-fetch-happen: ^14.0.1
  checksum: 5f7618c84ec60495b8bb2be7ae01811311c7fab591a373b6e45fc94d01b8a9e4de5c5c52125241edd4ecd9e1c0e5caddedb93a465690d3add0f7c40b2a10abbe
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-fest@npm:^0.16.0":
  version: 0.16.0
  resolution: "type-fest@npm:0.16.0"
  checksum: 1a4102c06dc109db00418c753062e206cab65befd469d000ece4452ee649bf2a9cf57686d96fb42326bc9d918d9a194d4452897b486dcc41989e5c99e4e87094
  languageName: node
  linkType: hard

"type-fest@npm:^0.18.0":
  version: 0.18.1
  resolution: "type-fest@npm:0.18.1"
  checksum: e96dcee18abe50ec82dab6cbc4751b3a82046da54c52e3b2d035b3c519732c0b3dd7a2fa9df24efd1a38d953d8d4813c50985f215f1957ee5e4f26b0fe0da395
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: e6b32a3b3877f04339bae01c193b273c62ba7bfc9e325b8703c4ee1b32dc8fe4ef5dfa54bf78265e069f7667d058e360ae0f37be5af9f153b22382cd55a9afe0
  languageName: node
  linkType: hard

"type-fest@npm:^0.6.0":
  version: 0.6.0
  resolution: "type-fest@npm:0.6.0"
  checksum: b2188e6e4b21557f6e92960ec496d28a51d68658018cba8b597bd3ef757721d1db309f120ae987abeeda874511d14b776157ff809f23c6d1ce8f83b9b2b7d60f
  languageName: node
  linkType: hard

"type-fest@npm:^0.8.1":
  version: 0.8.1
  resolution: "type-fest@npm:0.8.1"
  checksum: d61c4b2eba24009033ae4500d7d818a94fd6d1b481a8111612ee141400d5f1db46f199c014766b9fa9b31a6a7374d96fc748c6d688a78a3ce5a33123839becb7
  languageName: node
  linkType: hard

"type-fest@npm:^1.0.1":
  version: 1.4.0
  resolution: "type-fest@npm:1.4.0"
  checksum: b011c3388665b097ae6a109a437a04d6f61d81b7357f74cbcb02246f2f5bd72b888ae33631b99871388122ba0a87f4ff1c94078e7119ff22c70e52c0ff828201
  languageName: node
  linkType: hard

"type-fest@npm:^2.12.2":
  version: 2.19.0
  resolution: "type-fest@npm:2.19.0"
  checksum: a4ef07ece297c9fba78fc1bd6d85dff4472fe043ede98bd4710d2615d15776902b595abf62bd78339ed6278f021235fb28a96361f8be86ed754f778973a0d278
  languageName: node
  linkType: hard

"type-fest@npm:^3.8.0":
  version: 3.13.1
  resolution: "type-fest@npm:3.13.1"
  checksum: c06b0901d54391dc46de3802375f5579868949d71f93b425ce564e19a428a0d411ae8d8cb0e300d330071d86152c3ea86e744c3f2860a42a79585b6ec2fdae8e
  languageName: node
  linkType: hard

"type-fest@npm:^4.2.0, type-fest@npm:^4.6.0, type-fest@npm:^4.7.1":
  version: 4.33.0
  resolution: "type-fest@npm:4.33.0"
  checksum: 42c9a4e305ef86826f6c3e9fb0d230765523eba248b7927580e76fa0384a4a12dfcde3ba04ac94b3cfab664b16608f1f9b8fb6116a48c728b87350e8252fd32c
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-buffer@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.1
    is-typed-array: ^1.1.10
  checksum: 3e0281c79b2a40cd97fe715db803884301993f4e8c18e8d79d75fd18f796e8cd203310fec8c7fdb5e6c09bedf0af4f6ab8b75eb3d3a85da69328f28a80456bd3
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-byte-length@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    for-each: ^0.3.3
    has-proto: ^1.0.1
    is-typed-array: ^1.1.10
  checksum: b03db16458322b263d87a702ff25388293f1356326c8a678d7515767ef563ef80e1e67ce648b821ec13178dd628eb2afdc19f97001ceae7a31acf674c849af94
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-byte-offset@npm:1.0.0"
  dependencies:
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    for-each: ^0.3.3
    has-proto: ^1.0.1
    is-typed-array: ^1.1.10
  checksum: 04f6f02d0e9a948a95fbfe0d5a70b002191fae0b8fe0fe3130a9b2336f043daf7a3dda56a31333c35a067a97e13f539949ab261ca0f3692c41603a46a94e960b
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-length@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.2
    for-each: ^0.3.3
    is-typed-array: ^1.1.9
  checksum: 2228febc93c7feff142b8c96a58d4a0d7623ecde6c7a24b2b98eb3170e99f7c7eff8c114f9b283085cd59dcd2bd43aadf20e25bba4b034a53c5bb292f71f8956
  languageName: node
  linkType: hard

"typescript-service@npm:^2.0.3":
  version: 2.0.3
  resolution: "typescript-service@npm:2.0.3"
  dependencies:
    tslib: ^1.9.3
  checksum: 58f9ad5be4aa2a1419129bc829e5547858e87ba1aabb5f9c5504fea9ed17f8601706c795a782ffc99fb97fa82238e761a6a3adedb56786be90e937ebfe050e7b
  languageName: node
  linkType: hard

"typescript@npm:^4.4.3":
  version: 4.5.5
  resolution: "typescript@npm:4.5.5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 506f4c919dc8aeaafa92068c997f1d213b9df4d9756d0fae1a1e7ab66b585ab3498050e236113a1c9e57ee08c21ec6814ca7a7f61378c058d79af50a4b1f5a5e
  languageName: node
  linkType: hard

"typescript@npm:^5.1.6":
  version: 5.1.6
  resolution: "typescript@npm:5.1.6"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: b2f2c35096035fe1f5facd1e38922ccb8558996331405eb00a5111cc948b2e733163cc22fab5db46992aba7dd520fff637f2c1df4996ff0e134e77d3249a7350
  languageName: node
  linkType: hard

"typescript@patch:typescript@^4.4.3#~builtin<compat/typescript>":
  version: 4.5.5
  resolution: "typescript@patch:typescript@npm%3A4.5.5#~builtin<compat/typescript>::version=4.5.5&hash=493e53"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: c05c318d79c690f101d7ffb34cd6c7d6bbd884d3af9cefe7749ad0cd6be43c7082f098280982ca945dcba23fde34a08fed9602bb26540936baf8c0520727d3ba
  languageName: node
  linkType: hard

"typescript@patch:typescript@^5.1.6#~builtin<compat/typescript>":
  version: 5.1.6
  resolution: "typescript@patch:typescript@npm%3A5.1.6#~builtin<compat/typescript>::version=5.1.6&hash=493e53"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 21e88b0a0c0226f9cb9fd25b9626fb05b4c0f3fddac521844a13e1f30beb8f14e90bd409a9ac43c812c5946d714d6e0dee12d5d02dfc1c562c5aacfa1f49b606
  languageName: node
  linkType: hard

"uglify-js@npm:^3.1.4":
  version: 3.15.1
  resolution: "uglify-js@npm:3.15.1"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: cf88574ec8af4d69368142a3f9fb83ac11b1344a117dff08890fcf99ed12c782c810f02e71a0c2a7e8666ea6225894f1c171cbd90e1a1fe4b2c4a198f8ad61a3
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "unbox-primitive@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
    has-bigints: ^1.0.2
    has-symbols: ^1.0.3
    which-boxed-primitive: ^1.0.2
  checksum: b7a1cf5862b5e4b5deb091672ffa579aa274f648410009c81cca63fed3b62b610c4f3b773f912ce545bb4e31edc3138975b5bc777fc6e4817dca51affb6380e9
  languageName: node
  linkType: hard

"unfetch@npm:^4.2.0":
  version: 4.2.0
  resolution: "unfetch@npm:4.2.0"
  checksum: 6a4b2557e1d921eaa80c4425ce27a404945ec26491ed06e62598f333996a91a44c7908cb26dc7c2746d735762b13276cf4aa41829b4c8f438dde63add3045d7a
  languageName: node
  linkType: hard

"unicode-emoji-modifier-base@npm:^1.0.0":
  version: 1.0.0
  resolution: "unicode-emoji-modifier-base@npm:1.0.0"
  checksum: 6e1521d35fa69493207eb8b41f8edb95985d8b3faf07c01d820a1830b5e8403e20002563e2f84683e8e962a49beccae789f0879356bf92a4ec7a4dd8e2d16fdb
  languageName: node
  linkType: hard

"unicorn-magic@npm:^0.1.0":
  version: 0.1.0
  resolution: "unicorn-magic@npm:0.1.0"
  checksum: 48c5882ca3378f380318c0b4eb1d73b7e3c5b728859b060276e0a490051d4180966beeb48962d850fd0c6816543bcdfc28629dcd030bb62a286a2ae2acb5acb6
  languageName: node
  linkType: hard

"unicorn-magic@npm:^0.3.0":
  version: 0.3.0
  resolution: "unicorn-magic@npm:0.3.0"
  checksum: bdd7d7c522f9456f32a0b77af23f8854f9a7db846088c3868ec213f9550683ab6a2bdf3803577eacbafddb4e06900974385841ccb75338d17346ccef45f9cb01
  languageName: node
  linkType: hard

"unified@npm:^9.0.0":
  version: 9.2.2
  resolution: "unified@npm:9.2.2"
  dependencies:
    bail: ^1.0.0
    extend: ^3.0.0
    is-buffer: ^2.0.0
    is-plain-obj: ^2.0.0
    trough: ^1.0.0
    vfile: ^4.0.0
  checksum: 7c24461be7de4145939739ce50d18227c5fbdf9b3bc5a29dabb1ce26dd3e8bd4a1c385865f6f825f3b49230953ee8b591f23beab3bb3643e3e9dc37aa8a089d5
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"unique-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: ^2.0.0
  checksum: ef68f639136bcfe040cf7e3cd7a8dff076a665288122855148a6f7134092e6ed33bf83a7f3a9185e46c98dddc445a0da6ac25612afa1a7c38b8b654d6c02498e
  languageName: node
  linkType: hard

"unique-string@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-string@npm:3.0.0"
  dependencies:
    crypto-random-string: ^4.0.0
  checksum: 1a1e2e7d02eab1bb10f720475da735e1990c8a5ff34edd1a3b6bc31590cb4210b7a1233d779360cc622ce11c211e43afa1628dd658f35d3e6a89964b622940df
  languageName: node
  linkType: hard

"unist-util-is@npm:^4.0.0":
  version: 4.1.0
  resolution: "unist-util-is@npm:4.1.0"
  checksum: 726484cd2adc9be75a939aeedd48720f88294899c2e4a3143da413ae593f2b28037570730d5cf5fd910ff41f3bc1501e3d636b6814c478d71126581ef695f7ea
  languageName: node
  linkType: hard

"unist-util-remove@npm:^2.0.1":
  version: 2.1.0
  resolution: "unist-util-remove@npm:2.1.0"
  dependencies:
    unist-util-is: ^4.0.0
  checksum: 99e54f3ea0523f8cf957579a6e84e5b58427bffab929cc7f6aa5119581f929db683dd4691ea5483df0c272f486dda9dbd04f4ab74dca6cae1f3ebe8e4261a4d9
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^2.0.0":
  version: 2.0.3
  resolution: "unist-util-stringify-position@npm:2.0.3"
  dependencies:
    "@types/unist": ^2.0.2
  checksum: f755cadc959f9074fe999578a1a242761296705a7fe87f333a37c00044de74ab4b184b3812989a57d4cd12211f0b14ad397b327c3a594c7af84361b1c25a7f09
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^3.0.0":
  version: 3.1.1
  resolution: "unist-util-visit-parents@npm:3.1.1"
  dependencies:
    "@types/unist": ^2.0.0
    unist-util-is: ^4.0.0
  checksum: 1170e397dff88fab01e76d5154981666eb0291019d2462cff7a2961a3e76d3533b42eaa16b5b7e2d41ad42a5ea7d112301458283d255993e660511387bf67bc3
  languageName: node
  linkType: hard

"unist-util-visit@npm:^2.0.3":
  version: 2.0.3
  resolution: "unist-util-visit@npm:2.0.3"
  dependencies:
    "@types/unist": ^2.0.0
    unist-util-is: ^4.0.0
    unist-util-visit-parents: ^3.0.0
  checksum: 1fe19d500e212128f96d8c3cfa3312846e586b797748a1fd195fe6479f06bc90a6f6904deb08eefc00dd58e83a1c8a32fb8677252d2273ad7a5e624525b69b8f
  languageName: node
  linkType: hard

"universal-user-agent@npm:^7.0.0, universal-user-agent@npm:^7.0.2":
  version: 7.0.2
  resolution: "universal-user-agent@npm:7.0.2"
  checksum: 3f02cb6de0bb9fbaf379566bd0320d8e46af6e4358a2e88fce7e70687ed7b48b37f479d728bb22f4204a518e363f3038ac4841c033af1ee2253f6428a6c67e53
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.0
  resolution: "universalify@npm:2.0.0"
  checksum: 2406a4edf4a8830aa6813278bab1f953a8e40f2f63a37873ffa9a3bc8f9745d06cc8e88f3572cb899b7e509013f7f6fcc3e37e8a6d914167a5381d8440518c44
  languageName: node
  linkType: hard

"untildify@npm:^4.0.0":
  version: 4.0.0
  resolution: "untildify@npm:4.0.0"
  checksum: 39ced9c418a74f73f0a56e1ba4634b4d959422dff61f4c72a8e39f60b99380c1b45ed776fbaa0a4101b157e4310d873ad7d114e8534ca02609b4916bb4187fb9
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.11":
  version: 1.0.11
  resolution: "update-browserslist-db@npm:1.0.11"
  dependencies:
    escalade: ^3.1.1
    picocolors: ^1.0.0
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: b98327518f9a345c7cad5437afae4d2ae7d865f9779554baf2a200fdf4bac4969076b679b1115434bd6557376bdd37ca7583d0f9b8f8e302d7d4cc1e91b5f231
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url-join@npm:^5.0.0":
  version: 5.0.0
  resolution: "url-join@npm:5.0.0"
  checksum: 5921384a8ad4395b49ce4b50aa26efbc429cebe0bc8b3660ad693dd12fd859747b5369be0443e60e53a7850b2bc9d7d0687bcb94386662b40e743596bbf38101
  languageName: node
  linkType: hard

"use-debounce@npm:^10.0.1":
  version: 10.0.1
  resolution: "use-debounce@npm:10.0.1"
  peerDependencies:
    react: ">=16.8.0"
  checksum: 6b53d2b9b1856bacfa4dffd78234bba744792e1d75c194af0642faf1ac2914ebdcc770ac23b7e59ca999f997d969edf3c75d22362879818927d20d3408dd59db
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.2.0":
  version: 1.2.0
  resolution: "use-sync-external-store@npm:1.2.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 5c639e0f8da3521d605f59ce5be9e094ca772bd44a4ce7322b055a6f58eeed8dda3c94cabd90c7a41fb6fa852210092008afe48f7038792fd47501f33299116a
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 39931f6da74e307f51c0fb463dc2462807531dc80760a9bff1e35af4316131b4fc3203d16da60ae33f07fdca5b56f3f1dd662da0c99fea9aaeab2004780cc5f4
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.0":
  version: 3.0.0
  resolution: "v8-compile-cache-lib@npm:3.0.0"
  checksum: 674e312bbca796584b61dc915f33c7e7dc4e06d196e0048cb772c8964493a1ec723f1dd014d9419fd55c24a6eae148f60769da23f622e05cd13268063fa1ed6b
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1, validate-npm-package-license@npm:^3.0.4":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: ^3.0.0
    spdx-expression-parse: ^3.0.0
  checksum: 35703ac889d419cf2aceef63daeadbe4e77227c39ab6287eeb6c1b36a746b364f50ba22e88591f5d017bc54685d8137bc2d328d0a896e4d3fd22093c0f32a9ad
  languageName: node
  linkType: hard

"validate-npm-package-name@npm:^6.0.0":
  version: 6.0.0
  resolution: "validate-npm-package-name@npm:6.0.0"
  checksum: 4d018c4fa07f95534a5fea667adc653b1ef52f08bf56aff066c28394499d0a6949c0b00edbd7077c4dc1e041da9220af7c742ced67d7d2d6a1b07d10cbe91b29
  languageName: node
  linkType: hard

"vfile-message@npm:^2.0.0":
  version: 2.0.4
  resolution: "vfile-message@npm:2.0.4"
  dependencies:
    "@types/unist": ^2.0.0
    unist-util-stringify-position: ^2.0.0
  checksum: 1bade499790f46ca5aba04bdce07a1e37c2636a8872e05cf32c26becc912826710b7eb063d30c5754fdfaeedc8a7658e78df10b3bc535c844890ec8a184f5643
  languageName: node
  linkType: hard

"vfile@npm:^4.0.0":
  version: 4.2.1
  resolution: "vfile@npm:4.2.1"
  dependencies:
    "@types/unist": ^2.0.0
    is-buffer: ^2.0.0
    unist-util-stringify-position: ^2.0.0
    vfile-message: ^2.0.0
  checksum: ee5726e10d170472cde778fc22e0f7499caa096eb85babea5d0ce0941455b721037ee1c9e6ae506ca2803250acd313d0f464328ead0b55cfe7cb6315f1b462d6
  languageName: node
  linkType: hard

"walk-up-path@npm:^3.0.1":
  version: 3.0.1
  resolution: "walk-up-path@npm:3.0.1"
  checksum: 9ffca02fe30fb65f6db531260582988c5e766f4c739cf86a6109380a7f791236b5d0b92b1dce37a6f73e22dca6bc9d93bf3700413e16251b2bd6bbd1ca2be316
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-boxed-primitive@npm:1.0.2"
  dependencies:
    is-bigint: ^1.0.1
    is-boolean-object: ^1.1.0
    is-number-object: ^1.0.4
    is-string: ^1.0.5
    is-symbol: ^1.0.3
  checksum: 53ce774c7379071729533922adcca47220228405e1895f26673bbd71bdf7fb09bee38c1d6399395927c6289476b5ae0629863427fd151491b71c4b6cb04f3a5e
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.10, which-typed-array@npm:^1.1.11":
  version: 1.1.11
  resolution: "which-typed-array@npm:1.1.11"
  dependencies:
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.0
  checksum: 711ffc8ef891ca6597b19539075ec3e08bb9b4c2ca1f78887e3c07a977ab91ac1421940505a197758fb5939aa9524976d0a5bbcac34d07ed6faa75cedbb17206
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 2a44b2788165d0a3de71fd517d4880a8e20ea3a82c080ce46e294f0b68b69a2e49cff5f99c600e275c698a90d12c5ea32aff06c311f0db2eb3f1201f3e7b2a04
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: 6cd96a410161ff617b63581a08376f0cb9162375adeb7956e10c8cd397821f7eb2a6de24eb22a0b28401300bf228c86e50617cd568209b5f6775b93c97d2fe3a
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^6.0.0":
  version: 6.0.0
  resolution: "write-file-atomic@npm:6.0.0"
  dependencies:
    imurmurhash: ^0.1.4
    signal-exit: ^4.0.1
  checksum: 35f1303b0229c89c36d0817de9912b43a242f775cb0f386fecf97bac735013e1fde5f464c2ce9f63288d2c91b1ec5bc18d55347b0e37c0e4dbc64b60dc220629
  languageName: node
  linkType: hard

"xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"xxhashjs@npm:^0.2.2":
  version: 0.2.2
  resolution: "xxhashjs@npm:0.2.2"
  dependencies:
    cuint: ^0.2.2
  checksum: cf6baf05bafe5651dbf108008bafdb1ebe972f65228633f00b56c49d7a1e614a821fe3345c4eb27462994c7c954d982eae05871be6a48146f30803dd87f3c3b6
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0, yaml@npm:^1.10.2":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: ce4ada136e8a78a0b08dc10b4b900936912d15de59905b2bf415b4d33c63df1d555d23acb2a41b23cf9fb5da41c256441afca3d6509de7247daa062fd2c5ea5f
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2, yargs-parser@npm:^20.2.3":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 8bb69015f2b0ff9e17b2c8e6bfe224ab463dd00ca211eece72a4cd8a906224d2703fb8a326d36fdd0e68701e201b2a60ed7cf81ce0fd9b3799f9fe7745977ae3
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs@npm:^16.0.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: ^7.0.2
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.0
    y18n: ^5.0.5
    yargs-parser: ^20.2.2
  checksum: b14afbb51e3251a204d81937c86a7e9d4bdbf9a2bcee38226c900d00f522969ab675703bee2a6f99f8e20103f608382936034e64d921b74df82b63c07c5e8f59
  languageName: node
  linkType: hard

"yargs@npm:^17.0.0, yargs@npm:^17.5.1":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 73b572e863aa4a8cbef323dd911d79d193b772defd5a51aab0aca2d446655216f5002c42c5306033968193bdbf892a7a4c110b0d77954a7fdf563e653967b56a
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 2c487b0e149e746ef48cda9f8bad10fc83693cd69d7f9dcd8be4214e985de33a29c9e24f3c0d6bcf2288427040a8947406ab27f7af67ee9456e6b84854f02dd6
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"yoctocolors@npm:^2.0.0":
  version: 2.1.1
  resolution: "yoctocolors@npm:2.1.1"
  checksum: 563fbec88bce9716d1044bc98c96c329e1d7a7c503e6f1af68f1ff914adc3ba55ce953c871395e2efecad329f85f1632f51a99c362032940321ff80c42a6f74d
  languageName: node
  linkType: hard

"zod@npm:^3.22.4":
  version: 3.22.4
  resolution: "zod@npm:3.22.4"
  checksum: 80bfd7f8039b24fddeb0718a2ec7c02aa9856e4838d6aa4864335a047b6b37a3273b191ef335bf0b2002e5c514ef261ffcda5a589fb084a48c336ffc4cdbab7f
  languageName: node
  linkType: hard

"zwitch@npm:^1.0.0":
  version: 1.0.5
  resolution: "zwitch@npm:1.0.5"
  checksum: 28a1bebacab3bc60150b6b0a2ba1db2ad033f068e81f05e4892ec0ea13ae63f5d140a1d692062ac0657840c8da076f35b94433b5f1c329d7803b247de80f064a
  languageName: node
  linkType: hard
