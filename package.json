{"name": "@unlockre/utils-packages", "private": true, "scripts": {"build-all": "scripts/build-all.sh", "semantic-release-all": "yarn workspaces foreach run semantic-release", "utils-amplitude:run": "yarn workspace @unlockre/utils-amplitude run", "utils-array:run": "yarn workspace @unlockre/utils-array run", "utils-auth0:run": "yarn workspace @unlockre/utils-auth0 run", "utils-date:run": "yarn workspace @unlockre/utils-date run", "utils-dom:run": "yarn workspace @unlockre/utils-dom run", "utils-formatting:run": "yarn workspace @unlockre/utils-formatting run", "utils-http:run": "yarn workspace @unlockre/utils-http run", "utils-intl:run": "yarn workspace @unlockre/utils-intl run", "utils-next:run": "yarn workspace @unlockre/utils-next run", "utils-number:run": "yarn workspace @unlockre/utils-number run", "utils-object:run": "yarn workspace @unlockre/utils-object run", "utils-promise:run": "yarn workspace @unlockre/utils-promise run", "utils-react:run": "yarn workspace @unlockre/utils-react run", "utils-split-io:run": "yarn workspace @unlockre/utils-split-io run", "utils-string:run": "yarn workspace @unlockre/utils-string run", "utils-swr:run": "yarn workspace @unlockre/utils-swr run", "utils-userback:run": "yarn workspace @unlockre/utils-userback run", "utils-validation:run": "yarn workspace @unlockre/utils-validation run"}, "workspaces": ["packages/*"], "packageManager": "yarn@3.1.1", "devDependencies": {"@commitlint/cli": "^16.2.1", "@commitlint/config-conventional": "^16.2.1", "@types/uuid": "^9.0.8", "husky": "^7.0.4", "lint-staged": "^12.3.4"}, "resolutions": {"npm/chalk": "^4.1.2", "semantic-release-slack-bot/micromatch": "^4.0.0"}, "dependencies": {"uuid": "^9.0.1"}}