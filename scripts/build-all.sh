#!/usr/bin/env bash

orderedPackagesName=(
  utils-array
  utils-number
  utils-string
  utils-object
  utils-promise
  utils-formatting
  utils-http
  utils-intl
  utils-date
  utils-dom
  utils-validation
  utils-react
  utils-next
  utils-auth0
  utils-amplitude
  utils-split-io
  utils-userback
  utils-swr
)

for package in ${orderedPackagesName[@]}
do
  packageFullName="@unlockre/${package}"

  echo "Building package: ${package}"

  yarn workspace $packageFullName build

  if (( $? != 0 ))
  then
    exit 1
  fi
done