name: Deploy

on:
  workflow_call:
    inputs:
      with-semantic-release:
        description: With Semantic Release?
        default: false
        type: boolean

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Clone repository
        uses: actions/checkout@v2
        with:
          persist-credentials: false

      - name: Setup Node and Yarn
        uses: unlockre/github-resources/actions/setup-node-and-yarn@main
        with:
          github-token: ${{ secrets.GH_PAT }}

      - name: Build
        run: |
          yarn build-all
        env:
          GH_PAT: ${{ secrets.GH_PAT }}

      - name: Semantic Versioning
        if: ${{ inputs.with-semantic-release }}
        run: |
          yarn semantic-release-all
        env:
          GH_PAT: ${{ secrets.GH_PAT }}
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
          RELEASES_SLACK_WEBHOOK: ${{ secrets.RELEASES_SLACK_WEBHOOK }}
          YARN_NPM_AUTH_TOKEN: ${{ secrets.GH_PAT }}
