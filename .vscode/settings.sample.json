{"editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.formatOnSave": false, "eslint.validate": ["javascript", "typescript"], "eslint.workingDirectories": ["./packages/utils-array", "./packages/utils-amplitude", "./packages/utils-auth0", "./packages/utils-dom", "./packages/utils-formatting", "./packages/utils-http", "./packages/utils-intl", "./packages/utils-next", "./packages/utils-object", "./packages/utils-promise", "./packages/utils-react", "./packages/utils-split-io", "./packages/utils-userback", "./packages/utils-validation"], "typescript.tsdk": "./node_modules/typescript/lib"}