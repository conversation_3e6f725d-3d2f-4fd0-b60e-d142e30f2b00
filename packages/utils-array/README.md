# utils-array

- [Installation](#installation)
- [Utilities](#utilities)
  - [areEqual](#areEqual)
  - [chunk](#chunk)
  - [diff](#diff)
  - [ensureItem](#ensureItem)
  - [ensureValidIndex](#ensureValidIndex)
  - [findIndexFrom](#findindexfrom)
  - [getFrequencyMap](#getFrequencyMap)
  - [groupBy](#groupby)
  - [haveSameItems](#havesameitems)
  - [insert](#insert)
  - [isValidIndex](#isValidIndex)
  - [move](#move)
  - [reduceIf](#reduceif)
  - [reduceWhile](#reducewhile)
  - [remove](#remove)
  - [removeItem](#removeitem)
  - [toggleItem](#toggleitem)
  - [unique](#unique)
  - [uniqueBy](#uniqueby)
  - [update](#update)

## Installation

```sh
yarn add @unlockre/utils-array
```

## Utilities

### areEqual

#### Description

Returns a boolean indicating if the given arrays are equal.

> By default, it uses `Object.is` to compare the items, but you can provide your own function as the third argument.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers1 = [1, 2, 3, 4, 5];

const numbers2 = [1, 2, 3, 4, 5];

console.log(withArray.areEqual(numbers1, numbers2));

// true
```

### chunk

#### Description

Divides the given array into chunks of the specified size.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const array = [1, 2, 3, 4, 5, 6, 7];

console.log(withArray.chunk(array, 2));

// [
//   [1, 2],
//   [3, 4],
//   [5, 6],
//   [7],
// ]
```

### diff

#### Description

Returns the difference between the two given arrays.

> By default, it uses `Array.prototype.includes` to compare the items, but you can provide a custom comparison function as the third argument.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers1 = [1, 2, 3, 4, 5];

const numbers2 = [4, 5, 6, 7, 8];

console.log(withArray.diff(numbers1, numbers2));

// [1, 2, 3]
```

### ensureItem

#### Description

Returns the item at the given index, throwing an error if the index is out of bounds.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const allNumbers = [0, 1, 2, 3, 4];

console.log(withArray.ensureItem(allNumbers, 2));

// logs 2

console.log(withArray.ensureItem(allNumbers, 6));

// throws Error("Invalid index: 6")
```

### ensureValidIndex

#### Description

Throws an Error if the given index is out of bounds.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [0, 1, 2, 3, 4];

withArray.ensureValidIndex(numbers, 6);

// throws Error("Invalid index: 6")
```

### findIndexFrom

#### Description

Returns the index of the element in the given array starting from the provided index, that satisfies the predicate function.

If no elements satisfy the predicate function, -1 is returned.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [1, 3, 5, 7];

console.log(withArray.findIndexFrom(numbers, 2, number => number < 5));

// -1
```

### getFrequencyMap

#### Description

Returns a map with the frequency of each item in the given array.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [1, 2, 4, 3, 4, 1, 4, 3, 4, 1, 4, 4];

const frequencyMap = withArray.getFrequencyMap(numbers);

console.log(frequencyMap);

// Map(4) { 1 => 3, 2 => 1, 3 => 2, 4 => 6 }
```

### groupBy

#### Description

Groups the items in the given array returning an object where each entry corresponds to a group of items identified by the key returned by the provided function.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const items = [
  {type: "fruit", name: "apple"},
  {type: "vegetable", name: "lettuce"},
  {type: "vegetable", name: "carrot"},
  {type: "fruit", name: "banana"},
];

const itemsGrouped = withArray.groupBy(
  items,
  item => item.type
);

console.log(itemsGrouped);

// {
//   fruit: [
//     {type: "fruit", name: "apple"},
//     {type: "fruit", name: "banana"}
//   ],
//   vegetable: [
//     {type: "vegetable", name: "lettuce"},
//     {type: "vegetable", name: "carrot"}
//   ]
// }
```

### haveSameItems

#### Description

Returns a boolean indicating if the given arrays have the same items (references and occurrences) regardless of their order.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers1 = [1, 2, 2, 3, 2, 4, 5];

const numbers2 = [5, 4, 2, 3, 2, 2, 1];

console.log(withArray.haveSameItems(numbers1, numbers2));

// true
```

### insert

#### Description

Returns a new array with the given value inserted at index.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [0, 2, 3];

const updatedNumbers = withArray.insert(numbers, 1, 1);

console.log(updatedNumbers);

// [0, 1, 2, 3]
```

### isValidIndex

#### Description

Returns a boolean indicating if the given index is valid.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [0, 1, 2, 3];

console.log(withArray.isValidIndex(numbers, 5));

// false
```

### move

#### Description

Moves the item at the source index to the target index.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [0, 1, 2, 3, 4];

const updatedNumbers = withArray.move(numbers, 2, 4);

console.log(updatedNumbers);

// [0, 1, 4, 3, 2]
```

### reduceIf

#### Description

Reduces the given array only if the predicate function returns true.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [1, 2, 3, 4, 5];

const sum = withArray.reduceIf(
  numbers,
  0,
  (sum, number) => number % 2 === 1,
  (sum, number) => sum + number
);

console.log(sum);

// 9
```

### reduceWhile

#### Description

Reduces the given array while the predicate function returns true.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [1, 2, 3, 4, 5];

const sum = withArray.reduceWhile(
  numbers,
  0,
  (sum, number) => number < 4,
  (sum, number) => sum + number
);

console.log(sum);

// 6
```

### remove

#### Description

Returns a new array with the given amount elements from index removed.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [0, 1, 2, 3, 4];

const updatedNumbers = withArray.remove(numbers, 2, 2);

console.log(updatedNumbers);

// [0, 1, 4]
```

### removeItem

#### Description

Returns a new array with the given item removed.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = ["one", "two", "three", "four", "five"];

const updatedNumbers = withArray.removeItem(numbers, "three");

console.log(updatedNumbers);

// ["one", "two", "four", "five"]
```

### toggleItem

#### Description

Returns a new array with the given item toggled (added if it's not present, removed if it's present).

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = ["one", "two", "three", "four", ];

console.log(withArray.toggleItem(numbers, "three"));

// ["one", "two", "four"]

console.log(withArray.toggleItem(updatedNumbers, "five"));

// ["one", "two", "three", "four", "five"]
```

### unique

#### Description

Returns a new array with only one occurrence of each element.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [1, 2, 2, 3, 3, 4, 5, 5, 6];

const updatedNumbers = withArray.unique(numbers);

console.log(updatedNumbers);

// [1, 2, 3, 4, 5, 6]
```

### uniqueBy

#### Description

Returns a new array with unique elements based on the reference obtained from each of them using the given `getReference` function.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const items = [
  {id: 1, label: "one"},
  {id: 2, label: "two"},
  {id: 1, label: "one"},
  {id: 2, label: "two"}
];

const uniqueItemsById = withArray.uniqueBy(items, item => item.id);

console.log(uniqueItemsById);

// [
//   {id: 1, label: "one"},
//   {id: 2, label: "two"},
// ]
```

### update

#### Description

Returns a new array with the value at index updated.

#### Example

```ts
import * as withArray from "@unlockre/utils-array/dist";

const numbers = [0, 1, 2, 3, 4];

const updatedNumbers = withArray.update(numbers, 0, 5);

console.log(updatedNumbers);

// [5, 1, 2, 3, 4]
```
