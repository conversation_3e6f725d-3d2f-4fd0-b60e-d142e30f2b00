const getChunk = <TItem>(
  items: TItem[],
  chunkSize: number,
  chunkIndex: number
) => items.slice(chunkIndex * chunkSize, chunkIndex * chunkSize + chunkSize);

const getLength = <TItem>(items: TItem[], chunkSize: number) =>
  Math.ceil(items.length / chunkSize);

const chunk = <TItem>(items: TItem[], chunkSize: number) =>
  Array.from({length: getLength(items, chunkSize)}, (_, index) =>
    getChunk(items, chunkSize, index)
  );

export {chunk};
