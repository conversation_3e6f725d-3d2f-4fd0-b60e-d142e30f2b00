import type {ReducePredicate, Reducer} from "./types";

const reduceIf = <TItem, TResult>(
  items: TItem[],
  initialValue: TResult,
  predicate: ReducePredicate<TItem, TResult>,
  reducer: Reducer<TItem, TResult>
) =>
  items.reduce((result, item, index) => {
    if (!predicate(item, index, items, result)) {
      return result;
    }

    return reducer(result, item, index, items);
  }, initialValue);

export {reduceIf};
