import {ensureValidIndex} from "./ensure-valid-index";

const move = <TItem>(
  items: TItem[],
  sourceIndex: number,
  targetIndex: number
) => {
  ensureValidIndex(items, sourceIndex);

  ensureValidIndex(items, targetIndex);

  if (sourceIndex === targetIndex) {
    return items;
  }

  return items.reduce<TItem[]>((result, item, index) => {
    if (index === sourceIndex) {
      return result;
    }

    if (index !== targetIndex) {
      return [...result, item];
    }

    return sourceIndex < targetIndex
      ? [...result, item, items[sourceIndex]]
      : [...result, items[sourceIndex], item];
  }, []);
};

export {move};
