type Reduction<TItem, TReference> = {
  references: TReference[];
  result: TItem[];
};

type GetReference<TItem, TReference> = (item: TItem) => TReference;

const updateReduction = <TItem, TReference>(
  reduction: Reduction<TItem, TReference>,
  item: TItem,
  reference: TReference
): Reduction<TItem, TReference> => ({
  references: [...reduction.references, reference],
  result: [...reduction.result, item]
});

const createReduction = <TItem, TReference>(): Reduction<
  TItem,
  TReference
> => ({
  references: [],
  result: []
});

const uniqueBy = <TItem, TReference>(
  items: TItem[],
  getReference: GetReference<TItem, TReference>
) =>
  items.reduce((reduction, item) => {
    const reference = getReference(item);

    return reduction.references.includes(reference)
      ? reduction
      : updateReduction(reduction, item, reference);
  }, createReduction<TItem, TReference>()).result;

export {uniqueBy};
