import type {ReducePredicate, Reducer} from "./types";

const reduceWhile = <TItem, TResult>(
  items: TItem[],
  initialValue: TResult,
  predicate: ReducePredicate<TItem, TResult>,
  reducer: Reducer<TItem, TResult>
) => {
  let result = initialValue;

  for (const [i, item] of items.entries()) {
    if (!predicate(item, i, items, result)) {
      break;
    }

    result = reducer(result, item, i, items);
  }

  return result;
};

export {reduceWhile};
