import type {CompareNumber} from "@unlockre/utils-number/dist";

import {ensureValidIndex} from "./ensure-valid-index";
import {findIndex} from "./find-index";
import type {AreItemsEqual} from "./types";

const sortLike = <TItem>(
  items: TItem[],
  otherItems: TItem[],
  compareItemIndex: CompareNumber,
  areItemsEqual?: AreItemsEqual<TItem>
) =>
  [...items].sort((item1, item2) => {
    const item1Index = ensureValidIndex(
      otherItems,
      findIndex(otherItems, item1, areItemsEqual)
    );

    const item2Index = ensureValidIndex(
      otherItems,
      findIndex(otherItems, item2, areItemsEqual)
    );

    return compareItemIndex(item1Index, item2Index);
  });

export {sortLike};
