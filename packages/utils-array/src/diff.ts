import type {AreItemsEqual} from "./types";

const diffWithIncludes = <TItem>(items1: TItem[], items2: TItem[]) =>
  items1.filter(item1 => !items2.includes(item1));

const diffWithFind = <TItem>(
  items1: TItem[],
  items2: TItem[],
  areItemsEqual: AreItemsEqual<TItem>
) => items1.filter(item1 => !items2.find(item2 => areItemsEqual(item2, item1)));

const diff = <TItem>(
  items1: TItem[],
  items2: TItem[],
  areItemsEqual?: AreItemsEqual<TItem>
) => {
  if (items2.length === 0) {
    return [...items1];
  }

  return areItemsEqual
    ? diffWithFind(items1, items2, areItemsEqual)
    : diffWithIncludes(items1, items2);
};

export {diff};
