type GetGroupKey<TItem, T<PERSON><PERSON><PERSON><PERSON> extends string> = (item: TItem) => TGroupKey;

type Result<TItem, TGroup<PERSON>ey extends string> = Record<TGroupKey, TItem[]>;

const groupBy = <TItem, TGroup<PERSON>ey extends string = string>(
  items: TItem[],
  getGroupKey: GetGroupKey<TItem, TGroupKey>
) =>
  items.reduce(
    (result, item) => {
      const groupKey = getGroupKey(item);

      const group = result[groupKey] ?? [];

      return {...result, [groupKey]: [...group, item]};
    },
    {} as Result<TItem, TGroupKey>
  );

export {groupBy};
