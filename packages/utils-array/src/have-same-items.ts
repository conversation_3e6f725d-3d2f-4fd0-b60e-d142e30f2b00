import {getFrequencyMap} from "./get-frequency-map";

const haveSameItems = <TItem>(items1: TItem[], items2: TItem[]) => {
  if (items1.length !== items2.length) {
    return false;
  }

  const frequencyMap1 = getFrequencyMap(items1);

  const frequencyMap2 = getFrequencyMap(items2);

  if (frequencyMap1.size !== frequencyMap2.size) {
    return false;
  }

  return [...frequencyMap1].every(
    ([item, count]) => count === frequencyMap2.get(item)
  );
};

export {haveSameItems};
