declare module "array.prototype.groupby" {
  type AnyArray<TElement> = Array<TElement> | ReadonlyArray<TElement>;

  type GroupByGetKey<
    TArray extends AnyArray<unknown>,
    TGroupByKey extends string
  > = (element: TArray[number], index: number, array: TArray) => TGroupByKey;

  type GroupBy = <TArray extends AnyArray<unknown>, TGroupByKey extends string>(
    array: TArray,
    getKey: GroupByGetKey<TArray, TGroupByKey>
  ) => Record<TGroupByKey, TArray>;

  type GroupByThis = <
    TThis extends AnyArray<unknown>,
    TGroupByKey extends string
  >(
    this: TThis,
    getKey: GroupByGetKey<TThis, TGroupByKey>
  ) => Record<TGroupByKey, TThis>;

  type GroupByThisBinded<TThis extends AnyArray<unknown>> = <
    TGroupByKey extends string
  >(
    this: TThis,
    getKey: GroupByGetKey<TThis, TGroupByKey>
  ) => Record<TGroupByKey, TThis>;

  const exports: GroupBy & {
    getPolyfill: () => GroupByThis;
    implementation: GroupByThis;
    shim: () => GroupByThis;
  };

  export default exports;

  export type {GroupBy, GroupByGetKey, GroupByThis, GroupByThisBinded};
}
