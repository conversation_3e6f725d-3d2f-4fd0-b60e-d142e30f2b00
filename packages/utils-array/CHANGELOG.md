# [@unlockre/utils-array-v1.14.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.13.0...@unlockre/utils-array-v1.14.0) (2025-07-25)


### Features

* Add diff array utility ([#252](https://github.com/unlockre/utils-packages/issues/252)) ([076a0d0](https://github.com/unlockre/utils-packages/commit/076a0d02d21f7ff178c28f01a498b6014bd6180f))

# [@unlockre/utils-array-v1.13.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.12.0...@unlockre/utils-array-v1.13.0) (2025-07-08)


### Features

* Add areEqual array utility ([#248](https://github.com/unlockre/utils-packages/issues/248)) ([d8683cb](https://github.com/unlockre/utils-packages/commit/d8683cbea17501ec8a0170a21c4cb0f792ca88a3))

# [@unlockre/utils-array-v1.12.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.11.0...@unlockre/utils-array-v1.12.0) (2025-06-30)


### Features

* Add reduceIf array utility ([#245](https://github.com/unlockre/utils-packages/issues/245)) ([df5173c](https://github.com/unlockre/utils-packages/commit/df5173c632044f9af5c8dc8c093f10a6a301428e))

# [@unlockre/utils-array-v1.11.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.10.0...@unlockre/utils-array-v1.11.0) (2025-06-12)


### Features

* Add array utilities ([#242](https://github.com/unlockre/utils-packages/issues/242)) ([bb69d41](https://github.com/unlockre/utils-packages/commit/bb69d41d17f12a98e83bb033f9e8b0aaaf960dc7))

# [@unlockre/utils-array-v1.10.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.9.2...@unlockre/utils-array-v1.10.0) (2025-05-26)


### Features

* Add array utilities ([#240](https://github.com/unlockre/utils-packages/issues/240)) ([56a3d3e](https://github.com/unlockre/utils-packages/commit/56a3d3ed93f69215d5a66107ec5a378e3d82fb37))

# [@unlockre/utils-array-v1.9.2](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.9.1...@unlockre/utils-array-v1.9.2) (2025-01-24)


### Bug Fixes

* Fix move array utility ([#205](https://github.com/unlockre/utils-packages/issues/205)) ([0eff88d](https://github.com/unlockre/utils-packages/commit/0eff88d9a3555916df238565e861bfdadef23263))

# [@unlockre/utils-array-v1.9.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.9.0...@unlockre/utils-array-v1.9.1) (2025-01-23)


### Bug Fixes

* Fix move array utility ([#203](https://github.com/unlockre/utils-packages/issues/203)) ([9d05df5](https://github.com/unlockre/utils-packages/commit/9d05df5d23cf2bc803b7594f19663c96dcc485e4))

# [@unlockre/utils-array-v1.9.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.8.0...@unlockre/utils-array-v1.9.0) (2025-01-22)


### Features

* Add array utilities ([#201](https://github.com/unlockre/utils-packages/issues/201)) ([400e535](https://github.com/unlockre/utils-packages/commit/400e535cbf54dd045a69f55035e3efde0f26a136))

# [@unlockre/utils-array-v1.8.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.7.0...@unlockre/utils-array-v1.8.0) (2024-03-12)


### Features

* Add chunk and groupBy array utilities ([#116](https://github.com/unlockre/utils-packages/issues/116)) ([8b5ba57](https://github.com/unlockre/utils-packages/commit/8b5ba574288e99108370197d32369f24c7dd6d2e))

# [@unlockre/utils-array-v1.7.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.6.1...@unlockre/utils-array-v1.7.0) (2023-12-11)


### Features

* Add array utilities ([#100](https://github.com/unlockre/utils-packages/issues/100)) ([abbaf36](https://github.com/unlockre/utils-packages/commit/abbaf36d09af267880be5a5386e99e1326261b86))

# [@unlockre/utils-array-v1.6.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.6.0...@unlockre/utils-array-v1.6.1) (2023-12-07)


### Bug Fixes

* Improve array groupBy utility typing ([#98](https://github.com/unlockre/utils-packages/issues/98)) ([a720568](https://github.com/unlockre/utils-packages/commit/a720568a74b95efa5f76ed208cf19ab6fb3d199d))

# [@unlockre/utils-array-v1.6.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.5.1...@unlockre/utils-array-v1.6.0) (2023-09-22)


### Features

* Add groupBy array utility ([#79](https://github.com/unlockre/utils-packages/issues/79)) ([d3eb7d5](https://github.com/unlockre/utils-packages/commit/d3eb7d5c896784eaa587f007f0a659a3425d9cd0))

# [@unlockre/utils-array-v1.5.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.5.0...@unlockre/utils-array-v1.5.1) (2023-07-17)


### Bug Fixes

* **utils-array:** Throw if index is out of bounds ([#50](https://github.com/unlockre/utils-packages/issues/50)) ([ec99c72](https://github.com/unlockre/utils-packages/commit/ec99c7292739c5764327d98514b0123ab5ca36c6))

# [@unlockre/utils-array-v1.5.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.4.0...@unlockre/utils-array-v1.5.0) (2023-05-03)


### Features

* Add array update util ([#33](https://github.com/unlockre/utils-packages/issues/33)) ([2bdd0b6](https://github.com/unlockre/utils-packages/commit/2bdd0b6964a2560028d204288bd71ab4d2e22f9d))

# [@unlockre/utils-array-v1.4.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.3.0...@unlockre/utils-array-v1.4.0) (2023-03-15)


### Features

* Add unique feature to utils-array ([#28](https://github.com/unlockre/utils-packages/issues/28)) ([a24ee4f](https://github.com/unlockre/utils-packages/commit/a24ee4ff051edaaf94f3edb35663606291d230a2))

# [@unlockre/utils-array-v1.3.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.2.0...@unlockre/utils-array-v1.3.0) (2022-09-22)


### Features

* Add utils-formatting package ([#22](https://github.com/unlockre/utils-packages/issues/22)) ([893075c](https://github.com/unlockre/utils-packages/commit/893075c3e479a727e9d3adf96c68b48b37de503b))

# [@unlockre/utils-array-v1.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.1.1...@unlockre/utils-array-v1.2.0) (2022-07-28)


### Features

* Add utils-dom package ([#18](https://github.com/unlockre/utils-packages/issues/18)) ([46543cb](https://github.com/unlockre/utils-packages/commit/46543cbb83d6dfffe9765aab53b52ba9845c32e1))

# [@unlockre/utils-array-v1.1.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.1.0...@unlockre/utils-array-v1.1.1) (2022-06-10)


### Bug Fixes

* Corrected wrong export declaration ([#11](https://github.com/unlockre/utils-packages/issues/11)) ([943b019](https://github.com/unlockre/utils-packages/commit/943b01980d23e8984b9efd10ac640654ca9f067a))

# [@unlockre/utils-array-v1.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-array-v1.0.0...@unlockre/utils-array-v1.1.0) (2022-06-09)


### Features

* Add utils-array remove function ([#9](https://github.com/unlockre/utils-packages/issues/9)) ([d983a0e](https://github.com/unlockre/utils-packages/commit/d983a0eb236e75920b06da55397c6bd519c76e3c))
* Add utils-promise package ([#10](https://github.com/unlockre/utils-packages/issues/10)) ([431a300](https://github.com/unlockre/utils-packages/commit/431a300f56e137a444bb0967ad617d9fc6845ecb))

# @unlockre/utils-array-v1.0.0 (2022-04-29)


### Features

* Add utils-array package ([#5](https://github.com/unlockre/utils-packages/issues/5)) ([4ba190b](https://github.com/unlockre/utils-packages/commit/4ba190bd47c20dc0c8d2649e4c137cd0bd87408e))
