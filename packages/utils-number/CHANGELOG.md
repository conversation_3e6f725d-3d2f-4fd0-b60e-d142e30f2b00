# [@unlockre/utils-number-v2.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-number-v2.1.0...@unlockre/utils-number-v2.2.0) (2025-01-21)


### Features

* Add getRange utility ([#196](https://github.com/unlockre/utils-packages/issues/196)) ([c0b029c](https://github.com/unlockre/utils-packages/commit/c0b029c5e618622777ea40c8c5a638d3a28b697a))

# [@unlockre/utils-number-v2.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-number-v2.0.0...@unlockre/utils-number-v2.1.0) (2024-08-30)


### Features

* Add getAverage ([#167](https://github.com/unlockre/utils-packages/issues/167)) ([c8f7fd7](https://github.com/unlockre/utils-packages/commit/c8f7fd72488baedb2b040aa639728ed64294cc00))

# [@unlockre/utils-number-v2.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-number-v1.0.1...@unlockre/utils-number-v2.0.0) (2023-12-19)


### Bug Fixes

* Fix number compare utils ([#101](https://github.com/unlockre/utils-packages/issues/101)) ([1275f81](https://github.com/unlockre/utils-packages/commit/1275f810f71108f74989244d9c639f21397da09c))


### BREAKING CHANGES

* Fix number compare utils (It was backwards)

# [@unlockre/utils-number-v1.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-number-v1.0.0...@unlockre/utils-number-v1.0.1) (2023-09-12)


### Bug Fixes

* Fix utils-number compare fns types ([#74](https://github.com/unlockre/utils-packages/issues/74)) ([742cd30](https://github.com/unlockre/utils-packages/commit/742cd3059c1a171df78940e8652dc8488d7985bf))

# @unlockre/utils-number-v1.0.0 (2023-09-12)


### Features

* Add utils-number package ([#71](https://github.com/unlockre/utils-packages/issues/71)) ([7b07a5a](https://github.com/unlockre/utils-packages/commit/7b07a5a0278a1e1fc1b9f4fbbfe53cc8151d9da7))
