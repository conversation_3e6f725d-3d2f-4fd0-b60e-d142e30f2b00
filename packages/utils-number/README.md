# utils-number

- [Installation](#installation)
- [Utilities](#utilities)
  - [compareAsc](#compareAsc)
  - [compareDesc](#compareDesc)
  - [getAverage](#getAverage)
  - [getRange](#getRange)
  - [sum](#sum)

## Installation

```
yarn add @unlockre/utils-number
```

## Utilities

### compareAsc

#### API

```ts
const compareAsc = (
  number1: number,
  number2: number
): number => { /* ... */ }
```

#### Description

Function that compares 2 numbers and returns the following:
- zero if they are equal
- a positive number if number2 is less than number1
- a negative number if number2 is greater than number1

#### Example

```ts
import {compareAsc} from "@unlockre/utils-number/dist";

console.log(compareAsc(1, 1); // 0
console.log(compareAsc(1, 2); // -1
console.log(compareAsc(2, 1)); // 1
```

### compareDesc

#### API

```ts
const compareDesc = (
  number1: number,
  number2: number
): number => { /* ... */ }
```

#### Description

Function that compares 2 numbers and returns the following:
- zero if they are equal
- a positive number if number2 is greater than number1
- a negative number if number2 is less than number1

#### Example

```ts
import {compareDesc} from "@unlockre/utils-number/dist";

console.log(compareDesc(1, 1); // 0
console.log(compareDesc(1, 2); // 1
console.log(compareDesc(2, 1)); // -1
```

### getAverage

#### API

```ts
const getAverage = (
  values: number[]
): number => { /* ... */ }
```

#### Description

Function that calculates the average of a list of numbers

#### Example

```ts
import {getAverage} from "@unlockre/utils-number/dist";

console.log(getAverage([1, 2, 3, 4, 5])); // 3
```

### getRange

#### API

```ts
const getRange = (
  min: number,
  max: number,
  step = 1
): number[] => { /* ... */ }
```

#### Description

Function that returns a list of numbers (range) starting from `min`, increasing each of them with the given `step`, without surpassing the provided `max`.

#### Example

```ts
import {getRange} from "@unlockre/utils-number/dist";

console.log(getRange(1, 10)); // [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
console.log(getRange(1, 10, 2)); // [1, 3, 5, 7, 9]
console.log(getRange(1, 10, 3)); // [1, 4, 7, 10]
```

### sum

#### API

```ts
const sum = (
  values: number[]
): number => { /* ... */ }
```

#### Description

Function that calculates the sum of a list of numbers

### Example

```ts
import {sum} from "@unlockre/utils-number/dist";

console.log(sum([1, 2, 3, 4])); // 10
```
