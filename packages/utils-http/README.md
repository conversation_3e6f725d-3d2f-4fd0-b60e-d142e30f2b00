# utils-http

## Installation

```
yarn add @unlockre/utils-http
```

## Utilities

### Response / readInChunks

#### Description

Function that reads in chunks a response. It receives a response and a callback function that will be called on every chunk read.

#### Example

```tsx
import {useEffect} from "react";

import * as withResponse from "@unlockre/utils-http/dist/response";

const useFetchChunks = () => {
  const [text, setText] = useState("");

  const handleChunk = (chunk: string) => 
    setText(currentText => currentText + chunk);

  useEffect(() => {
    fetch("my-url").then(withResponse.readInChunks(handleChunk));
  }, []);

  return {text};
}

export default useFetchChunks;
```

### Response / throwIfError

#### Description

Function that receives a response and will throw an HttpResponseError if its response is not ok.

#### Example

```ts
import * as withResponse from "@unlockre/utils-http/dist/response";

fetch("my-url")
  .then(withResponse.throwIfError)
  // This will be called if !response.ok
  .catch(err => console.log(err));
```

### HttpResponseError

#### Description

The HttpResponseError class is a custom error class designed to be used for HTTP response errors. It provides a convenient way to encapsulate and throw exceptions when an HTTP response indicates an error or failure status.

#### Example

```ts
import * as withResponse from "@unlockre/utils-http/dist/response";
import HttpResponseError from "@unlockre/utils-http/dist/http-response-error";

fetch("my-url")
  .then(withResponse.throwIfError)
  // This will be called if !response.ok
  .catch(err => {
    if (err instanceof HttpResponseError) {
      // do something
    } else {
      // do something else
    }
  });
```

### HttpStatusCode

#### Description

List of http status codes and messages. Exports a getText function that returns the text based on the status.