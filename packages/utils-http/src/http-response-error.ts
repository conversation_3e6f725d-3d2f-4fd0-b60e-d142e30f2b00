import {CustomError} from "@mgtitimoli/utils-error";

import * as withHttpStatusCode from "./http-status-code";
import type {HttpStatusCode} from "./http-status-code";

const getResponseStatusCode = (response: Response): HttpStatusCode =>
  response.status as any;

const getMessage = (response: Response) =>
  response.statusText ||
  withHttpStatusCode.getText(getResponseStatusCode(response));

class HttpResponseError extends CustomError {
  response: Response;

  constructor(response: Response) {
    super(getMessage(response));

    this.response = response;
  }
}

export default HttpResponseError;
