import ensureBody from "./ensure-body";

const readInChunks =
  (onChunk: (chunk: string) => unknown) => (response: Response) =>
    new Promise(resolve => {
      const responseBody = ensureBody(response);

      const chunkStream = responseBody.pipeThrough(new TextDecoderStream());

      const reader = chunkStream.getReader();

      const read = (): Promise<void> =>
        reader.read().then(({done, value: chunk}) => {
          if (done) {
            return resolve(response);
          }

          onChunk(chunk);

          return read();
        });

      read();
    });

export default readInChunks;
