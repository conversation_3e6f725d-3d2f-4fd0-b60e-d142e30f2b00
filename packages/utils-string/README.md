# utils-string

- [Installation](#installation)
- [Types](#types)
- [Utilities](#utilities)
  - [capitalize](#capitalize)
  - [compareAsc](#compareAsc)
  - [compareDesc](#compareDesc)
  - [concat](#concat)
  - [uncapitalize](#uncapitalize)

## Installation

```sh
yarn add @unlockre/utils-string
```

## Types

```ts
type CompareResult = -1 | 0 | 1;
```

## Utilities

### capitalize

#### Description

Function that capitalizes a word

#### Example

```ts
import {capitalize} from "@unlockre/utils-string/dist";

console.log(capitalize("someString")); // SomeString
```

### compareAsc

#### Description

Function that compares 2 strings and returns the following:
- zero if they are equal
- a positive number if string2 comes after string1 (alphabetical order)
- a negative number if string2 comes before string1 (alphabetical order)

#### Example

```ts
import {compareAsc} from "@unlockre/utils-string/dist";

console.log(compareAsc("abc", "abc")); // 0
console.log(compareAsc("def", "abc")); // -1
console.log(compareAsc("abc", "def")); // 1
```

### compareDesc

#### Description

Function that compares 2 strings and returns the following:
- zero if they are equal
- a positive number if string2 comes before string1 (alphabetical order)
- a negative number if string2 comes after string1 (alphabetical order)

#### Example

```ts
import {compareDesc} from "@unlockre/utils-string/dist";

console.log(compareDesc("aabbcc", "aabbcc")); // 0
console.log(compareDesc("aabbcc", "ddeeff")); // -1
console.log(compareDesc("ddeeff", "aabbcc")); // 1
```

### concat

#### Description

Function that concatenates strings.

#### Example

```ts
import {concat} from "@unlockre/utils-string/dist";

console.log(concat("Some", "Pascal", "Case", "String")); // SomePascalCaseString
```

### uncapitalize

#### Description

Function that uncapitalizes a word

#### Example

```ts
import {uncapitalize} from "@unlockre/utils-string/dist";

console.log(uncapitalize("SomeString")); // someString
```
