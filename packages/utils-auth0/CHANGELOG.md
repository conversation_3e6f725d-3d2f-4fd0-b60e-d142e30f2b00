# [@unlockre/utils-auth0-v9.1.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v9.1.0...@unlockre/utils-auth0-v9.1.1) (2025-05-08)


### Bug Fixes

* Simplify useLogoutIfNeeded hook ([#239](https://github.com/unlockre/utils-packages/issues/239)) ([d2db9d1](https://github.com/unlockre/utils-packages/commit/d2db9d18dbec104d9a3acf2811f9cb244514ca51))

# [@unlockre/utils-auth0-v9.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v9.0.0...@unlockre/utils-auth0-v9.1.0) (2025-05-08)


### Features

* Improve useLogoutIfNeeded hook ([#238](https://github.com/unlockre/utils-packages/issues/238)) ([02268cc](https://github.com/unlockre/utils-packages/commit/02268cc0a4334ccfedab027dcd866f9b97482bd3))

# [@unlockre/utils-auth0-v9.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v8.0.0...@unlockre/utils-auth0-v9.0.0) (2025-05-08)


### Bug Fixes

* Support no minIssuedDate in useLogoutIfNeeded hook ([#237](https://github.com/unlockre/utils-packages/issues/237)) ([e21c4c6](https://github.com/unlockre/utils-packages/commit/e21c4c60ddde7c42a4e4867c1b1e6eab1a081af7))


### Features

* Add useLogoutIfNeeded hook ([#235](https://github.com/unlockre/utils-packages/issues/235)) ([671a70e](https://github.com/unlockre/utils-packages/commit/671a70e37d6a52b9b96e3dd835875238b7bfc988))


### BREAKING CHANGES

* Move Auth0AccessTokenClaims type to a dedicated module

# [@unlockre/utils-auth0-v8.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v7.0.0...@unlockre/utils-auth0-v8.0.0) (2025-04-07)


### Bug Fixes

* Remove keyreviews access permission ([#234](https://github.com/unlockre/utils-packages/issues/234)) ([6a2c58f](https://github.com/unlockre/utils-packages/commit/6a2c58f74c3106aaa4b37d946db8e1c8ee00782e))


### BREAKING CHANGES

* Remove keyreviews access permission

# [@unlockre/utils-auth0-v7.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v6.0.0...@unlockre/utils-auth0-v7.0.0) (2025-03-31)


### Bug Fixes

* Update react dependencies ([#232](https://github.com/unlockre/utils-packages/issues/232)) ([f98c37c](https://github.com/unlockre/utils-packages/commit/f98c37c119e6b5aee33e3860a51e30beb2c48460))


### BREAKING CHANGES

* Update react dependencies

# [@unlockre/utils-auth0-v6.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v5.6.0...@unlockre/utils-auth0-v6.0.0) (2025-01-13)


### Features

* Return clearExecution from useAsync ([#194](https://github.com/unlockre/utils-packages/issues/194)) ([505bbbf](https://github.com/unlockre/utils-packages/commit/505bbbfed4e179f135b2ca1f834ed307730985dd))


### BREAKING CHANGES

* Change useAsync result type

# [@unlockre/utils-auth0-v5.6.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v5.5.0...@unlockre/utils-auth0-v5.6.0) (2024-11-01)


### Features

* Add keyreview mgmt permission ([#175](https://github.com/unlockre/utils-packages/issues/175)) ([0181d09](https://github.com/unlockre/utils-packages/commit/0181d09d2c2a568229083dfcf8119d8653f1afeb))

# [@unlockre/utils-auth0-v5.5.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v5.4.0...@unlockre/utils-auth0-v5.5.0) (2024-10-18)


### Features

* Add keyreviews permission to auth0 ([#173](https://github.com/unlockre/utils-packages/issues/173)) ([b556f53](https://github.com/unlockre/utils-packages/commit/b556f53a3618b92c662e33e3e959ed8bfd2dc5fb))

# [@unlockre/utils-auth0-v5.4.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v5.3.0...@unlockre/utils-auth0-v5.4.0) (2024-06-26)


### Features

* Add keydocs permission ([#150](https://github.com/unlockre/utils-packages/issues/150)) ([37012fa](https://github.com/unlockre/utils-packages/commit/37012faf21abdbf310822e774ff0c8171c385953))

# [@unlockre/utils-auth0-v5.3.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v5.2.0...@unlockre/utils-auth0-v5.3.0) (2024-06-26)


### Features

* Add access permission type guard ([#148](https://github.com/unlockre/utils-packages/issues/148)) ([ad9acf5](https://github.com/unlockre/utils-packages/commit/ad9acf54aab25581d0a628cccaee948bea931ead))

# [@unlockre/utils-auth0-v5.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v5.1.0...@unlockre/utils-auth0-v5.2.0) (2024-06-25)


### Features

* Add keypilot permission ([#145](https://github.com/unlockre/utils-packages/issues/145)) ([f2e773d](https://github.com/unlockre/utils-packages/commit/f2e773dc4de41050513f1daf7a96f56bcce725f4))

# [@unlockre/utils-auth0-v5.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v5.0.0...@unlockre/utils-auth0-v5.1.0) (2024-06-12)


### Features

* Add keyway:access:keycomps permission ([#140](https://github.com/unlockre/utils-packages/issues/140)) ([d123f2b](https://github.com/unlockre/utils-packages/commit/d123f2bbadaba0487ff87102340aca42510a6e50))

# [@unlockre/utils-auth0-v5.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v4.1.1...@unlockre/utils-auth0-v5.0.0) (2024-03-04)


### Features

* Add useAuth0Auth hook ([#115](https://github.com/unlockre/utils-packages/issues/115)) ([68a304a](https://github.com/unlockre/utils-packages/commit/68a304a73d36e028fd84e4569379cae4f537d425))


### BREAKING CHANGES

* Deprecate useAuth0Login in favor of useAuth0Auth hook

# [@unlockre/utils-auth0-v4.1.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v4.1.0...@unlockre/utils-auth0-v4.1.1) (2024-03-03)


### Bug Fixes

* Make useAuth0Login params optional ([#114](https://github.com/unlockre/utils-packages/issues/114)) ([57693c1](https://github.com/unlockre/utils-packages/commit/57693c1db602776a97a18fbaa24bcd1dab4ba9e7))

# [@unlockre/utils-auth0-v4.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v4.0.0...@unlockre/utils-auth0-v4.1.0) (2024-03-03)


### Features

* Export Auth0AuthenticationChangeHandler type ([#113](https://github.com/unlockre/utils-packages/issues/113)) ([5e10242](https://github.com/unlockre/utils-packages/commit/5e1024232c3789770ada1d266e47bd0cecf5c1ea))

# [@unlockre/utils-auth0-v4.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v3.5.0...@unlockre/utils-auth0-v4.0.0) (2024-03-03)


### Features

* Add onAuthenticationChange handler ([#112](https://github.com/unlockre/utils-packages/issues/112)) ([37f9ce2](https://github.com/unlockre/utils-packages/commit/37f9ce20522b4998e678e8240b0fc9bbd31a2c13))


### BREAKING CHANGES

* Add onAuthenticationChange handler and remove autoRedirectToLogin param

# [@unlockre/utils-auth0-v3.5.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v3.4.0...@unlockre/utils-auth0-v3.5.0) (2024-02-29)


### Features

* Add auth0 returnTo url ([#110](https://github.com/unlockre/utils-packages/issues/110)) ([5420e7d](https://github.com/unlockre/utils-packages/commit/5420e7d019b9c71bf378fe79b8336d64ccb30861))

# [@unlockre/utils-auth0-v3.4.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v3.3.1...@unlockre/utils-auth0-v3.4.0) (2023-10-19)


### Features

* Add keywayUsersCreateInOtherOrg permission ([#87](https://github.com/unlockre/utils-packages/issues/87)) ([c79cdf3](https://github.com/unlockre/utils-packages/commit/c79cdf3eb55d29daad00e87f13600a268e985143))

# [@unlockre/utils-auth0-v3.3.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v3.3.0...@unlockre/utils-auth0-v3.3.1) (2023-10-10)


### Bug Fixes

* Stabilize utils-auth0 hooks ([#81](https://github.com/unlockre/utils-packages/issues/81)) ([eff505c](https://github.com/unlockre/utils-packages/commit/eff505c2458bc04554f5baf84a2e4f861489f57c))

# [@unlockre/utils-auth0-v3.3.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v3.2.0...@unlockre/utils-auth0-v3.3.0) (2023-10-09)


### Features

* Add organizationId to auth0 claims ([#80](https://github.com/unlockre/utils-packages/issues/80)) ([c5c3ce0](https://github.com/unlockre/utils-packages/commit/c5c3ce0c7a6bf5c5f4304c7b934c7f08b9600a99))

# [@unlockre/utils-auth0-v3.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v3.1.1...@unlockre/utils-auth0-v3.2.0) (2023-07-10)


### Features

* Add keyway access asset manager permission ([#48](https://github.com/unlockre/utils-packages/issues/48)) ([2df8a3c](https://github.com/unlockre/utils-packages/commit/2df8a3c8e48216f9912b313af0943271d51f0286))

# [@unlockre/utils-auth0-v3.1.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v3.1.0...@unlockre/utils-auth0-v3.1.1) (2023-07-03)


### Bug Fixes

* Make useAuth0Login ssr compatible ([#47](https://github.com/unlockre/utils-packages/issues/47)) ([fe73be7](https://github.com/unlockre/utils-packages/commit/fe73be7f764d09201ff14310e6cce94fec491f48))

# [@unlockre/utils-auth0-v3.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v3.0.0...@unlockre/utils-auth0-v3.1.0) (2023-06-23)


### Features

* Accept redirectUrl in useAuth0Login ([#45](https://github.com/unlockre/utils-packages/issues/45)) ([745ac5a](https://github.com/unlockre/utils-packages/commit/745ac5a6171d0f8de9bc033b009e5c96013e9193))

# [@unlockre/utils-auth0-v3.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v2.0.2...@unlockre/utils-auth0-v3.0.0) (2023-06-22)


### Bug Fixes

* Improve utils-auth0 naming ([#44](https://github.com/unlockre/utils-packages/issues/44)) ([adb4bc1](https://github.com/unlockre/utils-packages/commit/adb4bc1e7b1f9c3b3fa1c8ca037ac56bc44334e9))


### BREAKING CHANGES

* Rename KeywayAuth0Permission to KeywayPermission (module, type, etc)

# [@unlockre/utils-auth0-v2.0.2](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v2.0.1...@unlockre/utils-auth0-v2.0.2) (2023-06-20)


### Bug Fixes

* Fix utils-auth0 README ([#43](https://github.com/unlockre/utils-packages/issues/43)) ([ddad460](https://github.com/unlockre/utils-packages/commit/ddad460164e75ffda3a2c669547e25bfc29c03a8))

# [@unlockre/utils-auth0-v2.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v2.0.0...@unlockre/utils-auth0-v2.0.1) (2023-06-20)


### Bug Fixes

* Fix utils-auth0 types ([#42](https://github.com/unlockre/utils-packages/issues/42)) ([0c78c27](https://github.com/unlockre/utils-packages/commit/0c78c27f8d1ddc0a225f097baf25604483bc6455))

# [@unlockre/utils-auth0-v2.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v1.0.2...@unlockre/utils-auth0-v2.0.0) (2023-06-19)


### Features

* Add permissions to utils-auth0 ([#41](https://github.com/unlockre/utils-packages/issues/41)) ([7159ae1](https://github.com/unlockre/utils-packages/commit/7159ae1e087420e2c7756e6e7cf638001d2afe6a))


### BREAKING CHANGES

* Now useDealRoomAccessTokenClaims accepts a permission enum type instead of custom claims

# [@unlockre/utils-auth0-v1.0.2](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v1.0.1...@unlockre/utils-auth0-v1.0.2) (2023-06-13)


### Bug Fixes

* Fix useAuth0Login hook ([#39](https://github.com/unlockre/utils-packages/issues/39)) ([4a3b510](https://github.com/unlockre/utils-packages/commit/4a3b510518b14316af6811aaa47094a341a70c20))

# [@unlockre/utils-auth0-v1.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-auth0-v1.0.0...@unlockre/utils-auth0-v1.0.1) (2023-06-13)


### Bug Fixes

* Fix utils-auth0 README ([#38](https://github.com/unlockre/utils-packages/issues/38)) ([9792a8f](https://github.com/unlockre/utils-packages/commit/9792a8f11832512c0a77a6cd241823a754e6076f))

# @unlockre/utils-auth0-v1.0.0 (2023-06-07)


### Features

* Add utils-auth0 package ([#35](https://github.com/unlockre/utils-packages/issues/35)) ([335d94b](https://github.com/unlockre/utils-packages/commit/335d94b0b7080cffeaeb437fec47e033dcd003b0))
