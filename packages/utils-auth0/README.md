# utils-auth0

## Installation

```
yarn add @unlockre/utils-auth0
```

### Peer dependencies

- **@auth0/auth0-react**: `^2.0.0`
- **@auth0/auth0-spa-js**: `^2.0.0`
- **react**: `^17.0.0`

## Utilities

### keyway-permission

#### Description

This module contains a type and a const with all the Keyway global permissions, like for example: access permissions (`keyway:access:deal_room`, `keyway:access:property-sage`, etc).

### useAuth0AccessToken

#### Description

Custom hook that returns an [AsyncResult](../utils-react/src/async-result.ts) containing the Auth0 access token.

#### API

```ts
import type {AsyncResult} from "@unlockre/utils-react/dist/async-result";

type Result = AsyncResult<string>;

const useAuth0AccessToken = (): Result => { /* ... */ };
```

#### Example

```tsx
import {useAuth0AccessToken} from "@unlockre/utils-auth0/dist";

const MyComponent = () => {
  const result = useAuth0AccessToken();

  const accessToken = result.status === "succeeded"
    ? result.data
    : "-";

  const errorMessage = result.status === "failed"
    ? result.error.message
    : "-";

  return (
    <div>
      <div>Status: {result.status}</div>
      <div>Error: {errorMessage}</div>
      <div>AccessToken: {accessToken}</div>
    </div>
  );
};
```

### useAuth0AccessTokenClaims

#### Description

Custom hook to get the access token [claims](https://auth0.com/docs/secure/tokens/json-web-tokens/json-web-token-claims).

#### API

```ts
/**
 * @see https://www.iana.org/assignments/jwt/jwt.xhtml#claims
 */
type DefaultAuth0AccessTokenClaims = {
  aud: string[];
  azp: string;
  exp: number;
  iat: number;
  iss: string;
  scope: string;
  sub: string;
};

type Auth0AccessTokenClaims<TOtherPermission extends string = never> =
  & DefaultAuth0AccessTokenClaims
  & {
      permissions: (KeywayPermission | TOtherPermission)[]
    };

type Auth0AccessTokenClaimsResult<
  TOtherPermission extends string = never
> = AsyncResult<Auth0AccessTokenClaims<TOtherPermission>>;

const useAuth0AccessTokenClaims = <
  TOtherPermission extends string  = never
>(): Auth0AccessTokenClaimsResult<TOtherPermission> => {
  /* ... */
};
```

#### Examples

- Without other permissions

```ts
import {useAuth0AccessTokenClaims} from "@unlockre/utils-auth0/dist";

import type {AccessKeywayPermission} from "@unlockre/utils-auth0/dist/keyway-permission";

const useCanAccess = (accessPermission: AccessKeywayPermission) => {
  const accessTokenClaimsResult = useAuth0AccessTokenClaims();

  if (accessTokenClaimsResult.status !== "succeeded") {
    return undefined;
  }

  return accessTokenClaimsResult.data.permissions.includes(accessPermission);
};
```

- With other permissions

> This pattern can be used to attach the permissions of one or more apis to create a custom hook that can be used to get these in addition of the ones from [keyway](src/keyway-permission.ts).

**utils/deal-room-api/deal-room-api-permission.ts**

```ts
const dealRoomApiPermissions = {
  dealRoomDashboardReadAll: "deal_room:read_all:dashboard",
  dealRoomDashboardReadOwn: "deal_room:read_own:dashboard",
  dealRoomDealsCreate: "deal_room:create:deals",
  dealRoomDealsReadAll: "deal_room:read_all:deals",
  dealRoomDealsReadOwn: "deal_room:read_own:deals",
  dealRoomDealsUpdateAll: "deal_room:update_all:deals",
  dealRoomDealsUpdateOwn: "deal_room:update_own:deals",
  dealRoomMembersCreate: "deal_room:create:members",
  dealRoomMembersRead: "deal_room:read:members",
  dealRoomMembersUpdate: "deal_room:update:members",
  dealRoomUsersCreate: "deal_room:create:users"
} as const;

type DealRoomApiPermission =
  typeof dealRoomApiPermissions[keyof typeof dealRoomApiPermissions];

export {dealRoomApiPermissions};

export type {DealRoomApiPermission};
```

**utils/auth0/deal-room-permission.ts**

> We are creating this in case we ever need to add more permissions from another api.

```ts
import {keywayPermissions} from "@unlockre/utils-auth0/dist/keyway-permission";

import {dealRoomApiPermissions} from "@/utils/deal-room-api/deal-room-api-permission";

const dealRoomPermissions = {
  ...keywayPermissions,
  ...dealRoomApiPermissions
} as const;

type DealRoomPermission =
  typeof dealRoomPermissions[keyof typeof dealRoomPermissions];

export {dealRoomPermissions};

export type {DealRoomPermission};
```

**utils/auth0/use-deal-room-access-token-claims.ts**

> In case the project is using more than one API, another permission enum (in this case would be `deal-room-permission`) could be created joining the permissions enums from all the APIs and passing this one to `useAuth0AccessTokenClaims`.

```ts
import {useAuth0AccessTokenClaims} from "@unlockre/utils-auth0/dist";

import type {DealRoomPermission} from "./deal-room-permission";

const useDealRoomAccessTokenClaims = useAuth0AccessTokenClaims<DealRoomApiPermission>;
```

### useAuth0IdToken

#### Description

Custom hook that returns an [AsyncResult](../utils-react/src/async-result.ts) containing the Auth0 id token.

#### API

```ts
import type {AsyncResult} from "@unlockre/utils-react/dist/async-result";
import type {IdToken} from "@auth0/auth0-spa-js";

type Result = AsyncResult<IdToken>;

const useAuth0IdToken = (): Result => { /* ... */ };
```

#### Example

```tsx
import {useAuth0IdToken} from "@unlockre/utils-auth0/dist";

const MyComponent = () => {
  const result = useAuth0IdToken();

  const idToken = result.status === "succeeded"
    ? JSON.stringify(result.data)
    : "-";

  const errorMessage = result.status === "failed"
    ? result.error.message
    : "-";

  return (
    <div>
      <div>Status: {result.status}</div>
      <div>Error: {errorMessage}</div>
      <div>IdToken: {idToken}</div>
    </div>
  );
};
};
```

### useAuth0Auth

#### Description

Custom hook to know if the user is authenticated or not, handle any authentication change, redirect to login and perform a logout.

#### API

```ts
type RedirectToLogin = (returnBaseUrl?: string) => Promise<void>;

type LogoutTo = (returnUrl?: string) => void;

type Result = {
  isAuthenticated: boolean;
  isLoading: boolean;
  logoutTo: LogoutTo;
  redirectToLogin: RedirectToLogin;
};

type AuthenticationChangeHandlerParams = {
  isAuthenticated: boolean;
  redirectToLogin: RedirectToLogin;
};

type AuthenticationChangeHandler = (
  params: AuthenticationChangeHandlerParams
) => unknown;

type Params = {
  onAuthenticationChange?: AuthenticationChangeHandler;
};

const useAuth0Login = (params: Params = {}): Result => {
  /* ... */
};
```

#### Example

```tsx
import {useAuth0Login} from "@unlockre/deal-room-utils/dist/auth0";

import type {ReactElement} from "react";

type Props = {
  children: ReactElement | null;
};

const PrivatePageGuard = ({children}: Props) => {
  const {isAuthenticated} = useAuth0Login({
    onAuthenticationChange: ({isAuthenticated, redirectToLogin}) => {
      if (!isAuthenticated) {
        redirectToLogin();
      }
    }
  });

  return isAuthenticated ? children : <div>Loading...</div>;
};

export default PrivatePageGuard;
```

### useLogoutIfNeeded

#### Description

Custom hook to logout the user if the access token was issued before the given date.

#### API

```ts
const useLogoutIfNeeded = (minIssuedDate: Date) => {
  /* ... */
};
```

#### Example

```tsx
import {useLogoutIfNeeded} from "@unlockre/utils-auth0/dist";

const minIssuedDate = new Date("2024-01-01");

const MyComponent = () => {
  useLogoutIfNeeded(minIssuedDate);

  return <div>Content</div>;
};
```

### Auth0Client.create

#### Description

This utility is used to create an `Auth0Client` instance.

### API

```ts
import {Auth0Client} from "@auth0/auth0-spa-js";

type Params = {
  audience: string;
  clientId: string;
  domain: string;
};

const create = ({audience, clientId, domain}: Params): Auth0Client => {
  /* ... */
};
```

#### Example

```ts
import * as withAuth0Client from "@unlockre/utils-auth0/dist/auth0-client";

import environmentVariables from "@/utils/environment/environment-variables";

const auth0Client = withAuth0Client.create({
  audience: environmentVariables.AUTH0_AUDIENCE,
  clientId: environmentVariables.AUTH0_CLIENT_ID,
  domain: environmentVariables.AUTH0_DOMAIN
});

export default auth0Client;
```
