import * as withAsyncResult from "@unlockre/utils-react/dist/async-result";
import type {AsyncResult} from "@unlockre/utils-react/dist/async-result";
import {useMemo} from "react";

import type {Auth0AccessTokenClaims} from "./auth0-access-token-claims";
import useAuth0AccessToken from "./use-auth0-access-token";

type Auth0AccessTokenClaimsResult<TOtherPermission extends string = never> =
  AsyncResult<Auth0AccessTokenClaims<TOtherPermission>>;

const getAuth0AccessTokenClaims = (auth0AccessToken: string) =>
  JSON.parse(atob(auth0AccessToken.split(".")[1]));

const getSucceededResult = (auth0AccessToken: string) =>
  withAsyncResult.createSucceeded(getAuth0AccessTokenClaims(auth0AccessToken));

const useAuth0AccessTokenClaims = <
  TOtherPermission extends string = never
>(): Auth0AccessTokenClaimsResult<TOtherPermission> => {
  const auth0AccessTokenResult = useAuth0AccessToken();

  const auth0AccessTokenClaims = useMemo(
    () =>
      auth0AccessTokenResult.status === "succeeded"
        ? getSucceededResult(auth0AccessTokenResult.data)
        : auth0AccessTokenResult,
    [auth0AccessTokenResult]
  );

  return auth0AccessTokenClaims;
};

export default useAuth0AccessTokenClaims;
