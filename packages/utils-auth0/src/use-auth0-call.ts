import {useAuth0} from "@auth0/auth0-react";
import {useAsync} from "@unlockre/utils-react/dist";
import * as withAsyncResult from "@unlockre/utils-react/dist/async-result";
import type {FailedAsyncResult} from "@unlockre/utils-react/dist/async-result";
import {useCallback, useEffect, useState} from "react";

import type {UseAuth0Result} from "./types";

type Auth0Call<TData> = (useAuth0Result: UseAuth0Result) => Promise<TData>;

const useAuth0Call = <TData>(auth0Call: Auth0Call<TData>) => {
  const [notAuthenticatedResult, setNotAuthenticatedResult] = useState<
    FailedAsyncResult | undefined
  >(undefined);

  const useAuth0Result = useAuth0();

  const auth0CallWithParams = useCallback(
    () => auth0Call(useAuth0Result),
    [auth0Call, useAuth0Result]
  );

  const [auth0CallResult, executeAuthCall] = useAsync(auth0CallWithParams);

  useEffect(() => {
    if (!useAuth0Result.isAuthenticated) {
      setNotAuthenticatedResult(
        withAsyncResult.createFailed(new Error("Not authenticated"))
      );

      return;
    }

    setNotAuthenticatedResult(undefined);

    executeAuthCall();
  }, [useAuth0Result.isAuthenticated, executeAuthCall]);

  return notAuthenticatedResult || auth0CallResult;
};

export default useAuth0Call;
