import {useAuth0} from "@auth0/auth0-react";
import {useCallback, useEffect} from "react";

type RedirectToLogin = (returnBaseUrl?: string) => Promise<void>;

type AuthenticationChangeHandlerParams = {
  isAuthenticated: boolean;
  redirectToLogin: RedirectToLogin;
};

type Auth0AuthenticationChangeHandler = (
  params: AuthenticationChangeHandlerParams
) => unknown;

type Params = {
  onAuthenticationChange?: Auth0AuthenticationChangeHandler;
};

const useAuth0Auth = ({onAuthenticationChange}: Params = {}) => {
  const {isAuthenticated, isLoading, loginWithRedirect, logout} = useAuth0();

  const defaultReturnUrl =
    typeof window === "undefined" ? undefined : window.location.origin;

  const redirectToLogin: RedirectToLogin = (returnBaseUrl = defaultReturnUrl) =>
    loginWithRedirect({
      appState: {
        returnTo: window.location.href
      },
      authorizationParams: {
        prompt: "login",
        // eslint-disable-next-line @typescript-eslint/naming-convention
        redirect_uri: returnBaseUrl
      }
    });

  const logoutTo = useCallback(
    (returnUrl = defaultReturnUrl) =>
      logout({logoutParams: {returnTo: returnUrl}}),
    [defaultReturnUrl, logout]
  );

  useEffect(() => {
    if (!isLoading) {
      onAuthenticationChange?.({isAuthenticated, redirectToLogin});
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, isLoading]);

  return {isAuthenticated, isLoading, logoutTo, redirectToLogin};
};

export {useAuth0Auth};

export type {Auth0AuthenticationChangeHandler};
