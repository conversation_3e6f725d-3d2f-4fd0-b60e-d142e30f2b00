const keywayPermissions = {
  keywayAccessAssetManager: "keyway:access:asset_manager",
  keywayAccessDealRoom: "keyway:access:deal_room",
  keywayAccessDealRoomAdmin: "keyway:access:deal_room_admin",
  keywayAccessKeybrain: "keyway:access:keybrain",
  keywayAccessKeycomps: "keyway:access:keycomps",
  keywayAccessKeydocs: "keyway:access:keydocs",
  keywayAccessKeypilot: "keyway:access:keypilot",
  keywayAccessPropertySage: "keyway:access:property_sage",
  keywayUsersCreateInOtherOrg: "keyway:users:create_in_other_org",
  keywayAccessKeyreviewsManager: "keyway:access:keyreviews_manager"
} as const;

const keywayAccessPermissions: KeywayPermission[] = [
  keywayPermissions.keywayAccessAssetManager,
  keywayPermissions.keywayAccessDealRoomAdmin,
  keywayPermissions.keywayAccessDealRoom,
  keywayPermissions.keywayAccessKeybrain,
  keywayPermissions.keywayAccessKeycomps,
  keywayPermissions.keywayAccessKeydocs,
  keywayPermissions.keywayAccessKeypilot,
  keywayPermissions.keywayAccessPropertySage,
  keywayPermissions.keywayAccessKeyreviewsManager
];

const isKeywayAccessPermission = (
  permission: KeywayPermission
): permission is AccessKeywayPermission =>
  keywayAccessPermissions.includes(permission);

type KeywayPermission =
  (typeof keywayPermissions)[keyof typeof keywayPermissions];

type AccessKeywayPermission =
  | typeof keywayPermissions.keywayAccessAssetManager
  | typeof keywayPermissions.keywayAccessDealRoom
  | typeof keywayPermissions.keywayAccessDealRoomAdmin
  | typeof keywayPermissions.keywayAccessKeybrain
  | typeof keywayPermissions.keywayAccessKeycomps
  | typeof keywayPermissions.keywayAccessKeydocs
  | typeof keywayPermissions.keywayAccessKeypilot
  | typeof keywayPermissions.keywayAccessKeyreviewsManager
  | typeof keywayPermissions.keywayAccessPropertySage;

export {keywayPermissions, isKeywayAccessPermission};

export type {AccessKeywayPermission, KeywayPermission};
