import {useEffect, useMemo} from "react";

import * as withAuth0AccessTokenClaims from "./auth0-access-token-claims";
import useAuth0AccessTokenClaims from "./use-auth0-access-token-claims";
import {useAuth0Auth} from "./use-auth0-auth";

const useLogoutIfNeeded = (minAccessTokenIssuedDate?: Date) => {
  const {logoutTo} = useAuth0Auth();

  const accessTokenClaimsResult = useAuth0AccessTokenClaims();

  const isLoading =
    minAccessTokenIssuedDate !== undefined &&
    (accessTokenClaimsResult.status === "idle" ||
      accessTokenClaimsResult.status === "pending");

  const willLogout = useMemo(() => {
    if (
      !minAccessTokenIssuedDate ||
      accessTokenClaimsResult.status === "failed"
    ) {
      return false;
    }

    if (
      accessTokenClaimsResult.status === "idle" ||
      accessTokenClaimsResult.status === "pending"
    ) {
      return undefined;
    }

    const accessTokenClaims = accessTokenClaimsResult.data;

    const accessTokenIssuedDate =
      withAuth0AccessTokenClaims.getIssuedDate(accessTokenClaims);

    return accessTokenIssuedDate < minAccessTokenIssuedDate;
  }, [accessTokenClaimsResult, minAccessTokenIssuedDate]);

  useEffect(() => {
    if (willLogout) {
      logoutTo();
    }
  }, [logoutTo, willLogout]);

  return {isLoading, willLogout};
};

export {useLogoutIfNeeded};
