import {CustomError} from "@mgtitimoli/utils-error";

import ignoreError from "./ignore-error";

const isCancelablePromiseError = (error: Error) =>
  error instanceof PromiseCanceledError;

class PromiseCanceledError extends CustomError {
  static ignore<TResult>(promise: Promise<TResult>): Promise<TResult | void> {
    return ignoreError(promise, isCancelablePromiseError);
  }
}

export default PromiseCanceledError;
