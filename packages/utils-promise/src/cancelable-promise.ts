import PromiseCanceledError from "./promise-canceled-error";

type Cancel = (reason?: string) => void;

type CancelablePromise<TResult> = {
  cancel: Cancel;
  promise: Promise<TResult>;
};

type IgnoredCancelablePromise<TResult> = CancelablePromise<TResult | void>;

const create = <TResult>(
  inputPromise: Promise<TResult>
): CancelablePromise<TResult> => {
  let cancel: Cancel;

  const promise = new Promise<TResult>((resolve, reject) => {
    cancel = reason => reject(new PromiseCanceledError(reason));

    inputPromise.then(resolve);
    inputPromise.catch(reject);
  });

  return {
    // @ts-expect-error
    cancel,
    promise
  };
};

const createIgnored = <TResult>(
  promise: Promise<TResult>
): IgnoredCancelablePromise<TResult> => {
  const cancelablePromise = create(promise);

  return {
    cancel: cancelablePromise.cancel,
    promise: PromiseCanceledError.ignore(cancelablePromise.promise)
  };
};

export {create, createIgnored};

export type {CancelablePromise, IgnoredCancelablePromise};
