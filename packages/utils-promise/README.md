# utils-promise

## Installation

```
yarn add @unlockre/utils-promise
```

## API

### ignoreError

#### Description

`ignoreError` function can be used to obtain a new promise from a given one, but ignoring one or more errors from it.

#### Example

```typescript
import * as withPromise from "@unlockre/utils-promise/dist";

const rejectedPromise = Promise.reject<number>("Something");

const shouldIgnoreError = (error: unknown) => error === "Something";

withPromise
  .ignoreError(rejectedPromise, shouldIgnoreError)
  .then(result => {
    // Given there is a chance for the promise for being rejected, now result
    // instead of being number, it would be number | void
  });
```

### PromiseCanceledError

#### Description

`PromiseCanceledError` class extends from Error and it is used by `CancelablePromise` when the promise it contains is rejected using `cancel`.

#### Types

```typescript
declare class PromiseCanceledError extends CustomError {
    static ignore<TResult>(promise: Promise<TResult>): Promise<TResult | void>;
}
```

### CancelablePromise

#### Description

A `CancelablePromise` is composed by a promise (created from a given promise) that can be canceled at any time, and a cancel function provided to do this.

#### Types

```typescript
type CancelablePromise<TResult> = {
  cancel: (reason?: string) => void;
  promise: Promise<TResult>;
};
```

#### Functions

---

##### create

###### Description

`create` function can be used to create a `CancelablePromise` from a given promise but without ignoring the `PromiseCanceledError` provided when `cancel` function is called.

> If you would like to ignore the `PromiseCanceledError` provided when `cancel` function is called, you can use `createIgnored`.

###### Example

```typescript
import PromiseCanceledError from "@unlockre/utils-promise/dist/promise-canceled-error";
import * as withCancelablePromise from "@unlockre/utils-promise/dist/cancelable-promise";

const originalPromise = Promise.resolve("Resolved!");

const cancelablePromise = withCancelablePromise.create(originalPromise);

// Important:
// If no catch function is provided, the CancelablePromise promise is gonna
// failed without a handler
cancelablePromise.catch(error => {
  if (!(error instanceof PromiseCanceledError)) throw error;

  console.log(error.message); // Cancelation reason

  // Original Promise has been canceled, do something
});

cancelablePromise.cancel("Cancelation reason");
```

---

##### createIgnored

###### Description

`createIgnored` function can be used to create a `CancelablePromise` from a given promise ignoring the `PromiseCanceledError` provided when `cancel` function is called.

> If you would like to handle the `PromiseCanceledError` provided when `cancel` function is called, you can use `create`.

###### Example

```typescript
import PromiseCanceledError from "@unlockre/utils-promise/dist/promise-canceled-error";
import * as withCancelablePromise from "@unlockre/utils-promise/dist/cancelable-promise";

const originalPromise = Promise.resolve("Resolved!");

// Important:
// There is no need to provide a handled as if we create it this way,
// PromiseCanceledError are gonna be ignored
const cancelablePromise = withCancelablePromise.createIgnored(originalPromise);


cancelablePromise.cancel("Cancelation reason");
```
