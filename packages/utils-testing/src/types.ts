type UnprefixedTestIds<TTestIdKey extends string, TPrefix extends string> = {
  [TKey in TTestIdKey as TTestId<PERSON><PERSON> extends `${TPrefix}${infer UnprefixedTestIdKey}`
    ? Uncapitalize<UnprefixedTestIdKey>
    : never]?: string;
};

type PrefixedTestIds<TTestIdKey extends string, TPrefix extends string> = {
  [TKey in TTestIdKey as `${TPrefix}${Capitalize<TKey>}`]?: string;
};

type AnyTestIds = Partial<Record<string, string>>;

export type {AnyTestIds, PrefixedTestIds, UnprefixedTestIds};
