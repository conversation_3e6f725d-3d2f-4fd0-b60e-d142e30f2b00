import * as withString from "@unlockre/utils-string/dist";

import type {AnyTestIds, PrefixedTestIds, UnprefixedTestIds} from "./types";

const unprefixWith = <TTestIds extends AnyTestIds, TPrefix extends string>(
  testIds: TTestIds,
  prefix: TPrefix
) =>
  Object.entries(testIds).reduce(
    (unprefixedTestIds, [key, value]) => ({
      ...unprefixedTestIds,
      [withString.uncapitalize(key.replace(prefix, ""))]: value
    }),
    {} as UnprefixedTestIds<Extract<keyof TTestIds, string>, TPrefix>
  );

const prefixWith = <TTestIds extends AnyTestIds, TPrefix extends string>(
  testIds: TTestIds,
  prefix: TPrefix
) =>
  Object.entries(testIds).reduce(
    (prefixedTestIds, [key, value]) => ({
      ...prefixedTestIds,
      [prefix + withString.capitalize(key)]: value
    }),
    {} as PrefixedTestIds<Extract<keyof TTestIds, string>, TPrefix>
  );

export {prefixWith, unprefixWith};
