# [@unlockre/utils-object-v3.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v2.0.0...@unlockre/utils-object-v3.0.0) (2025-02-01)


### Features

* Add new types and utilities ([#211](https://github.com/unlockre/utils-packages/issues/211)) ([4db18c2](https://github.com/unlockre/utils-packages/commit/4db18c2957f5430446e3bf401d851574023f62ff))


### BREAKING CHANGES

* Use named exports instead of default exports

# [@unlockre/utils-object-v2.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.8.0...@unlockre/utils-object-v2.0.0) (2024-09-03)


### Features

* Add compareUsing utility ([#169](https://github.com/unlockre/utils-packages/issues/169)) ([703d48c](https://github.com/unlockre/utils-packages/commit/703d48cf2a73dc598206ac0be7bd51e4185e3c82))


### BREAKING CHANGES

* Deprecate compareAsc and CompareDesc utilities

# [@unlockre/utils-object-v1.8.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.7.2...@unlockre/utils-object-v1.8.0) (2024-04-23)


### Features

* Add types and getEntries utility ([#127](https://github.com/unlockre/utils-packages/issues/127)) ([0b41065](https://github.com/unlockre/utils-packages/commit/0b41065f77b8241609c44d7dbaea5817da8a2aa8))

# [@unlockre/utils-object-v1.7.2](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.7.1...@unlockre/utils-object-v1.7.2) (2023-09-14)


### Bug Fixes

* Fix compare asc by ([#78](https://github.com/unlockre/utils-packages/issues/78)) ([9dfa7c3](https://github.com/unlockre/utils-packages/commit/9dfa7c39d335b4aaeb3557365e97aa28f3111d9d))

# [@unlockre/utils-object-v1.7.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.7.0...@unlockre/utils-object-v1.7.1) (2023-09-12)


### Bug Fixes

* Fix utils-object compare fns types ([#76](https://github.com/unlockre/utils-packages/issues/76)) ([a50917e](https://github.com/unlockre/utils-packages/commit/a50917e173308a8e5cd83d0faa002643f284aa98))

# [@unlockre/utils-object-v1.7.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.6.0...@unlockre/utils-object-v1.7.0) (2023-09-12)


### Features

* Add utils-object compare-by ([#73](https://github.com/unlockre/utils-packages/issues/73)) ([2e7a41d](https://github.com/unlockre/utils-packages/commit/2e7a41da3e97eac080bfc2af2ad0d3900d088354))

# [@unlockre/utils-object-v1.6.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.5.0...@unlockre/utils-object-v1.6.0) (2023-08-10)


### Features

* Add hasProperty utility ([#65](https://github.com/unlockre/utils-packages/issues/65)) ([086e256](https://github.com/unlockre/utils-packages/commit/086e256ef75ffbab009508ef22476a2da3a75ee6))

# [@unlockre/utils-object-v1.5.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.4.0...@unlockre/utils-object-v1.5.0) (2023-04-13)


### Features

* Add areEqual and pick object utils ([#30](https://github.com/unlockre/utils-packages/issues/30)) ([639974a](https://github.com/unlockre/utils-packages/commit/639974a415f21e0a6d336ff64e3d277db2f6928f))

# [@unlockre/utils-object-v1.4.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.3.0...@unlockre/utils-object-v1.4.0) (2022-09-22)


### Features

* Add utils-formatting package ([#22](https://github.com/unlockre/utils-packages/issues/22)) ([893075c](https://github.com/unlockre/utils-packages/commit/893075c3e479a727e9d3adf96c68b48b37de503b))

# [@unlockre/utils-object-v1.3.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.2.0...@unlockre/utils-object-v1.3.0) (2022-07-28)


### Features

* Add utils-dom package ([#18](https://github.com/unlockre/utils-packages/issues/18)) ([46543cb](https://github.com/unlockre/utils-packages/commit/46543cbb83d6dfffe9765aab53b52ba9845c32e1))

# [@unlockre/utils-object-v1.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.1.0...@unlockre/utils-object-v1.2.0) (2022-07-21)


### Features

* Add StringKeyOf type ([#16](https://github.com/unlockre/utils-packages/issues/16)) ([9a667e8](https://github.com/unlockre/utils-packages/commit/9a667e88b5a668b0c3bc861ea94cb5c1b5ad0e9e))

# [@unlockre/utils-object-v1.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-object-v1.0.0...@unlockre/utils-object-v1.1.0) (2022-03-29)


### Features

* Add @unlockre/utils-intl package ([#3](https://github.com/unlockre/utils-packages/issues/3)) ([3aa4eda](https://github.com/unlockre/utils-packages/commit/3aa4eda9fb669f033c686af010a8af7b33a45ccf))

# @unlockre/utils-object-v1.0.0 (2022-03-09)


### Features

* **utils-object:** Add @unlockre/utils-object package ([51b9584](https://github.com/unlockre/utils-packages/commit/51b95842f5ef2503e1d87e1c48ae4b1bb73ac8c2))
