# utils-swr

## Install

```
yarn add @unlockre/utils-swr
```

### Peer dependencies

- **swr**: `^2.0.0`

## API

### SWRResponse.throwIfError

#### Description

Function that throws an error if the given `SWRResponse`.

> This function is usually used in combination of swrWithCustomResponse utility (see below)

#### Example

```ts
import * as withSwrResponse from "@unlockre/utils-swr/dist/swr-response";

type Params = {
  dealId: number;
  fileId: string;
};

const useKeypilotFileChat = ({dealId, fileId}: Params) => {
  // ...

  const fileQuestionsResponse = useDealRoomApiGet(
    "/deal/{deal-id}/file/{file-id}/questions",
    {
      /* eslint-disable @typescript-eslint/naming-convention */
      "deal-id": String(dealId),
      "file-id": fileId
      /* eslint-enabled */
    },
    {
      // ...
      refreshInterval: refreshHistoryReq,
    }
  );

  withSwrResponse.throwIfError(fileQuestionsResponse);

  // ...
};
```

### swrWithCustomResponse

#### Description

This function receives a **swr like hook** (`useSwr`) and a response transformer and returns a new **swr like hook** that returns the result of applying the transformer function to the `SWRResponse`.

> This function can be used to wrap any swr like hook to create a new one that throws an error if the request failed (see the example below)

#### Example

```ts
import {swrWithCustomResponse} from "@unlockre/utils-swr/dist";
import * as withSwrResponse from "@unlockre/utils-swr/dist/swr-response";

import {bindUseOpenApiGet} from "@unlockre/open-api-client/dist";

import type {paths} from "./deal-room-api-schema";
import useDealRoomApiClient from "./use-deal-room-api-client";

const useDealRoomApiGet = swrWithCustomResponse(
  bindUseOpenApiGet(useDealRoomApiClient),
  withSwrResponse.throwIfError
);

export {useDealRoomApiGet};
```

### useLimitedRefreshInterval

#### Description

Hook that returns a refreshInterval function configured using the following params:
- `intervalMs`: the amount of milliseconds of the refresh interval
- `shouldContinue`: function that returns a boolean indicating if it should continue or not

#### Example

```ts
import type {OpenApiResponse} from "@unlockre/open-api-client/dist";
import {useLimitedRefreshInterval} from "@unlockre/utils-swr/dist";

import type {paths} from "@/utils/deal-room-api/deal-room-api-schema";

type Params = {
  dealId: number;
  fileId: string;
};

type FileHistoryResponse = OpenApiResponse<
  paths,
  "/deal/{deal-id}/file/{file-id}/questions",
  "get"
>;

const shouldHistoryRefresh = (
  fileHistoryResponse: FileHistoryResponse,
  retriesCount: number
) =>
  retriesCount < retriesCountLimit &&
  !fileHistoryResponse?.data.history?.length;

const useKeypilotFileChat = ({dealId, fileId}: Params) => {
  // ...

  const refreshHistoryReq = useLimitedRefreshInterval<FileHistoryResponse>({
    intervalMs: 10000,
    shouldContinue: (fileHistory: FileHistoryResponse, retriesCount: number) =>
      shouldHistoryRefresh(fileHistory, retriesCount)
  });

  const fileQuestionsResponse = useDealRoomApiGet(
    "/deal/{deal-id}/file/{file-id}/questions",
    {
      /* eslint-disable @typescript-eslint/naming-convention */
      "deal-id": String(dealId),
      "file-id": fileId
      /* eslint-enabled */
    },
    {
      // ...
      refreshInterval: refreshHistoryReq,
    }
  );

  // ...
};
```
