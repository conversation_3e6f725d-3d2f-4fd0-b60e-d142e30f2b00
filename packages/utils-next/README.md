# utils-next

## Installation

```
yarn add @unlockre/utils-next
```

### Peer dependencies

- **next**: `^12.0.0`
- **react**: `^17.0.0`

## Utilities

### useIsomorphicRouter

#### Description

This hook replicates the behavior of **Next** [useRouter](https://nextjs.org/docs/pages/api-reference/functions/use-router), but starts with `isReady: false` to keep the same value this field has on the server and avoid SSR issues.

#### Example

```tsx
import {useIsomorphicRouter} from "@unlockre/utils-next/dist";

const MyComponent = () => {
  const router = useIsomorphicRouter();

  return (
    <div>
      <div>Is router ready?: {router.isReady}</div>
      <div>Base path: {router.basePath}</div>
    </div>
  );
};
```

### useIsRouteMatched

#### Description

This hook checks if the current route matches any of the specified paths.

#### Example

```tsx
import {useIsRouteMatched} from "@unlockre/utils-next/dist";

const MyComponent = () => {
  const {isRouteReady, isRouteMatched} = useIsRouteMatched(["/some-path"]);

  return (
    <div>
      <div>Is route ready?: {isRouteReady}</div>
      <div>Is route matched?: {isRouteMatched}</div>
    </div>
  );
};
```

### usePushNestedRoute

#### Description

This hook returns a `pushNestedRoute` function to navigate to a nested route just specifying the nested path.

#### Example

```tsx
import {usePushNestedRoute} from "@unlockre/utils-next/dist";

const MyComponent = () => {
  const {pushNestedRoute} = usePushNestedRoute();

  const handleClick = () => pushNestedRoute("/some-path");

  return (
    <div>
      <button onClick={handleClick}>Go to some path</button>
    </div>
  );
};
```

### useSetDefaultUrlQuery

#### Description

This hook sets the default url query using the provided `getDefaultUrlQueryFrom` function when the given `getUrlQueryFrom` function fails to get the url query from `router.query`. A boolean value is returned to indicate if the url query is ready or not.

> **IMPORTANT**: Rendering should be stopped until the url query is ready, so the nested components can use `useUrlQuery` hook and access the url query with the defaults set.

#### Example

**some-screen-url-query.ts**

```ts
import {z} from "zod";
import type {NextUrlQuery} from "@unlockre/utils-next/dist";

import {defaultTabName, tabNameSchema} from "./tab-name";

const someScreenUrlQuerySchema = z.object({
  compSetId: z.string().min(1, {message: "Required"}),
  tab: tabNameSchema
});

const getFrom = (nextUrlQuery: NextUrlQuery) =>
  someScreenUrlQuerySchema.parse(nextUrlQuery);

const getDefaultFrom = (nextUrlQuery: NextUrlQuery) =>
  someScreenUrlQuerySchema.parse({
    ...nextUrlQuery,
    tab: defaultTabName
  });

export {getDefaultFrom, getFrom};
```

**use-some-screen.ts**

```ts
import {useSetDefaultUrlQuery} from "@unlockre/utils-next/dist";

import * as withUrlQuery from "./url-query";

const useSomeScreen = () => {
  const isUrlQueryReady = useSetDefaultUrlQuery(
    withUrlQuery.getFrom,
    withUrlQuery.getDefaultFrom
  );

  // Rendering should be stopped until the url query is ready, so the nested
  // components can use `useUrlQuery` hook and access the url query with the
  // defaults set
  if (!isUrlQueryReady) {
    return null;
  }

  // ...
};

export {useSomeScreen};
```

### useUrlQuery

#### Description

This hook manages and manipulates the query parameters of the current URL in a Next.js application.

#### Example

**some-screen-url-query.ts**

```ts
import {z} from "zod";
import type {NextUrlQuery} from "@unlockre/utils-next/dist";

import {defaultTabName, tabNameSchema} from "./tab-name";

const someScreenUrlQuerySchema = z.object({
  compSetId: z.string().min(1, {message: "Required"}),
  tab: tabNameSchema
});

const getFrom = (nextUrlQuery: NextUrlQuery) =>
  someScreenUrlQuerySchema.parse(nextUrlQuery);

const getDefaultFrom = (nextUrlQuery: NextUrlQuery) =>
  someScreenUrlQuerySchema.parse({
    ...nextUrlQuery,
    tab: defaultTabName
  });

export {getDefaultFrom, getFrom};
```

**some-component.ts**

```tsx
import {useUrlQuery} from "@unlockre/utils-next/dist";

import * as withSomeScreenUrlQuery from "./some-screen-url-query";

const SomeComponent = () => {
  const {setUrlQuery, urlQuery} = useUrlQuery(withSomeScreenUrlQuery.getFrom);

  return (
    <div>
      <button onClick={() => setUrlQuery({tab: homeTab.name})}>Home</button>
    </div>
  );
};

export {SomeComponent};
```
