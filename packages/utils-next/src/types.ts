import type {NextRouter} from "next/router";

type NextUrlQuery = NextRouter["query"];

type NextUrlInput = Exclude<Parameters<NextRouter["push"]>[0], string>;

type NextUrlQueryInput = Exclude<
  NextUrlInput["query"],
  string | null | undefined
>;

type ParseNextUrlQuery<TNextUrlQueryInput extends NextUrlQueryInput> = (
  nextUrlQuery: NextUrlQuery
) => TNextUrlQueryInput;

export type {NextUrlInput, NextUrlQuery, NextUrlQueryInput, ParseNextUrlQuery};
