import {useRouter} from "next/router";
import {useEffect} from "react";

import type {NextUrlQueryInput, ParseNextUrlQuery} from "./types";
import {useUrlQuery} from "./use-url-query";

const useSetDefaultUrlQuery = <TUrlQuery extends NextUrlQueryInput>(
  getUrlQueryFrom: ParseNextUrlQuery<TUrlQuery>,
  getDefaultUrlQueryFrom: ParseNextUrlQuery<TUrlQuery>
) => {
  const router = useRouter();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const {setUrlQuery, urlQuery} = useUrlQuery(getUrlQueryFrom, () => {});

  useEffect(() => {
    if (!urlQuery) {
      setUrlQuery(getDefaultUrlQueryFrom(router.query));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.query, urlQuery]);

  return urlQuery !== undefined;
};

export {useSetDefaultUrlQuery};
