import {useIsomorphicRouter} from "./use-isomorphic-router";

const getRoutePath = (routePathWithQuery: string) =>
  routePathWithQuery.split("?")[0];

const useIsRouteMatched = (routePaths: string[]) => {
  const router = useIsomorphicRouter();

  return {
    isRouteReady: router.isReady,
    isRouteMatched:
      router.isReady && routePaths.includes(getRoutePath(router.asPath))
  };
};

export {useIsRouteMatched};
