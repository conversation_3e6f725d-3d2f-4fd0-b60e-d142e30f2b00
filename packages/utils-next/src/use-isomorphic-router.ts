import {useRouter} from "next/router";
import type {NextRouter} from "next/router";
import {useEffect, useState} from "react";

const useIsomorphicRouter = (): NextRouter => {
  const [isRouterReady, setIsRouterReady] = useState(false);

  const router = useRouter();

  useEffect(() => {
    setIsRouterReady(router.isReady);
  }, [router.isReady]);

  return {...router, isReady: isRouterReady};
};

export {useIsomorphicRouter};
