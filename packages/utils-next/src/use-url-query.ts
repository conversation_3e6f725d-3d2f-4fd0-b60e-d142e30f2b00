import * as withObject from "@unlockre/utils-object/dist";
import {useRouter} from "next/router";
import {useCallback, useMemo} from "react";

import type {NextUrlQueryInput, ParseNextUrlQuery} from "./types";

type ErrorHandler = (error: unknown) => unknown;

type Result<TUrlQuery extends NextUrlQueryInput | undefined> = {
  replaceUrlQuery: (urlQuery: Partial<NonNullable<TUrlQuery>>) => void;
  setUrlQuery: (urlQuery: Partial<NonNullable<TUrlQuery>>) => void;
  urlQuery: TUrlQuery;
};

type ResultFrom<
  TUrlQuery extends NextUrlQueryInput,
  TOnError extends ErrorHandler | undefined
> = TOnError extends undefined
  ? Result<TUrlQuery>
  : Result<TUrlQuery | undefined>;

const useUrlQuery = <
  TUrlQuery extends NextUrlQueryInput,
  TRest extends [] | [ErrorHandler]
>(
  getUrlQueryFrom: ParseNextUrlQuery<TUrlQuery>,
  ...[onError]: TRest
) => {
  const router = useRouter();

  const urlQuery = useMemo(
    () => {
      if (!onError) {
        return getUrlQueryFrom(router.query);
      }

      try {
        return getUrlQueryFrom(router.query);
      } catch (error) {
        onError(error);

        return undefined;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [router.query]
  );

  const getUrlQuery = useCallback(
    (someUrlQuery: Partial<TUrlQuery>) =>
      withObject.omitEntries(
        {...router.query, ...someUrlQuery} as TUrlQuery,
        withObject.isNullishEntry
      ),
    [router.query]
  );

  const replaceUrlQuery = useCallback(
    (someUrlQuery: Partial<TUrlQuery>) => {
      router.replace(
        {
          pathname: router.pathname,
          query: getUrlQuery(someUrlQuery)
        },
        undefined,
        {shallow: true}
      );
    },
    [getUrlQuery, router]
  );

  const setUrlQuery = useCallback(
    (someUrlQuery: Partial<TUrlQuery>) => {
      router.push(
        {
          pathname: router.pathname,
          query: getUrlQuery(someUrlQuery)
        },
        undefined,
        {shallow: true}
      );
    },
    [getUrlQuery, router]
  );

  return {
    urlQuery,
    replaceUrlQuery,
    setUrlQuery
  } as ResultFrom<TUrlQuery, TRest[0]>;
};

export {useUrlQuery};
