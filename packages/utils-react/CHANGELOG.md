# [@unlockre/utils-react-v3.1.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v3.1.0...@unlockre/utils-react-v3.1.1) (2025-07-10)


### Bug Fixes

* Early return for unmounted elements ([#249](https://github.com/unlockre/utils-packages/issues/249)) ([2c6a0c7](https://github.com/unlockre/utils-packages/commit/2c6a0c73a345b04cd8a60c4175d152a123feb8ce))

# [@unlockre/utils-react-v3.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v3.0.0...@unlockre/utils-react-v3.1.0) (2025-06-05)


### Features

* Allow passing args to useAsync returned function ([#241](https://github.com/unlockre/utils-packages/issues/241)) ([11d3145](https://github.com/unlockre/utils-packages/commit/11d3145f7db4690c389af5ce33a43cc788514fdd))

# [@unlockre/utils-react-v3.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v2.3.0...@unlockre/utils-react-v3.0.0) (2025-03-31)


### Bug Fixes

* Update react dependencies ([#232](https://github.com/unlockre/utils-packages/issues/232)) ([f98c37c](https://github.com/unlockre/utils-packages/commit/f98c37c119e6b5aee33e3860a51e30beb2c48460))


### BREAKING CHANGES

* Update react dependencies

# [@unlockre/utils-react-v2.3.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v2.2.1...@unlockre/utils-react-v2.3.0) (2025-03-27)


### Features

* Add useWindowEvent ([#230](https://github.com/unlockre/utils-packages/issues/230)) ([a3a8fd8](https://github.com/unlockre/utils-packages/commit/a3a8fd856823d9243cce06436bb6759059df413e))

# [@unlockre/utils-react-v2.2.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v2.2.0...@unlockre/utils-react-v2.2.1) (2025-02-07)


### Bug Fixes

* Add React 18 to peerDependencies ([#214](https://github.com/unlockre/utils-packages/issues/214)) ([a3afa9f](https://github.com/unlockre/utils-packages/commit/a3afa9ffcccf41e038e4708b5eb5530e1ceb860b))

# [@unlockre/utils-react-v2.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v2.1.0...@unlockre/utils-react-v2.2.0) (2025-01-31)


### Features

* Add useResizeObserver react hook ([#209](https://github.com/unlockre/utils-packages/issues/209)) ([96ec8a7](https://github.com/unlockre/utils-packages/commit/96ec8a7d1ecbce457fbb15a4698c37203979ddec))

# [@unlockre/utils-react-v2.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v2.0.0...@unlockre/utils-react-v2.1.0) (2025-01-24)


### Features

* Add reset useArray action ([#204](https://github.com/unlockre/utils-packages/issues/204)) ([2bc1eba](https://github.com/unlockre/utils-packages/commit/2bc1eba6eae784ef9062e3e936fb3b0932ea0291))

# [@unlockre/utils-react-v2.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.13.0...@unlockre/utils-react-v2.0.0) (2025-01-13)


### Features

* Return clearExecution from useAsync ([#194](https://github.com/unlockre/utils-packages/issues/194)) ([505bbbf](https://github.com/unlockre/utils-packages/commit/505bbbfed4e179f135b2ca1f834ed307730985dd))


### BREAKING CHANGES

* Change useAsync result type

# [@unlockre/utils-react-v1.13.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.12.0...@unlockre/utils-react-v1.13.0) (2024-07-25)


### Features

* Add media query and print view hooks ([#156](https://github.com/unlockre/utils-packages/issues/156)) ([582c718](https://github.com/unlockre/utils-packages/commit/582c7189c7cef42cc0a6bb53644402efc6e0672b))

# [@unlockre/utils-react-v1.12.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.11.0...@unlockre/utils-react-v1.12.0) (2024-06-25)


### Features

* Add react hooks ([#144](https://github.com/unlockre/utils-packages/issues/144)) ([2ac85fd](https://github.com/unlockre/utils-packages/commit/2ac85fda93acafaae05a64e2fb49e04b9a08e7d7))

# [@unlockre/utils-react-v1.11.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.10.1...@unlockre/utils-react-v1.11.0) (2024-04-23)


### Features

* Add types and getEntries utility ([#127](https://github.com/unlockre/utils-packages/issues/127)) ([0b41065](https://github.com/unlockre/utils-packages/commit/0b41065f77b8241609c44d7dbaea5817da8a2aa8))

# [@unlockre/utils-react-v1.10.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.10.0...@unlockre/utils-react-v1.10.1) (2024-02-06)


### Bug Fixes

* Add uuid package as dependency ([#106](https://github.com/unlockre/utils-packages/issues/106)) ([079ecb7](https://github.com/unlockre/utils-packages/commit/079ecb7c67242ff085fe651cc507ee7532e342a5))

# [@unlockre/utils-react-v1.10.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.9.1...@unlockre/utils-react-v1.10.0) (2024-01-31)


### Features

* Add useId react utility (hook) ([#105](https://github.com/unlockre/utils-packages/issues/105)) ([9dfd145](https://github.com/unlockre/utils-packages/commit/9dfd145b24d2021cb0f048e7d7f685ac158fda8d))

# [@unlockre/utils-react-v1.9.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.9.0...@unlockre/utils-react-v1.9.1) (2023-10-18)


### Bug Fixes

* Rollback to default exports ([#86](https://github.com/unlockre/utils-packages/issues/86)) ([64761c9](https://github.com/unlockre/utils-packages/commit/64761c9983166c8bd2382d4b2f4f060adf733d3d))

# [@unlockre/utils-react-v1.9.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.8.0...@unlockre/utils-react-v1.9.0) (2023-10-12)


### Bug Fixes

* Add missing export ([#84](https://github.com/unlockre/utils-packages/issues/84)) ([776c4a0](https://github.com/unlockre/utils-packages/commit/776c4a09c1aa17fdbfc714c8ffb435d32c898d3b))


### Features

* Add useCompareWithPrev hook ([#82](https://github.com/unlockre/utils-packages/issues/82)) ([c6c7447](https://github.com/unlockre/utils-packages/commit/c6c7447b5036ce6ec0237e0894e6c53a7cc30157))

# [@unlockre/utils-react-v1.8.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.7.2...@unlockre/utils-react-v1.8.0) (2023-09-05)


### Features

* Add react state types ([#70](https://github.com/unlockre/utils-packages/issues/70)) ([04efd9d](https://github.com/unlockre/utils-packages/commit/04efd9d64016d345c149a2a72b5db1576db8e3b9))

# [@unlockre/utils-react-v1.7.2](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.7.1...@unlockre/utils-react-v1.7.2) (2023-07-18)


### Bug Fixes

* Add align to top param to scroll into view ([#53](https://github.com/unlockre/utils-packages/issues/53)) ([c43c9b9](https://github.com/unlockre/utils-packages/commit/c43c9b9424b93a7900867ee011e0ebf149883c1b))
* Fix useScrollIntoView README.md ([#54](https://github.com/unlockre/utils-packages/issues/54)) ([b99b3c9](https://github.com/unlockre/utils-packages/commit/b99b3c96d045cfabae6eb2f18830beb207a65c2c))

# [@unlockre/utils-react-v1.7.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.7.0...@unlockre/utils-react-v1.7.1) (2023-07-17)


### Bug Fixes

* Fix useScrollIntoView ([#52](https://github.com/unlockre/utils-packages/issues/52)) ([59ca252](https://github.com/unlockre/utils-packages/commit/59ca252a6b5ad8bd3601748c71a64949d6b2cf3d))

# [@unlockre/utils-react-v1.7.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.6.0...@unlockre/utils-react-v1.7.0) (2023-07-17)


### Features

* Add useTypeAnimation hook ([#51](https://github.com/unlockre/utils-packages/issues/51)) ([a12b29a](https://github.com/unlockre/utils-packages/commit/a12b29a301d9bf370eaf42a7f0adb267f597af44))

# [@unlockre/utils-react-v1.6.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.5.1...@unlockre/utils-react-v1.6.0) (2023-06-07)


### Features

* Add useAsync and useScrollIntoView react utilities  ([#34](https://github.com/unlockre/utils-packages/issues/34)) ([2b3b1a2](https://github.com/unlockre/utils-packages/commit/2b3b1a2f4f5d4c23444c04b1262795fe31b21f0b))

# [@unlockre/utils-react-v1.5.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.5.0...@unlockre/utils-react-v1.5.1) (2023-04-13)


### Bug Fixes

* Add utils-object package ([#32](https://github.com/unlockre/utils-packages/issues/32)) ([cfff4e0](https://github.com/unlockre/utils-packages/commit/cfff4e06775b6b4935a40cb3550942c3f99725d6))

# [@unlockre/utils-react-v1.5.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.4.0...@unlockre/utils-react-v1.5.0) (2023-04-13)


### Features

* Add hooks useElementEvent and useElementStyle react utils ([#31](https://github.com/unlockre/utils-packages/issues/31)) ([729ba9b](https://github.com/unlockre/utils-packages/commit/729ba9ba19a4c2de0bb66f74036a4920ac2a4624))

# [@unlockre/utils-react-v1.4.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.3.0...@unlockre/utils-react-v1.4.0) (2022-09-22)


### Features

* Add utils-formatting package ([#22](https://github.com/unlockre/utils-packages/issues/22)) ([893075c](https://github.com/unlockre/utils-packages/commit/893075c3e479a727e9d3adf96c68b48b37de503b))

# [@unlockre/utils-react-v1.3.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.2.1...@unlockre/utils-react-v1.3.0) (2022-09-06)


### Features

* Add useMutationObserver hook (utils-react) ([#21](https://github.com/unlockre/utils-packages/issues/21)) ([4c20f12](https://github.com/unlockre/utils-packages/commit/4c20f1263e608f250da9c04f98acf2a82abb545e))

# [@unlockre/utils-react-v1.2.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.2.0...@unlockre/utils-react-v1.2.1) (2022-08-12)


### Bug Fixes

* Check if element exists ([#20](https://github.com/unlockre/utils-packages/issues/20)) ([f231f80](https://github.com/unlockre/utils-packages/commit/f231f80f7d679ca2a0588e9bbddd6944b6874551))

# [@unlockre/utils-react-v1.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.1.0...@unlockre/utils-react-v1.2.0) (2022-07-28)


### Features

* Add utils-dom package ([#18](https://github.com/unlockre/utils-packages/issues/18)) ([46543cb](https://github.com/unlockre/utils-packages/commit/46543cbb83d6dfffe9765aab53b52ba9845c32e1))

# [@unlockre/utils-react-v1.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-react-v1.0.0...@unlockre/utils-react-v1.1.0) (2022-05-17)


### Features

* Add useClickOutside react hook to @unlockre/utils-react ([#8](https://github.com/unlockre/utils-packages/issues/8)) ([c4c5ac8](https://github.com/unlockre/utils-packages/commit/c4c5ac8112133e0b847bbf1108c02773689d4b76))

# @unlockre/utils-react-v1.0.0 (2022-05-17)


### Features

* Add utils-react package ([#7](https://github.com/unlockre/utils-packages/issues/7)) ([b3357b6](https://github.com/unlockre/utils-packages/commit/b3357b648f22ad39706e23fcb98bfeaa25b1a49e))
