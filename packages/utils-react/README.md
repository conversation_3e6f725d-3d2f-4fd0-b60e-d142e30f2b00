# utils-react

## Types

### ReactSetStateFrom<TState>

#### Description

Given a state type, it returns its setter type.

```ts
type ReactSetStateFrom<TState> = Dispatch<SetStateAction<TState>>;

```

### ReactSetStateFrom<TState>

#### Description

Given a type, it returns a tuple with its value and setter type.

```ts
type ReactStateTupleFrom<TState> = [TState, ReactSetStateFrom<TState>];

```

## Utilities

### useArray

#### Description

Custom hook that receives an array and returns a copy of it with a set of functions to allow modifying it in an immutable way.

#### Example

```tsx
import {useArray} from "@unlockre/utils-react/dist";

import {trpc} from "@/utils/trpc";

type User = {
  fullAddress: string;
  id: string;
  name: string;
};

const useUsers = () => {
  const usersResponse = trpc.getAllUsers.useQuery()

  const areUsersLoading = usersResponse.isLoading;

  const [users, usersActions] = useArray<User>(usersResponse.data ?? []);

  return {areUsersLoading, users, usersActions};
};
```

### useAsync

#### Description

Custom hook that returns a function that wraps the given one and an [AsyncResult](src/async-result.ts) used to track the async execution.

> Any ongoing execution will be canceled (going back to an IdleAsyncResult) if the given `asyncFunction` changes. If you don't need this behavior, you can use [useCallback](https://reactjs.org/docs/hooks-reference.html#usecallback) react hook to wrap the `asyncFunction` function so it doesn't get canceled.

#### API

```ts
type AsyncFunction<TArgs extends unknown[], TData> = (
  ...args: TArgs
) => Promise<TData>;

type ExecuteAsyncFunction<TArgs extends unknown[]> = (...args: TArgs) => void;

type ClearExecution = () => void;

type Result<TArgs extends unknown[], TData> = [
  AsyncResult<TData>,
  ExecuteAsyncFunction<TArgs>,
  ClearExecution
];

const useAsync = <TData>(
  asyncFunction: AsyncFunction<TData>
): Result<TData> => { /* ... */ }
```

#### Example

```tsx
import {asyncResultStatuses} from "@unlockre/utils-react/dist/async-result-status";
import {useAsync} from "@unlockre/utils-react/dist";
import {useCallback} from "react";

type Props = {
  starWarsCharacterId: number;
}

const fetchStarWarsCharacter = (characterId: number) =>
  fetch("https://www.swapi.tech/api/people/" + characterId)
    .then(response => response.json());

const MyComponent = ({starWarsCharacterId}: Props) => {
  const [
    starWarsCharacterResult,
    executeFetchStarWarsCharacter,
    clearStarWarsCharacter
  ] = useAsync(fetchStarWarsCharacter);

  const data = result.status === asyncResultStatuses.succeeded
    ? JSON.stringify(result.data)
    : "-";

  const errorMessage = result.status === asyncResultStatuses.failed
    ? result.error.message
    : "-";

  return (
    <div>
      <button
        onClick={() => executeFetchStarWarsCharacter(startWarsCharacterId)}
      >
        Fetch Star Wars Character
      </button>
      <button onClick={() => clearStarWarsCharacter()}>Reset</button>
      <div>Status: {result.status}</div>
      <div>Error: {errorMessage}</div>
      <div>Data: {data}</div>
    </div>
  );
};
```

### useClickOutside

#### Description

Custom hook to handle the click outside of a given html element.

#### API

```ts
type OnClickOutside = (mouseEvent: MouseEvent) => unknown;

const useClickOutside = <THTMLElement extends HTMLElement>(
  htmlElement: THTMLElement,
  onClickOutside: () => unknown
) => { /* ... */ }
```

#### Example

```tsx
import {useClickOutside} from "@unlockre/utils-react/dist";
import {useRef, useState} from "react";

const MyComponent = () => {
  const [clicksOutsides, setClickOutsides] = useState(0);

  const containerRef = useRef<HTMLDivElement>(null);

  const onClickOutside = useCallback(
    () => setClickOutsides(clickOutsides => clickOutsides + 1),
    [setClickOutsides]
  );

  useClickOutside(containerRef.current, onClickOutside);

  return (
    <div ref={containerRef}># clicks outside: {clicksOutsides}</div>
  );
};
```

### useCompareWithPrev

#### Description

Custom hook to deep compare a value to the previous one passed.

#### API

```ts
import deepCompare from "fast-deep-equal";

type Compare<TValue> = (value1: TValue, value2: TValue) => boolean;

const useCompareWithPrev = <TValue>(
  value: TValue,
  compare: Compare<TValue> = deepCompare
): boolean => {
  /* ... */
};
```

#### Example

```tsx
import {useCompareWithPrev} from "@unlockre/utils-react/dist";

const MyComponent = () => {
  const areObjectsEqual = useCompareWithPrev({name: "Luke"});

  console.log(areObjectsEqual); // false

  return (
    <div>
      In JavaScript object are compared by reference this is why areObjectsEqual is false
    </div>
  );
};
```

### useElementEvent

#### Description

Custom hook to attach a listener to a given event on a provided html element.

#### API

```ts
const useElementEvent = <
  TElement extends HTMLElement,
  TEventName extends keyof HTMLElementEventMap
>(
  element: TElement | null,
  eventName: TEventName,
  onEvent: EventHandler<TElement>
) => { /* ... */ }
```

#### Example

```tsx
import {useElementEvent} from "@unlockre/utils-react/dist";
import {forwardRef, useRef, useState} from "react";

import type {ReactNode} from "react";

type ContainerElement = HTMLDivElement;

type ContainerProps = {
  children: ReactNode;
  height: number;
};

const Container = forwardRef<ContainerElement, ContainerProps>((
  {children, height},
  ref
) => (
  <div {...{ref}} style={{height, overflow: "scroll"}}>
    {children}
  </div>
));
const MyComponent = () => {
  const [containerElement, setContainerElement] = useState<ContainerElement>(null);
  const [containerElementScrollLeft, setContainerElementScrollLeft] = useState(0);
  const handleContainerElementScroll: EventHandler<ContainerElement> = useCallback(
    (event, target) => {
      setContainerElementScrollLeft(target.scrollLeft);
    },
    [setContainerElementScrollLeft]
  );
  useElementEvent(containerElement, "scroll", handleContainerElementScroll);
  return (
    <Container height={300} ref={setContainerElement}>
      <div style={{height: 500}}>Content!</div>
    </Container>
  );
};
```

### useElementStyle

#### Description

Custom hook to add custom styles to an HTMLElement.

#### API

```ts
type Props = {
  element: HTMLElement | null;
  style?: Partial<CSSStyleDeclaration>;
};

const useElementStyle = ({element, style}: Props) => { /* ... */ }
```

#### Example

```tsx
import {forwardRef} from "react";

import {useElementStyle} from "@unlockre/utils-react/dist";

import type {ReactNode} from "react";

type ContainerProps = {
  children: ReactNode;
  height: number;
};

const Container = forwardRef<ContainerElement, ContainerProps>((
  {children, height},
  ref
) => (
  <div {...{ref}} style={{height, overflow: "scroll"}}>
    {children}
  </div>
));

// This has to be stable, otherwise it will be set each time it changes
// By placing it outside the component we do this,
// otherwise we would need to use useMemo
const containerStyle = {
  zIndex: 2;
};

const MyComponent = () => {
  const [containerElement, setContainerElement] = useState<ContainerElement>(null);
  useElementStyle(containerElement, containerStyle);
  return (
    <Container height={300} ref={setContainerElement}>
      <div style={{height: 500}}>Content!</div>
    </Container>
  );
};
```

### useFuzzySearch

#### Description

Custom hook that provides a way to perform a [fuzzy search](https://en.wikipedia.org/wiki/Approximate_string_matching) on a given array.

#### Example

```tsx
import {useFuzzySearch} from "@unlockre/utils-react/dist";
import {useState} from "react";

type UserAddress = {
  city: string;
  state: string;
  street: string;
  zipCode: string;
}

type User = {
  address: UserAddress;
  id: string;
  name: string;
};

const fuzzySearchOptions = {
  debounceMs: 500,
  keys: [
    "name",
    "address.city",
    "address.state",
    "address.street",
  ],
  minMatchCharLength: 3,
  threshold: 0.2
};

const useFilteredUsers = (users?: User[]) => {
  const [userSearchTerm, setUserSearchTerm] = useState("");

  const filteredUsers = useFuzzySearch({
    items: users,
    options: fuzzySearchOptions,
    searchTerm: userSearchTerm
  });

  return {userSearchTerm, filteredUsers, setUserSearchTerm};
};

export {useFilteredUsers};
```

### useId

#### Description

Custom hook that generates a stable unique id.

> This hook mimics the one provided by React v18 with the same name so we can use it until we upgrade all the apps to this version.

### API

```ts
const useId = () => { /* ... */ }
```

#### Example

```tsx
import {useId} from "@unlockre/utils-react/dist";


const SendIcon = () => {
  const gradientId = useId();

  return (
    <svg
      fill="none"
      height="18"
      viewBox="0 0 17 18"
    >
      <path
        clipRule="evenodd"
        d="M2.78321 7.5356C2.32737 7.44172 2 7.0375 2 6.56855V1.4885C2 0.730626 2.81228 0.255499 3.4651 0.631508L16.5066 8.14301C17.1645 8.52193 17.1645 9.47807 16.5066 9.85699L3.4651 17.3685C2.81228 17.7445 2 17.2694 2 16.5115V11.4315C2 10.9625 2.32737 10.5583 2.78321 10.4644L8.32825 9.32235C8.67635 9.25066 8.67635 8.74934 8.32825 8.67765L2.78321 7.5356Z"
        fill={`url(#${gradientId})`)
        fillRule="evenodd"
      />
      <defs>
        <linearGradient
          gradientUnits="userSpaceOnUse"
          id={gradientId}
          x1="2"
          x2="16.1279"
          y1="0.5"
          y2="0.5"
        >
          <stop stopColor="rgb(18, 113, 255)" />
          <stop stopColor="rgb(108, 167, 255)" offset="1" />
        </linearGradient>
      </defs>
    </svg>
  );
};
```

### useLockBodyScroll

#### Description

Custom hook to prevent the page body to scroll (useful for modals).

### API

```ts
const useLockBodyScroll = (lockBodyScroll = true) => { /* ... */ }
```

#### Examples

#### With body scroll (hook disabled)

```ts
import {useLockBodyScroll} from "@unlockre/utils-react/dist";


const WithoutBodyScroll = () => {
  // passing false disables the hook behavior,
  // lets the body to scroll freely
  useLockBodyScroll(false);

  return null;
};
```

#### Without body scroll (hook enabled)

```ts
import {useLockBodyScroll} from "@unlockre/utils-react/dist";


const WithBodyScroll = () => {
  // passing true or not passing anything (defaults to true),
  // prevents the body to scroll
  useLockBodyScroll();

  return null;
};
```

### useMutationObserver

#### Description

Custom hook to observe mutations under the given HTML element.

#### API

```ts
const defaultOptions: MutationObserverInit = {
  attributes: true,
  characterData: true,
  childList: true,
  subtree: true
};

const useMutationObserver = (
  element: HTMLElement,
  onMutation: MutationCallback,
  options: MutationObserverInit = defaultOptions
) => { /* ... */ }
```

#### Example

```ts
import {useMutationObserver} from "@unlockre/utils-react/dist";

const useObserveBody = () => {
  const onMutation: MutationCallback = (mutations, observer) => {
    console.log(mutations, observer)
  };

  useMutationObserver(document.body, onMutation);
};
```

### useResizeObserver

#### Description

Custom hook to observe resizes of the given elements.

#### API

```ts
const useResizeObserver = (
  elements: Element[],
  onResize: ResizeObserverCallback,
  options?: ResizeObserverOptions
) => { /* ... */ }
```

#### Example

```ts
import {useResizeObserver} from "@unlockre/utils-react/dist";

const useObserveBodyResizing = () => {
  const handleResize: ResizeObserverCallback = useCallback(
    (entries, observer) => {
      console.log(entries, observer)
    },
    []
  );

  useResizeObserver([document.body], handleResize);
};
```

### useScrollIntoView

#### Description

Custom hook to scroll into view the element stored using the returned ref callback.

#### API

```ts
import type {Dispatch, SetStateAction} from "react";

type Params = {
  alignToTop?: boolean;
  skip?: boolean;
};

type SetState<TState> = Dispatch<SetStateAction<TState>>;

const defaultParams: Params = {
  alignToTop: true,
  skip: false
};

const useScrollIntoView = <THTMLElement extends HTMLElement>({
  alignToTop,
  skip
} = defaultParams): SetState<THTMLElement | null> => { /* ... */ }
```

#### Example

```tsx
import {useScrollIntoView} from "@unlockre/utils-react/dist";

const MyComponent = () => {
  const setElement = useScrollIntoView();

  return (
    <div ref={setElement}>Something</div>
  );
};
```

### useSetTimeout

#### Description

Custom hook to start a timeout with a given delay and `onTimeout` handler function to execute when the timeout is reached.

> Timeout will be restarted whenever any of the parameters changes, this also applies to the `onTimeout` function. If you don't need this behavior, you can use [useCallback](https://reactjs.org/docs/hooks-reference.html#usecallback) react hook to wrap the `onTimeout` function so it doesn't change and doesn't reset the timeout either.

#### API

```ts
const useSetTimeout = (
  isActive: boolean,
  delay: number,
  onTimeout: () => unknown
) => { /* ... */ }
```

#### Example

```tsx
import {useCallback, useState} from "react";
import {useSetTimeout} from "@unlockre/utils-react/dist";

const MyComponent = () => {
  const [message, timeout] = useState<string>("Waiting...");

  const onTimeout = useCallback(() => setMessage("Timeout!"), []);

  useSetTimeout(true, 3000, onTimeout);

  return (
    <div>{message}</div>
  );
};
```

### useTypingAnimation

#### Description

Custom hook receives a text and returns an animated text (animated substring of the received text) and an isReady boolean that let us know when it has finished.

#### API

```ts
type Result = {
  isReady: boolean;
  text?: string;
}

type Params = {
  intervalMs: number;
  text?: string;
};

const useTypingAnimation = (params: Params): Result => { /* ... */ }
```

#### Example

```tsx
import {useTypingAnimation} from "@unlockre/utils-react/dist";

const MyComponent = () => {
  const {text, isReady} = useTypingAnimation("Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam ultrices, neque sed tristique vestibulum, mauris leo tincidunt tortor, vitae posuere arcu est at sapien.");

  return (
    <div>
       <div>Text: {text}</div>
       <div>Is Ready?: {isReady}</div>
    <div>
  );
};
```
