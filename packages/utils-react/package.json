{"name": "@unlockre/utils-react", "version": "3.1.1", "repository": "https://github.com/unlockre/utils-packages", "scripts": {"build": "yarn build:clean && yarn build:code", "build:clean": "rm -rf dist", "build:code": "tsc && tsconfig-replace-paths", "lint:js": "eslint", "lint:ts": "eslint --config ./.eslintrc.tsc.js", "semantic-release": "semantic-release"}, "publishConfig": {"registry": "https://npm.pkg.github.com/", "access": "restricted"}, "files": ["dist", "CHANGELOG.md"], "peerDependencies": {"react": "^18.0.0"}, "devDependencies": {"@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@types/react": "^18.3.20", "@types/uuid": "^9.0.8", "@typescript-eslint/parser": "^6.2.1", "@unlockre/eslint-config": "^2.1.2", "eslint": "^8.46.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-tsc": "^2.0.0", "prettier": "^3.0.0", "react": "^18.3.1", "semantic-release": "^24.2.1", "semantic-release-monorepo": "^8.0.2", "semantic-release-slack-bot": "^4.0.2", "semantic-release-yarn": "^3.0.2", "tsconfig-replace-paths": "^0.0.14", "typescript": "^5.1.6"}, "dependencies": {"@unlockre/utils-object": "workspace:^", "fast-deep-equal": "^3.1.3", "fuse.js": "^7.0.0", "tslib": "^2.6.1", "use-debounce": "^10.0.1", "uuid": "^9.0.1"}}