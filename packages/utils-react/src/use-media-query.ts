import {useEffect, useState} from "react";

const getMatches = (query: string) => window.matchMedia(query).matches;

const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(
    typeof window !== "undefined" && getMatches(query)
  );

  useEffect(() => {
    const matchMedia = window.matchMedia(query);

    const handleMatchesChange = () => {
      setMatches(getMatches(query));
    };

    handleMatchesChange();

    matchMedia.addEventListener("change", handleMatchesChange);

    return () => {
      matchMedia.removeEventListener("change", handleMatchesChange);
    };
  }, [query]);

  return matches;
};

export {useMediaQuery};
