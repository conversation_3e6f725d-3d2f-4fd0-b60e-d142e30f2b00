import {asyncResultStatuses} from "./async-result-status";
import type {AsyncResultStatus} from "./async-result-status";

type SucceededAsyncResult<TData> = {
  data: TData;
  status: typeof asyncResultStatuses.succeeded;
};

type PendingAsyncResult = {
  status: typeof asyncResultStatuses.pending;
};

type FailedAsyncResult = {
  error: Error;
  status: typeof asyncResultStatuses.failed;
};

type IdleAsyncResult = {
  status: typeof asyncResultStatuses.idle;
};

type AsyncResult<TData> =
  | FailedAsyncResult
  | IdleAsyncResult
  | PendingAsyncResult
  | SucceededAsyncResult<TData>;

const createSucceeded = <TData>(data: TData): SucceededAsyncResult<TData> => ({
  status: asyncResultStatuses.succeeded,
  data
});

const createPending = (): PendingAsyncResult => ({
  status: asyncResultStatuses.pending
});

const createFailed = (error: Error): FailedAsyncResult => ({
  status: asyncResultStatuses.failed,
  error
});

const createIdle = (): IdleAsyncResult => ({status: asyncResultStatuses.idle});

export {
  asyncResultStatuses,
  createFailed,
  createIdle,
  createPending,
  createSucceeded
};
export type {
  AsyncResult,
  AsyncResultStatus,
  FailedAsyncResult,
  IdleAsyncResult,
  PendingAsyncResult,
  SucceededAsyncResult
};
