import {useLayoutEffect} from "react";

const getScrollbarWidth = () => window.innerWidth - document.body.offsetWidth;

const getLockBodyScrollStyle = (bodyPaddingRightPx: string) => ({
  overflow: "hidden",
  paddingRight: parseFloat(bodyPaddingRightPx) + getScrollbarWidth() + "px"
});

const getBodyScrollStyle = () => {
  const originalBodyStyle = window.getComputedStyle(document.body);

  return {
    overflow: originalBodyStyle.overflow,
    paddingRight: originalBodyStyle.paddingRight
  };
};

const useClientLockBodyScroll = (lockBodyScroll = true) => {
  useLayoutEffect(() => {
    if (!lockBodyScroll) {
      return;
    }

    const originalBodyScrollingStyle = getBodyScrollStyle();

    Object.assign(
      document.body.style,
      getLockBodyScrollStyle(originalBodyScrollingStyle.paddingRight)
    );

    return () => {
      Object.assign(document.body.style, originalBodyScrollingStyle);
    };
  }, [lockBodyScroll]);
};

// eslint-disable-next-line no-unused-vars
const useServerLockBodyScroll = (lockBodyScroll = true) => {};

const useLockBodyScroll =
  typeof window === "undefined"
    ? useServerLockBodyScroll
    : useClientLockBodyScroll;

export {useLockBodyScroll};
