import {useCallback, useEffect, useRef, useState} from "react";

import * as withAsyncResult from "./async-result";
import type {AsyncResult} from "./async-result";

type AsyncFunction<TArgs extends unknown[], TData> = (
  ...args: TArgs
) => Promise<TData>;

type ExecuteAsyncFunction<TArgs extends unknown[]> = (...args: TArgs) => void;

type ClearExecution = () => void;

type Result<TArgs extends unknown[], TData> = [
  AsyncResult<TData>,
  ExecuteAsyncFunction<TArgs>,
  ClearExecution
];

const useAsync = <TArgs extends unknown[], TData>(
  asyncFunction: AsyncFunction<TArgs, TData>
): Result<TArgs, TData> => {
  const executionIdRef = useRef<number>(0);

  const [result, setResult] = useState<AsyncResult<TData>>(
    withAsyncResult.createIdle()
  );

  const incrementExecutionId = () => {
    executionIdRef.current = executionIdRef.current + 1;

    return executionIdRef.current;
  };

  const clearExecution = useCallback(() => {
    incrementExecutionId();

    setResult(withAsyncResult.createIdle());
  }, []);

  const succeedExecution = (executionId: number) => (data: TData) => {
    if (executionId !== executionIdRef.current) {
      return;
    }

    setResult(withAsyncResult.createSucceeded(data));
  };

  const failExecution = (executionId: number) => (error: Error) => {
    if (executionId !== executionIdRef.current) {
      return;
    }

    setResult(withAsyncResult.createFailed(error));
  };

  const prepareExecution = () => {
    setResult(withAsyncResult.createPending());

    return incrementExecutionId();
  };

  const executeAsyncFunction = useCallback(
    (...args: TArgs) => {
      const executionId = prepareExecution();

      asyncFunction(...args)
        .then(succeedExecution(executionId))
        .catch(failExecution(executionId));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [asyncFunction]
  );

  useEffect(() => {
    clearExecution();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [asyncFunction]);

  return [result, executeAsyncFunction, clearExecution];
};

export {useAsync};
