import * as withArray from "@unlockre/utils-array/dist";
import {useCallback, useEffect, useMemo, useState} from "react";

const useArray = <TValue>(initialValues?: TValue[]) => {
  const [values, setValues] = useState(initialValues ?? []);

  useEffect(() => {
    setValues(initialValues ?? []);
  }, [initialValues]);

  const update = useCallback(
    (index: number, value: TValue) =>
      setValues(values => withArray.update(values, index, value)),
    [setValues]
  );

  const reset = useCallback(
    (newValues: TValue[] = initialValues ?? []) => setValues(newValues),
    [initialValues, setValues]
  );

  const remove = useCallback(
    (value: TValue) =>
      setValues(values => withArray.remove(values, values.indexOf(value), 1)),
    [setValues]
  );

  const prepend = useCallback(
    (value: TValue) => setValues(values => [value, ...values]),
    [setValues]
  );

  const insert = useCallback(
    (index: number, value: TValue) =>
      setValues(values => withArray.insert(values, index, value)),
    [setValues]
  );

  const append = useCallback(
    (value: TValue) => setValues(values => [...values, value]),
    [setValues]
  );

  const actions = useMemo(
    () => ({append, insert, prepend, remove, reset, update}),
    [append, insert, prepend, remove, reset, update]
  );

  return [values, actions] as const;
};

export {useArray};
