import Fuse from "fuse.js";
import type {IFuseOptions} from "fuse.js";
import {useMemo} from "react";
import {useDebounce} from "use-debounce";

type Options<TItem> = IFuseOptions<TItem> & {
  debounceMs?: number;
};

type Params<TItem> = {
  items?: TItem[];
  options?: Options<TItem>;
  searchTerm?: string;
};

const useFuzzySearch = <TItem>({items, options, searchTerm}: Params<TItem>) => {
  const fuse = useMemo(
    () => items && new Fuse(items, options),
    [items, options]
  );

  const minMatchCharLength = options?.minMatchCharLength ?? 1;

  const [debouncedSearchTerm] = useDebounce(
    searchTerm,
    options?.debounceMs ?? 0
  );

  const resultItems = useMemo(
    () =>
      !debouncedSearchTerm || debouncedSearchTerm.length < minMatchCharLength
        ? items
        : fuse?.search(debouncedSearchTerm).map(result => result.item),
    [fuse, items, minMatchCharLength, debouncedSearchTerm]
  );

  return resultItems;
};

export {useFuzzySearch};
export type {Options as UseFuzzySearchOptions};
