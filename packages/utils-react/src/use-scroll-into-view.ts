import {useEffect, useState} from "react";

type Params = {
  alignToTop?: boolean;
  skip?: boolean;
};

const defaultParams: Params = {
  alignToTop: true,
  skip: false
};

const useScrollIntoView = <THTMLElement extends HTMLElement>({
  alignToTop,
  skip
} = defaultParams) => {
  const [element, setElement] = useState<THTMLElement | null>(null);

  useEffect(() => {
    if (!skip && element) {
      element.scrollIntoView(alignToTop);
    }
  }, [skip, element, element?.innerHTML]);

  return setElement;
};

export {useScrollIntoView};
