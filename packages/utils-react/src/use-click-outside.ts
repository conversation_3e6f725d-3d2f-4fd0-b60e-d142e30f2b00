import {useCallback, useEffect} from "react";

type OnClickOutside = (mouseEvent: MouseEvent) => unknown;

const useClickOutside = <THTMLElement extends HTMLElement>(
  htmlElement: THTMLElement | null,
  onClickOutside: OnClickOutside
) => {
  const handleMouseDown = useCallback(
    (mouseEvent: MouseEvent) => {
      if (
        htmlElement &&
        !htmlElement.contains(mouseEvent.target as Node | null)
      ) {
        onClickOutside(mouseEvent);
      }
    },
    [htmlElement, onClickOutside]
  );

  useEffect(() => {
    document.addEventListener("mousedown", handleMouseDown);

    return () => {
      document.removeEventListener("mousedown", handleMouseDown);
    };
  }, [handleMouseDown]);
};

export {useClickOutside};
