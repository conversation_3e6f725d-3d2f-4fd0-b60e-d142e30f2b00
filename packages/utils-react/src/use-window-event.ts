import {useEffect} from "react";

type WindowEventHandler<TEventName extends keyof WindowEventMap> = (
  event: WindowEventMap[TEventName]
) => unknown;

const useWindowEvent = <TEventName extends keyof WindowEventMap>(
  eventName: TEventName,
  onEvent: WindowEventHandler<TEventName>
) => {
  useEffect(() => {
    window.addEventListener(eventName, onEvent);

    return () => window.removeEventListener(eventName, onEvent);
  }, [eventName, onEvent]);
};

export {useWindowEvent};
export type {WindowEventHandler};
