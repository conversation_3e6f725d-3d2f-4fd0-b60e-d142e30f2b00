import {useEffect} from "react";

type EventHandler<TElement extends HTMLElement> = (
  event: Event,
  target: TElement
) => unknown;

const useElementEvent = <
  TElement extends HTMLElement,
  TEventName extends keyof HTMLElementEventMap
>(
  element: TElement | null,
  eventName: TEventName,
  onEvent: EventHandler<TElement>
) => {
  useEffect(() => {
    if (!element) {
      return;
    }

    const handleEvent = (event: Event) => {
      onEvent(event, event.currentTarget as TElement);
    };

    element.addEventListener(eventName, handleEvent);

    return () => element.removeEventListener(eventName, handleEvent);
  }, [element, eventName, onEvent]);
};

export {useElementEvent};
export type {EventHandler};
