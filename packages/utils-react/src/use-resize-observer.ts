import {useEffect} from "react";

const useResizeObserver = (
  elements: Element[],
  onResize: ResizeObserverCallback,
  options?: ResizeObserverOptions
) =>
  useEffect(() => {
    if (elements.length === 0) {
      return;
    }

    const resizeObserver = new ResizeObserver(onResize);

    elements.forEach(element => resizeObserver.observe(element, options));

    return () => {
      resizeObserver.disconnect();
    };
  }, [elements, options, onResize]);

export {useResizeObserver};
