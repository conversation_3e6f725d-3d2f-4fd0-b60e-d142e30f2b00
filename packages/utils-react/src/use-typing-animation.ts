import {useEffect, useState} from "react";

type Params = {
  intervalMs: number;
  text?: string;
};

const useTypingAnimation = ({intervalMs, text}: Params) => {
  const [fullText, setFullText] = useState<string | undefined>(undefined);
  const [index, setIndex] = useState(0);

  useEffect(() => {
    setFullText(text);

    if (text === undefined) {
      return;
    }

    if (fullText !== undefined && text.indexOf(fullText) !== 0) {
      setIndex(0);
    }

    const timeoutId = setTimeout(() => {
      setIndex(index + 1);
    }, intervalMs);

    return () => clearTimeout(timeoutId);
  }, [index, text]);

  return {
    text: fullText?.slice(0, index),
    isReady: index + 1 === fullText?.length
  };
};

export {useTypingAnimation};
