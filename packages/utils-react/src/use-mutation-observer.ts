import {useEffect} from "react";

const defaultOptions: MutationObserverInit = {
  attributes: true,
  characterData: true,
  childList: true,
  subtree: true
};

const useMutationObserver = (
  element: HTMLElement | null,
  onMutation: MutationCallback,
  options: MutationObserverInit = defaultOptions
) =>
  useEffect(() => {
    if (!element) {
      return;
    }

    const observer = new MutationObserver(onMutation);

    observer.observe(element, options);

    return () => observer.disconnect();
  }, [element, onMutation, options]);

export {useMutationObserver};
