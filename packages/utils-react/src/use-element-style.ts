import * as withObject from "@unlockre/utils-object/dist";
import type {RecordFrom} from "@unlockre/utils-object/dist";
import {useEffect} from "react";

type Props = {
  element: HTMLElement | null;
  style?: Partial<CSSStyleDeclaration>;
};

const useElementStyle = ({element, style}: Props) =>
  useEffect(() => {
    if (!element || !style) {
      return;
    }

    const originalStyle = withObject.pick(
      element.style as RecordFrom<CSSStyleDeclaration>,
      Object.keys(style) as (keyof Partial<CSSStyleDeclaration>)[]
    );

    Object.assign(element.style, style);

    // we are doing this because sometimes the styles are changed when they are applied
    const newStyle = withObject.pick(
      element.style as RecordFrom<CSSStyleDeclaration>,
      Object.keys(style) as (keyof Partial<CSSStyleDeclaration>)[]
    );

    return () => {
      // TODO: Check why sometimes when the element is no longer inside the document the styles are cleaned
      if (!document.contains(element)) {
        return;
      }

      const currentStyle = withObject.pick(
        element.style as RecordFrom<CSSStyleDeclaration>,
        Object.keys(style) as (keyof Partial<CSSStyleDeclaration>)[]
      );

      if (!withObject.areEqual(currentStyle, newStyle)) {
        throw new Error("Element style has been changed externally");
      }

      Object.assign(element.style, originalStyle);
    };
  }, [element, style]);

export {useElementStyle};
