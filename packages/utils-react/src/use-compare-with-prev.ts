import deepCompare from "fast-deep-equal";
import {useRef} from "react";

type Compare<TValue> = (value1: TValue, value2: TValue) => boolean;

const noValue = Symbol();

const useCompareWithPrev = <TValue>(
  value: TValue,
  compare: Compare<TValue> = deepCompare
) => {
  const ref = useRef<TValue | symbol>(noValue);

  const areDeepEqual =
    ref.current !== noValue && compare(value, ref.current as TValue);

  if (!areDeepEqual) {
    ref.current = value;
  }

  return areDeepEqual;
};

export {useCompareWithPrev};
