import type {Types} from "@amplitude/analytics-browser";
import {createContext, useContext} from "react";
import type {ReactNode} from "react";

import isBrowser from "./is-browser";

type AmplitudeClientProviderProps = {
  amplitudeClient?: Types.BrowserClient;
  children: ReactNode;
};

const amplitudeClientContext = createContext<Types.BrowserClient | undefined>(
  undefined
);

const ensureAmplitudeClientIfBrowser = (
  amplitudeClient?: Types.BrowserClient
) => {
  if (!amplitudeClient && isBrowser()) {
    throw new Error("No AmplitudeClient");
  }

  return amplitudeClient;
};

const useAmplitudeClient = () => useContext(amplitudeClientContext);

const AmplitudeClientProvider = ({
  amplitudeClient,
  children
}: AmplitudeClientProviderProps) => (
  <amplitudeClientContext.Provider
    value={ensureAmplitudeClientIfBrowser(amplitudeClient)}
  >
    {children}
  </amplitudeClientContext.Provider>
);

export {AmplitudeClientProvider, useAmplitudeClient};
