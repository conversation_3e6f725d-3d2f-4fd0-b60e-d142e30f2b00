import {useAuth0} from "@auth0/auth0-react";
import {useAuth0AccessTokenClaims} from "@unlockre/utils-auth0/dist";
import {useEffect} from "react";

import * as withAmplitudeUser from "./amplitude-user";
import {useTrackUser} from "./use-track-user";

const useTrackAuth0User = () => {
  const {user: auth0User} = useAuth0();

  const accessTokenClaims = useAuth0AccessTokenClaims();

  const trackUser = useTrackUser();

  useEffect(() => {
    if (auth0User && accessTokenClaims.status === "succeeded") {
      trackUser(
        withAmplitudeUser.createFrom(auth0User, accessTokenClaims.data)
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [auth0User, accessTokenClaims]);
};

export {useTrackAuth0User};
