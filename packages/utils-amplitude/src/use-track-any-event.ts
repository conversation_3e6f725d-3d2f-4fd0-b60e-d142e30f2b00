import {useCallback} from "react";

import * as withBrowserClient from "./amplitude-client";
import {useAmplitudeClient} from "./amplitude-client-context";
import type {AnyEvent} from "./event";

const useTrackAnyEvent = <TEvent extends AnyEvent>() => {
  const amplitudeClient = useAmplitudeClient();

  const trackEvent = useCallback(
    (event: TEvent) => withBrowserClient.trackAnyEvent(amplitudeClient, event),
    [amplitudeClient]
  );

  return trackEvent;
};

export {useTrackAnyEvent};
