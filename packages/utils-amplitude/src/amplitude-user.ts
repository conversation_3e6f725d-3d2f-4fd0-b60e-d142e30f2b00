import type {User as Auth0User} from "@auth0/auth0-react";
import type {Auth0AccessTokenClaims} from "@unlockre/utils-auth0/dist/auth0-access-token-claims";
import {ensureIsDefined} from "@unlockre/utils-validation/dist";

type AmplitudeUser = {
  email: string;
  id: string;
  organizationId: string;
};

const createFrom = (
  auth0User: Auth0User,
  {organizationId}: Auth0AccessTokenClaims
) => ({
  email: ensureIsDefined(
    auth0User.email,
    "Expected auth0 user email to be defined"
  ),
  organizationId,
  id: ensureIsDefined(auth0User.sub, "Expected auth0 user sub to be defined")
});

export {createFrom};
export type {AmplitudeUser};
