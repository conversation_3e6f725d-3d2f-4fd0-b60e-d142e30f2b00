// prettier-ignore
type EventFrom<TType extends string, TData extends object = never> =
  [TData] extends [never]
    ? {data?: undefined; type: TType}
    : {data: TData; type: TType};

type AnyEvent = EventFrom<string, object> | EventFrom<string>;

const toAmplitudeEvent = <TEvent extends AnyEvent>(event: TEvent) => ({
  /* eslint-disable @typescript-eslint/naming-convention */
  event_type: event.type,
  event_properties: event.data
  /* eslint-enable */
});

export {toAmplitudeEvent};
export type {AnyEvent, EventFrom};
