import type {Types} from "@amplitude/analytics-browser";

import * as withEvent from "@/event";
import type {AnyEvent} from "@/event";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const trackAnyEvent = <TEvent extends AnyEvent>(
  amplitudeClient: Types.BrowserClient | undefined,
  event: TEvent
) => amplitudeClient?.track(withEvent.toAmplitudeEvent(event));

export {trackAnyEvent};
