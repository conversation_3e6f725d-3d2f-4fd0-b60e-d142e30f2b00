import type {Types} from "@amplitude/analytics-browser";

const sanitizeAmplitudeEventType = (eventType: string) => {
  if (eventType.endsWith("$identify")) {
    return "identify";
  }

  if (eventType.endsWith("Page Viewed")) {
    return "pageView";
  }

  if (eventType.endsWith("session_start")) {
    return "sessionStart";
  }

  if (eventType.endsWith("session_end")) {
    return "sessionEnd";
  }

  return eventType;
};

const getEventType = (event: Types.Event, sourceName?: string) => {
  const sanitizedEventType = sanitizeAmplitudeEventType(event.event_type);

  return sourceName
    ? sourceName + "/" + sanitizedEventType
    : sanitizedEventType;
};

const createEnrichmentPlugin = (
  sourceName?: string
): Types.EnrichmentPlugin => ({
  execute: async event => ({
    ...event,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    event_type: getEventType(event, sourceName)
  })
});

export {createEnrichmentPlugin};
