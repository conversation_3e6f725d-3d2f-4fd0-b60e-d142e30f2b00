import {createInstance} from "@amplitude/analytics-browser";

import isBrowser from "@/is-browser";

import {createEnrichmentPlugin} from "./create-enrichment-plugin";

type Params = {
  apiKey: string;
  apiUrl?: string;
  sourceName?: string;
};

const create = ({apiKey, apiUrl, sourceName}: Params) => {
  const amplitudeClient = createInstance();

  amplitudeClient.init(apiKey, {
    defaultTracking: true,
    serverUrl: apiUrl
  });

  amplitudeClient.add(createEnrichmentPlugin(sourceName));

  return amplitudeClient;
};

const createIfBrowser = (params: Params) =>
  isBrowser() ? create(params) : undefined;

export {createIfBrowser};
