import type {Types} from "@amplitude/analytics-browser";

import type {AmplitudeUser} from "@/amplitude-user";

import * as withIdentifyEvent from "../identify-event";

const trackUser = (
  amplitudeClient: Types.BrowserClient | undefined,
  amplitudeUser: AmplitudeUser
) => {
  if (!amplitudeClient) {
    return;
  }

  amplitudeClient.setUserId(amplitudeUser.id);

  const identifyEvent = withIdentifyEvent.createFrom(amplitudeUser);

  amplitudeClient.identify(identifyEvent);
};

export {trackUser};
