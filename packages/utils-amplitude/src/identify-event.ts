import {Identify} from "@amplitude/analytics-browser";
import * as withObject from "@unlockre/utils-object/dist";

import type {AmplitudeUser} from "@/amplitude-user";

const untrackedUserFieldKeys: (keyof AmplitudeUser)[] = ["id"];

const createFrom = (amplitudeUser: AmplitudeUser) => {
  const identifyEvent = new Identify();

  withObject
    .getEntries(amplitudeUser)
    .forEach(([userFieldKey, userFieldValue]) => {
      if (untrackedUserFieldKeys.includes(userFieldKey)) {
        return;
      }

      identifyEvent.set(userFieldKey, userFieldValue);
    });

  return identifyEvent;
};

export {createFrom};
