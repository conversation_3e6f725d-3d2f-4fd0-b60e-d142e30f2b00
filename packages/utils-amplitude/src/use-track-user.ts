import {useCallback} from "react";

import * as withBrowserClient from "./amplitude-client";
import {useAmplitudeClient} from "./amplitude-client-context";
import type {AmplitudeUser} from "./amplitude-user";

const useTrackUser = () => {
  const amplitudeClient = useAmplitudeClient();

  const trackUser = useCallback(
    (amplitudeUser: AmplitudeUser) =>
      withBrowserClient.trackUser(amplitudeClient, amplitudeUser),
    [amplitudeClient]
  );

  return trackUser;
};

export {useTrackUser};
