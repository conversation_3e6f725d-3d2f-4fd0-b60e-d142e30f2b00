# [@unlockre/utils-amplitude-v5.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v5.0.0...@unlockre/utils-amplitude-v5.0.1) (2025-06-30)


### Bug Fixes

* Stabilize trackEvent function ([#247](https://github.com/unlockre/utils-packages/issues/247)) ([a2cf08b](https://github.com/unlockre/utils-packages/commit/a2cf08bdc0ae00784f85c585152c322b802e2a99))

# [@unlockre/utils-amplitude-v5.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v4.0.0...@unlockre/utils-amplitude-v5.0.0) (2025-05-08)


### Features

* Add useLogoutIfNeeded hook ([#235](https://github.com/unlockre/utils-packages/issues/235)) ([671a70e](https://github.com/unlockre/utils-packages/commit/671a70e37d6a52b9b96e3dd835875238b7bfc988))


### BREAKING CHANGES

* Move Auth0AccessTokenClaims type to a dedicated module

# [@unlockre/utils-amplitude-v4.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v3.0.1...@unlockre/utils-amplitude-v4.0.0) (2025-03-31)


### Bug Fixes

* Update react dependencies ([#232](https://github.com/unlockre/utils-packages/issues/232)) ([f98c37c](https://github.com/unlockre/utils-packages/commit/f98c37c119e6b5aee33e3860a51e30beb2c48460))


### BREAKING CHANGES

* Update react dependencies

# [@unlockre/utils-amplitude-v3.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v3.0.0...@unlockre/utils-amplitude-v3.0.1) (2025-01-30)


### Bug Fixes

* Fix Amplitude IdentifyEvent ([#208](https://github.com/unlockre/utils-packages/issues/208)) ([b413cff](https://github.com/unlockre/utils-packages/commit/b413cffa03afc09f4f490fb54666bf7a212492a0))

# [@unlockre/utils-amplitude-v3.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v2.0.1...@unlockre/utils-amplitude-v3.0.0) (2025-01-30)


### Bug Fixes

* Fix user tracking ([#206](https://github.com/unlockre/utils-packages/issues/206)) ([4d4aeee](https://github.com/unlockre/utils-packages/commit/4d4aeee60d37eeb3d0e456ff55ef0556447b9910))


### BREAKING CHANGES

* Rename AbstractEvent to EventFrom and use named exports

# [@unlockre/utils-amplitude-v2.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v2.0.0...@unlockre/utils-amplitude-v2.0.1) (2024-03-18)


### Bug Fixes

* Make apiUrl optional in createIfBrowser utility ([#121](https://github.com/unlockre/utils-packages/issues/121)) ([0b34d03](https://github.com/unlockre/utils-packages/commit/0b34d0319ddd4e113a8703303bbd6739d2782585))

# [@unlockre/utils-amplitude-v2.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v1.2.1...@unlockre/utils-amplitude-v2.0.0) (2024-02-23)


### Features

* Support passing an amplitude api url ([#107](https://github.com/unlockre/utils-packages/issues/107)) ([a8466fb](https://github.com/unlockre/utils-packages/commit/a8466fb17d473757ff25a950027c13cb91bf86be))


### BREAKING CHANGES

* Change createIfBrowser function signature

# [@unlockre/utils-amplitude-v1.2.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v1.2.0...@unlockre/utils-amplitude-v1.2.1) (2023-11-01)


### Bug Fixes

* Add defaultTracking option to Amplitude init method ([#93](https://github.com/unlockre/utils-packages/issues/93)) ([4a4623b](https://github.com/unlockre/utils-packages/commit/4a4623b6df934f695346419fb4a4e982ed088548))

# [@unlockre/utils-amplitude-v1.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v1.1.1...@unlockre/utils-amplitude-v1.2.0) (2023-10-19)


### Features

* Track organization id on Amplitude ([#88](https://github.com/unlockre/utils-packages/issues/88)) ([c2bfd16](https://github.com/unlockre/utils-packages/commit/c2bfd1633be9c37546985efafc92ae48471967c7))

# [@unlockre/utils-amplitude-v1.1.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v1.1.0...@unlockre/utils-amplitude-v1.1.1) (2023-08-14)


### Bug Fixes

* Do not check for amplitudeClient in useAmplitudeClient hook ([#69](https://github.com/unlockre/utils-packages/issues/69)) ([ed29cae](https://github.com/unlockre/utils-packages/commit/ed29cae72d5e9b5a868b10adacaa2f692c9aecff))

# [@unlockre/utils-amplitude-v1.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v1.0.3...@unlockre/utils-amplitude-v1.1.0) (2023-08-11)


### Features

* Support SSR ([#68](https://github.com/unlockre/utils-packages/issues/68)) ([f43c981](https://github.com/unlockre/utils-packages/commit/f43c981190c3583ded7fa081181617a38164ca5a))

# [@unlockre/utils-amplitude-v1.0.3](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v1.0.2...@unlockre/utils-amplitude-v1.0.3) (2023-08-10)


### Bug Fixes

* Improve amplitude event type sanitization ([#67](https://github.com/unlockre/utils-packages/issues/67)) ([b8fbd80](https://github.com/unlockre/utils-packages/commit/b8fbd8054a51b8e252f9370bdef114360ffeefe1))

# [@unlockre/utils-amplitude-v1.0.2](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v1.0.1...@unlockre/utils-amplitude-v1.0.2) (2023-08-10)


### Bug Fixes

* Export useTrackAuth0User hook ([#66](https://github.com/unlockre/utils-packages/issues/66)) ([d3ae7da](https://github.com/unlockre/utils-packages/commit/d3ae7da35edaf5ef7addc06c71de4dfeb17bbf84))

# [@unlockre/utils-amplitude-v1.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-amplitude-v1.0.0...@unlockre/utils-amplitude-v1.0.1) (2023-08-10)


### Bug Fixes

* Make sourceName optional ([#60](https://github.com/unlockre/utils-packages/issues/60)) ([f636311](https://github.com/unlockre/utils-packages/commit/f636311756c5c01aa07b7f9a73fc9221cfea54a5))

# @unlockre/utils-amplitude-v1.0.0 (2023-08-09)


### Features

* Add utils-amplitude package ([#58](https://github.com/unlockre/utils-packages/issues/58)) ([6153399](https://github.com/unlockre/utils-packages/commit/615339985eae61a17de7bccec4ff98dba73f643c))
