# utils-amplitude

## Installation

```
yarn add @unlockre/utils-amplitude
```

### Peer dependencies

- **@amplitude/analytics-browser**: `^2.0.0`
- **@auth0/auth0-react**: `^2.0.0`
- **react**: `^17.0.0`

## Utilities

### amplitude-client / createIfBrowser

#### Description

Factory function to create and initialize an `AmplitudeClient` only in browser environment (returns `undefined` if not in a browser).

> This function is like this to support the usage of this package in SSR.

#### Example

**utils/amplitude/amplitude-client.ts**

```ts
import * as withAmplitudeClient from "@unlockre/utils-amplitude/dist/amplitude-client";

import environmentVariables from "@/utils/environment/environment-variables";

const amplitudeClient = withAmplitudeClient.createIfBrowser({
  apiKey: environmentVariables.AMPLITUDE_API_KEY,
  // (optional) passing it will prevent the chrome amplitude extension to work
  // correctly, so it is recommended to pass undefined on development
  apiUrl: environmentVariables.AMPLITUDE_API_URL,
  // (optional) could be an application name
  sourceName: "someEventSource"
});

export {amplitudeClient}
```

### amplitude-client / trackAnyEvent

#### Description

Function to track events.

> Given that is uses a generic Event, It can be used to bind a custom Event type to a concrete track function.

> On a React environment it is preferable to use `useTrackAnyEvent` hook (see below).

#### Example

**utils/amplitude/event.ts**

```ts
import type {EventFrom} from "@unlockre/utils-amplitude/dist";

type TabSelectEvent = EventFrom<"tabSelect", {tabName: string}>;

type UserDeleteEvent = EventFrom<"userDelete">;

type Event = TabSelectEvent | UserDeleteEvent;

export type {Event};
```

**utils/amplitude/track-event.ts**

```ts
import * as withAmplitudeClient from "@unlockre/utils-amplitude/dist/amplitude-client";

import type {Event} from "./event";

// bind our Event type to trackAnyEvent so we can have a concrete trackEvent
// function that can only be used passing a specific event
const trackEvent = withAmplitudeClient.trackAnyEvent<Event>;

export {trackEvent};
```

**some-module.ts**

```ts
import {amplitudeClient, trackEvent} from "@/utils/amplitude";

// this is not the recommended way if you are using react
// see useTrackEvent hook below
trackEvent(amplitudeClient, {
  type: "tabSelect",
  data: {tabName: "dealDetails"}
});
```

### amplitude-client / trackUser

#### Description

Function to track (or identify) the logged user (the one who is interacting with the application).

> On a React environment it is preferable to use `useTrackUser` hook (see below).

#### Example

```ts
import * as withAmplitudeClient from "@unlockre/utils-amplitude/dist/amplitude-client";

import {amplitudeClient} from "@/utils/amplitude";

withAmplitudeClient.trackUser(amplitudeClient, {
  id: "some-user-id",
  email: "<EMAIL>"
});
```

### AmplitudeClientProvider

#### Description

React component to provide the given `AmplitudeClient` instance to any of its descendants (using `useAmplitudeClient` hook).

#### Example

**components/application/application.tsx**

```tsx
import {AmplitudeClientProvider} from "@unlockre/utils-amplitude/dist/amplitude-client";

import {amplitudeClient} from "@/utils/amplitude";

type Props = {
  /* ... */
};

const Application = (props: Props) => (
  <AmplitudeClientProvider {...{amplitudeClient}}>
    {/* some other providers, etc */}
  </AmplitudeClientProvider>
);

export {Application};
```

### useAmplitudeClient

#### Description

React hook to obtain the `AmplitudeClient` instance provided by the closest `AmplitudeClientProvider` ancestor.

#### Example

```tsx
import {useAmplitudeClient} from "@unlockre/utils-amplitude/dist";

// It has to have an AmplitudeClientProvider ancestor!
const SomeComponent = () => {
  const amplitudeClient = useAmplitudeClient();

  // ...
};
```

### useTrackAnyEvent

#### Description

React hook that returns a `trackEvent` function that uses the `AmplitudeClient` provided to the closest `AmplitudeClientProvider` component.

> Given that is uses a generic Event, It can be used to bind a custom Event type to a concrete useTrackEvent hook.

#### Example

**utils/amplitude/event.ts**

```ts
import type {EventFrom} from "@unlockre/utils-amplitude/dist";

type TabSelectEvent = EventFrom<"tabSelect", {tabName: string}>;

type UserDeleteEvent = EventFrom<"userDelete">;

type Event = TabSelectEvent | UserDeleteEvent;

export type {Event};
```

**utils/amplitude/use-track-event.ts**

```ts
import {useTrackAnyEvent} from "@unlockre/utils-amplitude/dist";

import type {Event} from "./event";

// bind our Event type to useTrackAnyEvent so we can have a concrete useTrackEvent
// hok that can only be used passing a specific event
const useTrackEvent = useTrackAnyEvent<Event>;

export default useTrackEvent;
```

**tab.tsx**

```tsx
import {useTrackEvent} from "@/utils/amplitude";

type Props = {
  onSelect: () => unknown;
  tabName: string;
};

const Tab = ({onSelect, tabName}: Props) => {
  const trackEvent = useTrackEvent();

  const handleClick = () => {
    trackEvent({type: "tabSelect", data: {tabName}});

    onSelect();
  };

  return (
    <button onClick={handleClick}>{tabName}</button>
  )
};

export {Tab};
```

### useTrackAuth0User

#### Description

React hook that by simply calling it, it tracks the logged auth0 user.

### useTrackUser

#### Description

React hook that can be used to track (identify) an user by calling the function that it returns (`trackUser` binded to the `AmplitudeClient` provided by the closest `AmplitudeClientProvider` ancestor).

#### Example

```ts
import {useEffect} from "react";
import {useTrackUser} from "@/utils/amplitude";

import useLoggedUser from "somewhere";

const useTrackLoggedUser = () => {
  const loggedUser = useLoggedUser();

  const trackUser = useTrackUser();

  useEffect(() => {
    if (loggedUser) {
      trackUser(loggedUser);
    }
  }, [loggedUser]);
};
```