# [@unlockre/utils-split-io-v4.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-split-io-v3.0.0...@unlockre/utils-split-io-v4.0.0) (2025-05-08)


### Features

* Add useLogoutIfNeeded hook ([#235](https://github.com/unlockre/utils-packages/issues/235)) ([671a70e](https://github.com/unlockre/utils-packages/commit/671a70e37d6a52b9b96e3dd835875238b7bfc988))


### BREAKING CHANGES

* Move Auth0AccessTokenClaims type to a dedicated module

# [@unlockre/utils-split-io-v3.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-split-io-v2.1.0...@unlockre/utils-split-io-v3.0.0) (2025-03-31)


### Bug Fixes

* Update react dependencies ([#232](https://github.com/unlockre/utils-packages/issues/232)) ([f98c37c](https://github.com/unlockre/utils-packages/commit/f98c37c119e6b5aee33e3860a51e30beb2c48460))


### BREAKING CHANGES

* Update react dependencies

# [@unlockre/utils-split-io-v2.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-split-io-v2.0.0...@unlockre/utils-split-io-v2.1.0) (2025-02-20)


### Features

* Add userback splitio whitelisting support ([#216](https://github.com/unlockre/utils-packages/issues/216)) ([4324c0e](https://github.com/unlockre/utils-packages/commit/4324c0e4a4c37a17fb1d21aa9e046178532750ad))

# [@unlockre/utils-split-io-v2.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-split-io-v1.1.1...@unlockre/utils-split-io-v2.0.0) (2023-11-14)


### Bug Fixes

* Fix split io attributes logic ([#96](https://github.com/unlockre/utils-packages/issues/96)) ([6ea084d](https://github.com/unlockre/utils-packages/commit/6ea084d70397126f76f0825cb16a431485137670))


### BREAKING CHANGES

* Rename useSetUserSplitIoAttributes hook and add SplitIoProvider

# [@unlockre/utils-split-io-v1.1.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-split-io-v1.1.0...@unlockre/utils-split-io-v1.1.1) (2023-10-12)


### Bug Fixes

* Add missing exports ([#85](https://github.com/unlockre/utils-packages/issues/85)) ([d522409](https://github.com/unlockre/utils-packages/commit/d52240945c19d2cafd4d49d8b1852024987a54cc))

# [@unlockre/utils-split-io-v1.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-split-io-v1.0.2...@unlockre/utils-split-io-v1.1.0) (2023-10-12)


### Features

* Add custom attributes support  ([#83](https://github.com/unlockre/utils-packages/issues/83)) ([29be8d9](https://github.com/unlockre/utils-packages/commit/29be8d9b0c51be0f4b6b3b3e5eac8fa412200691))

# [@unlockre/utils-split-io-v1.0.2](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-split-io-v1.0.1...@unlockre/utils-split-io-v1.0.2) (2023-01-19)


### Bug Fixes

* Update utils-split-io README with latest API ([#27](https://github.com/unlockre/utils-packages/issues/27)) ([9836b68](https://github.com/unlockre/utils-packages/commit/9836b684db2af18da04b708fbbd9d9be8cb0502c))

# [@unlockre/utils-split-io-v1.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-split-io-v1.0.0...@unlockre/utils-split-io-v1.0.1) (2023-01-06)


### Bug Fixes

* **split.io:** Remove test code ([#25](https://github.com/unlockre/utils-packages/issues/25)) ([d98c750](https://github.com/unlockre/utils-packages/commit/d98c75047c10865e705ed3711b0b0932b15fc70f))

# @unlockre/utils-split-io-v1.0.0 (2023-01-06)


### Features

* Add utils-split-io package ([#24](https://github.com/unlockre/utils-packages/issues/24)) ([accee57](https://github.com/unlockre/utils-packages/commit/accee574d0555d96281ec67298f24b07dee0f860))
