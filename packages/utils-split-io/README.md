# utils-split-io

## Install

```
yarn add @unlockre/utils-split-io
```

### Peer dependencies

- **@splitsoftware/splitio**: `^10.23.1`
- **@splitsoftware/splitio-react**: `^1.9.0`
- **react**: `^17.0.0`

## Usage

**Create the feature flag name enum**

> utils/split-io/feature-flag-name.ts

```ts
const featureFlagNames = {
  feature1: "feature1",
  feature2: "feature2",
} as const;

type FeatureFlagName = typeof featureFlagName[keyof typeof featureFlagNames];

export {featureFlagNames};

export type {FeatureFlagName};
```

**Create the typed hook to get the user treatments**

> utils/split-io/use-user-split-io-treatments.ts

```ts
import {bindUseUserSplitIoTreatments} from '@unlockre/utils-split-io/dist';

import type {FeatureFlagName} from './feature-flag-name';

const useUserSplitIoTreatments =
  bindUseUserSplitIoTreatments<FeatureFlagName>();
```

**Initialize split.io factory**

> utils/split-io/split-io-factory.ts

```ts
import * as withSplitIoFactory from "@unlockre/utils-split-io/dist/split-io-factory";

import environmentVariables from "@/environment/environment-variables";

const splitIoFactory = withSplitIoFactory.create(
  environmentVariables.SPLIT_IO_BROWSER_API_KEY
);

export default splitIoFactory;
```

**Wrap your application with the SplitIoProvider and pass the factory instance**

```tsx
import {SplitIoProvider} from "@unlockre/utils-split-io/dist";

import splitIoFactory from "@/utils/split-io/split-io-factory";

const App = () => (
  <SplitIoProvider factory={splitIoFactory}>
    {...}
  </SplitIoProvider>
);
```

**Call useInitUserSplitIoAttributes inside your Page component**

```tsx
import {useInitUserSplitIoAttributes} from "@unlockre/utils-split-io/dist";

const Page = () => (
  useInitUserSplitIoAttributes();

  /* ... */
);
```

**Use the hook to query the treatments of a group of feature flags**

```tsx
import {useUserSplitIoTreatments} from "@/utils/split-io";

const MyComponent = () => {
  const userSplitIoTreatments = useUserSplitIoTreatments(['feature1']);

  return userSplitIoTreatments.feature1
    ? <div>Feature 1</div>
    : null;
};
```
