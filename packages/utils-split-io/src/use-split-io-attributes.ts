import type {
  Attributes,
  IBrowserClient
} from "@splitsoftware/splitio/types/splitio";

import * as withSplitIoClient from "./split-io-client";
import {useSplitIoPrivateContext} from "./split-io-private-context";

const useSplitIoAttributes = () => {
  const splitIoPrivateContext = useSplitIoPrivateContext();

  // We need to do this because split.io mutates client attributes when the ones
  // given, so the reference remains the same and therefore, it can't be used
  // as a useEffect dependency
  const setSplitIoAttributes = (
    splitIoClient: IBrowserClient,
    attributes: Attributes
  ) => {
    withSplitIoClient.setAttributes(splitIoClient, attributes);

    splitIoPrivateContext.setState(state => ({...state, attributes}));
  };

  const splitIoAttributes = splitIoPrivateContext.state.attributes;

  return {setSplitIoAttributes, splitIoAttributes};
};

export {useSplitIoAttributes};
