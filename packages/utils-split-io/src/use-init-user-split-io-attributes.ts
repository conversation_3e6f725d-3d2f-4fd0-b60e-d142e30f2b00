import {
  useAuth0AccessTokenClaims,
  useAuth0IdToken
} from "@unlockre/utils-auth0/dist";
import type {Auth0AccessTokenClaims} from "@unlockre/utils-auth0/dist/auth0-access-token-claims";
import {useEffect} from "react";

import {useSplitIoAttributes} from "./use-split-io-attributes";
import {useUserSplitIoClient} from "./use-user-split-io-client";

type Auth0IdToken = {email: string};

const getAttributesFrom = (
  {organizationId}: Auth0AccessTokenClaims,
  {email}: Auth0IdToken
) => ({
  organizationId,
  email
});

const useInitUserSplitIoAttributes = () => {
  const userSplitIoClient = useUserSplitIoClient();

  const accessTokenClaims = useAuth0AccessTokenClaims();
  const auth0Token = useAuth0IdToken();

  const {setSplitIoAttributes} = useSplitIoAttributes();

  useEffect(() => {
    if (
      !userSplitIoClient ||
      accessTokenClaims.status !== "succeeded" ||
      auth0Token.status !== "succeeded"
    ) {
      return;
    }

    const attributes = getAttributesFrom(
      accessTokenClaims.data,
      auth0Token.data as Auth0IdToken
    );

    setSplitIoAttributes(userSplitIoClient, attributes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accessTokenClaims.status, auth0Token.status]);
};

export {useInitUserSplitIoAttributes};
