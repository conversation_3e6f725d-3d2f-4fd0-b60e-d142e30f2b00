import type {
  Attributes,
  IBrowserClient
} from "@splitsoftware/splitio/types/splitio";

type SplitIoClientEventNameRecord = IBrowserClient["Event"];

type SplitIoClientEventName =
  SplitIoClientEventNameRecord[keyof SplitIoClientEventNameRecord];

type SplitIoClientEventListener = () => unknown;

const subscribe = (
  splitIoClient: IBrowserClient,
  eventName: SplitIoClientEventName,
  listener: SplitIoClientEventListener
) => {
  splitIoClient.addListener(eventName, listener);

  return () => {
    splitIoClient.removeListener(eventName, listener);
  };
};

const setAttributes = (
  splitIoClient: IBrowserClient,
  attributes: Attributes
) => {
  const couldClearAttributes = splitIoClient.clearAttributes();

  if (!couldClearAttributes) {
    throw new Error("Could not clear attributes");
  }

  const couldSetAttributes = splitIoClient.setAttributes(attributes);

  if (!couldSetAttributes) {
    throw new Error("Could not set attributes");
  }
};

export {setAttributes, subscribe};
