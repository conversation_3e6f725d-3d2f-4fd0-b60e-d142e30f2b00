import {bindUseSplitIoTreatments} from "./bind-use-split-io-treatments";
import {useUserSplitIoAttributes} from "./use-user-split-io-attributes";
import {useUserSplitIoClient} from "./use-user-split-io-client";

const useShouldFetchUserSplitIoTreatments = () => {
  const {userSplitIoAttributes} = useUserSplitIoAttributes();

  return userSplitIoAttributes !== undefined;
};

const bindUseUserSplitIoTreatments = <TAnyFeatureFlagName extends string>() =>
  bindUseSplitIoTreatments<TAnyFeatureFlagName>(
    useUserSplitIoClient,
    useShouldFetchUserSplitIoTreatments
  );

export {bindUseUserSplitIoTreatments};
