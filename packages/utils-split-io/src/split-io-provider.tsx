import {SplitFactory} from "@splitsoftware/splitio-react";
import type {ComponentProps, ReactNode} from "react";

import {SplitIoPrivateContextProvider} from "./split-io-private-context";

type SplitFactoryProps = ComponentProps<typeof SplitFactory>;

type ExposedSplitFactoryProps = Pick<SplitFactoryProps, "factory">;

type Props = ExposedSplitFactoryProps & {
  children: ReactNode;
};

const SplitIoProvider = ({children, factory}: Props) => (
  <SplitFactory {...{factory}}>
    <SplitIoPrivateContextProvider>{children}</SplitIoPrivateContextProvider>
  </SplitFactory>
);

export {SplitIoProvider};
