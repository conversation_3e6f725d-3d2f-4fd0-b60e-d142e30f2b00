import type {Attributes} from "@splitsoftware/splitio/types/splitio";
import type {ReactSetStateFrom} from "@unlockre/utils-react/dist";
import {createContext, useContext, useMemo, useState} from "react";
import type {ReactNode} from "react";

type SplitIoPrivateContextProviderProps = {
  children: ReactNode;
};

type SplitIoPrivateContextState = {
  attributes?: Attributes;
};

type SetSplitIoPrivateContextState =
  ReactSetStateFrom<SplitIoPrivateContextState>;

type SplitIoPrivateContextValue = {
  setState: SetSplitIoPrivateContextState;
  state: SplitIoPrivateContextState;
};

// eslint-disable-next-line @typescript-eslint/naming-convention
const SplitIoPrivateContext = createContext<
  SplitIoPrivateContextValue | undefined
>(undefined);

const SplitIoPrivateContextProvider = ({
  children
}: SplitIoPrivateContextProviderProps) => {
  const [state, setState] = useState<SplitIoPrivateContextState>({
    attributes: undefined
  });

  const splitIoPrivateContextValue = useMemo(
    () => ({state, setState}),
    [state]
  );

  return (
    <SplitIoPrivateContext.Provider value={splitIoPrivateContextValue}>
      {children}
    </SplitIoPrivateContext.Provider>
  );
};

const useSplitIoPrivateContext = () => {
  const splitIoPrivateContextValue = useContext(SplitIoPrivateContext);

  if (!splitIoPrivateContextValue) {
    throw new Error("Not initialized");
  }

  return splitIoPrivateContextValue;
};

export {SplitIoPrivateContextProvider, useSplitIoPrivateContext};
