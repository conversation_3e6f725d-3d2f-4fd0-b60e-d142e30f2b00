import {useSplitIoPrivateContext} from "./split-io-private-context";
import {userSplitIoAttributesSchema} from "./user-split-io-attributes";

const useUserSplitIoAttributes = () => {
  const splitIoPrivateContext = useSplitIoPrivateContext();

  const splitIoAttributes = splitIoPrivateContext.state.attributes;

  const userSplitIoAttributes =
    splitIoAttributes && userSplitIoAttributesSchema.parse(splitIoAttributes);

  return {userSplitIoAttributes};
};

export {useUserSplitIoAttributes};
