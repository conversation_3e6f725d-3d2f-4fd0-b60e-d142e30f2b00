import type {
  IBrowserSettings,
  MockedFeaturesMap
} from "@splitsoftware/splitio/types/splitio";
import {SplitSdk} from "@splitsoftware/splitio-react";

const getSettings = (
  authorizationKey: string,
  mockedTreatments?: MockedFeaturesMap
): IBrowserSettings => ({
  core: {
    authorizationKey,
    key: "anonymous"
  },
  features: mockedTreatments
});

const create = (
  authorizationKey: string,
  mockedTreatments?: MockedFeaturesMap
) => SplitSdk(getSettings(authorizationKey, mockedTreatments));

export {create};
