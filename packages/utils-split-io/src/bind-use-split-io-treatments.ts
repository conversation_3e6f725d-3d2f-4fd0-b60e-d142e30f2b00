import type {
  IBrowserClient,
  TreatmentWithConfig
} from "@splitsoftware/splitio/types/splitio";
import {useCallback, useEffect, useState} from "react";

import * as withSplitIoClient from "./split-io-client";
import {useSplitIoPrivateContext} from "./split-io-private-context";

type UseShouldFetchTreatments = () => boolean;

type UseSplitIoClient = () => IBrowserClient | null;

type SplitIoTreatments<TFeatureFlagName extends string> = Record<
  TFeatureFlagName,
  TreatmentWithConfig
>;

const defaultUseShouldFetchTreatments = () => true;

const bindUseSplitIoTreatments =
  <TAnyFeatureFlagName extends string>(
    useSplitIoClient: UseSplitIoClient,
    useShouldFetchTreatments: UseShouldFetchTreatments = defaultUseShouldFetchTreatments
  ) =>
  <TFeatureFlagName extends TAnyFeatureFlagName>(
    featureFlagNames: readonly TFeatureFlagName[]
  ) => {
    const [treatments, setTreatments] = useState<
      SplitIoTreatments<TFeatureFlagName> | undefined
    >(undefined);

    const splitIoPrivateContext = useSplitIoPrivateContext();

    const splitIoClient = useSplitIoClient();

    const refreshTreatments = useCallback(() => {
      // We need to create another array to make TS happy (readonly)
      setTreatments(
        splitIoClient?.getTreatmentsWithConfig([
          ...featureFlagNames
        ]) as SplitIoTreatments<TFeatureFlagName>
      );
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [splitIoClient, featureFlagNames]);

    const {attributes} = splitIoPrivateContext.state;

    const shouldFetchTreatments = useShouldFetchTreatments();

    useEffect(() => {
      if (shouldFetchTreatments) {
        splitIoClient?.ready().then(refreshTreatments);
      }
    }, [attributes, shouldFetchTreatments, splitIoClient, refreshTreatments]);

    useEffect(() => {
      if (!splitIoClient) {
        return;
      }

      return withSplitIoClient.subscribe(
        splitIoClient,
        splitIoClient.Event.SDK_UPDATE,
        refreshTreatments
      );
    }, [splitIoClient, refreshTreatments]);

    return treatments;
  };

export {bindUseSplitIoTreatments};
