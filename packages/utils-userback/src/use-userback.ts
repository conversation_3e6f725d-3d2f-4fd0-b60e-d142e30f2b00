import {useAuth0} from "@auth0/auth0-react";
import Userback from "@userback/widget";
import {useEffect} from "react";

import {useSplitIoTreatments} from "./use-split-io-treatments";
import {allUserbackFeatureFlagNames} from "./userback-feature-flag-name";

const useUserback = (userbackAccessToken?: string) => {
  const {user: auth0User} = useAuth0();

  const treatments = useSplitIoTreatments(allUserbackFeatureFlagNames);

  useEffect(() => {
    const isUserAllowed = treatments?.userbackBlacklist.treatment === "off";

    if (userbackAccessToken && auth0User && isUserAllowed) {
      // We could identify the user more precisely, but we're using this approach
      // to maintain backward compatibility with the original implementation
      Userback(userbackAccessToken, {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        user_data: {
          id: auth0User.nickname,
          info: {
            email: auth0User.email
          }
        }
      });
    }
  }, [auth0User, treatments, userbackAccessToken]);
};
export {useUserback};
