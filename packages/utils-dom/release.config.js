module.exports = {
  branches: ["main"],
  extends: "semantic-release-monorepo",
  plugins: [
    "@semantic-release/commit-analyzer",
    "@semantic-release/release-notes-generator",
    "@semantic-release/changelog",
    "semantic-release-yarn",
    [
      "@semantic-release/git",
      {
        assets: ["package.json", "CHANGELOG.md"],
        message:
          "chore(utils-dom): Create release ${nextRelease.version} [skip ci]"
      }
    ],
    "@semantic-release/github",
    [
      "semantic-release-slack-bot",
      {
        markdownReleaseNotes: true,
        notifyOnSuccess: true,
        notifyOnFail: true,
        slackWebhookEnVar: "RELEASES_SLACK_WEBHOOK"
      }
    ]
  ]
};
