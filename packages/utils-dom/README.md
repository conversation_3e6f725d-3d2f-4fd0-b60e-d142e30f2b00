# utils-dom

## Install

```
yarn add @unlockre/utils-dom
```

## Utilities

### createCsvBlob

#### Description

This function receives a string and returns a csv blob.

#### Example

```typescript
import {createCsvBlob} from "@unlockre/utils-dom/dist";

const csvBlob = createCsvBlob("some data")
```

### createZipBlob

#### Description

This function receives an array of filename and blob, return a zip blob.

#### Example

```typescript
import {createZipBlob} from "@unlockre/utils-dom/dist";
import {createCsvBlob} from "@unlockre/utils-dom/dist";

createZipBlob({
	files: [
		{
			filename: "myfile.csv", 
			blob: createCsvBlob("some data")
		}, 
		{
			filename: "myotherfile.txt",
			blob: new Blob([str], {type: "text/plain"});
		}
	]
});
```

### downloadBlob

#### Description

This function receives a blob and forces a file download.

#### Example

```typescript
import {downloadBlob} from "@unlockre/utils-dom/dist";

downloadBlob({name: "My file", blob: new Blob(["some data"], {type: "text/plain"})});
```

### downloadFile

#### Description

This function creates an anchor element to force a file download.

#### Example

```typescript
import {downloadFile} from "@unlockre/utils-dom/dist";

downloadFile({name: "My file", url: "https://my-domain.com/my-file"});
```

### loadScript

#### Description

This function loads a script creating an element and inserting it to the DOM, returning a promise that resolves once this is done.

#### Example

```typescript
import {loadScript} from "@unlockre/utils-dom/dist";

loadScript({
	async: true, // optional (this is the default)
	src: "https://my-domain.com/my-script.js",
	type: "text/javascript" // optional (this is the default)
});
```