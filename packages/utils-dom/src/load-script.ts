type Params = {
  async?: boolean;
  src: string;
  type?: string;
};

const loadScript = ({async = true, src, type = "text/javascript"}: Params) =>
  new Promise<HTMLScriptElement>((resolve, reject) => {
    const scriptEl = document.createElement("script");

    Object.assign(scriptEl, {type, async, src});

    scriptEl.addEventListener("load", () => {
      resolve(scriptEl);
    });

    scriptEl.addEventListener("error", () => {
      reject(new Error(`Failed to load the script ${src}`));
    });

    const container = document.head || document.body;

    container.appendChild(scriptEl);
  });

export default loadScript;
