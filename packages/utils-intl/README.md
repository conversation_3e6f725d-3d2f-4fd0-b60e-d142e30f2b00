# utils-intl

> DEPRECATED! Use [@unlockre/utils-formatting](https://github.com/unlockre/utils-packages/packages/utils-formatting)

## API

### formatCurrency

#### Description

Function to format a number representing a currency value. Accepts currency and minimum decimal digits

#### Example

```typescript
import {formatCurrency} from "@unlockre/utils-intl/dist";

const usdValue = 1000000.2;

const formattedCurrency = formatCurrency(usdValue, {minimumFractionDigits: 2});

console.log(formattedCurrency); // $1,000,000.20
```

#### Options

- currency?: Currency ID, defaults to "USD"
- minimumFractionDigits?: minimum amount of decimal digits to be displayed, defaults to 0
- locale?: Locale region value, defaults to "en-us"