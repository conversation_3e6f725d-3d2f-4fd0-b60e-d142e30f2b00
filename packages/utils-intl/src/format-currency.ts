type ExposedNumberFormatOptions = Pick<
  Intl.NumberFormatOptions,
  "currency" | "minimumFractionDigits"
>;
type Options = ExposedNumberFormatOptions & {
  locale?: string;
};

const getNUmberFormatOptions = ({
  currency = "USD",
  minimumFractionDigits = 0
}: Options) => ({
  currency,
  minimumFractionDigits,
  style: "currency"
});

const formatCurrency = (amount: number, {locale, ...options}: Options = {}) =>
  new Intl.NumberFormat(
    locale ?? "en-us",
    getNUmberFormatOptions(options)
  ).format(amount);

export default formatCurrency;
