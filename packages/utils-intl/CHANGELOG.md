# [@unlockre/utils-intl-v2.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-intl-v2.1.0...@unlockre/utils-intl-v2.2.0) (2022-09-22)


### Features

* Add utils-formatting package ([#22](https://github.com/unlockre/utils-packages/issues/22)) ([893075c](https://github.com/unlockre/utils-packages/commit/893075c3e479a727e9d3adf96c68b48b37de503b))

# [@unlockre/utils-intl-v2.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-intl-v2.0.0...@unlockre/utils-intl-v2.1.0) (2022-07-28)


### Features

* Add utils-dom package ([#18](https://github.com/unlockre/utils-packages/issues/18)) ([46543cb](https://github.com/unlockre/utils-packages/commit/46543cbb83d6dfffe9765aab53b52ba9845c32e1))

# [@unlockre/utils-intl-v2.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-intl-v1.1.0...@unlockre/utils-intl-v2.0.0) (2022-07-15)


### Features

* Add minimum digits on currency formatter ([#12](https://github.com/unlockre/utils-packages/issues/12)) ([#13](https://github.com/unlockre/utils-packages/issues/13)) ([a3fc1a4](https://github.com/unlockre/utils-packages/commit/a3fc1a4b4af66864c8f8de4029502a1f2cb4a819))


### BREAKING CHANGES

* formatCurrency now supports an options object for formatting

# [@unlockre/utils-intl-v1.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-intl-v1.0.1...@unlockre/utils-intl-v1.1.0) (2022-05-17)


### Features

* Add utils-react package ([#7](https://github.com/unlockre/utils-packages/issues/7)) ([b3357b6](https://github.com/unlockre/utils-packages/commit/b3357b648f22ad39706e23fcb98bfeaa25b1a49e))

# [@unlockre/utils-intl-v1.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-intl-v1.0.0...@unlockre/utils-intl-v1.0.1) (2022-03-29)


### Bug Fixes

* Do not show 00 when no decimals ([#4](https://github.com/unlockre/utils-packages/issues/4)) ([3b685e1](https://github.com/unlockre/utils-packages/commit/3b685e1f80ac114001705e9c1fb6f8d877b668f9))

# @unlockre/utils-intl-v1.0.0 (2022-03-29)


### Features

* Add @unlockre/utils-intl package ([#3](https://github.com/unlockre/utils-packages/issues/3)) ([3aa4eda](https://github.com/unlockre/utils-packages/commit/3aa4eda9fb669f033c686af010a8af7b33a45ccf))
