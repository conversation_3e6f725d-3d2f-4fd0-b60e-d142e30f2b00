{"compilerOptions": {"allowJs": true, "baseUrl": "./", "declaration": true, "esModuleInterop": true, "importHelpers": true, "inlineSourceMap": true, "inlineSources": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["dom", "es2020"], "moduleResolution": "node", "noEmitOnError": true, "outDir": "./dist", "paths": {"@/*": ["./src/*"]}, "resolveJsonModule": true, "rootDir": "./src", "skipLibCheck": true, "strict": true, "target": "es5"}, "exclude": ["dist", "node_modules"], "include": ["src", "@types"]}