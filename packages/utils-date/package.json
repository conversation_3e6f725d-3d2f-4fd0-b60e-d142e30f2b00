{"name": "@unlockre/utils-date", "version": "1.3.0", "repository": "https://github.com/unlockre/utils-packages", "scripts": {"build": "yarn build:clean && yarn build:code", "build:clean": "rm -rf dist", "build:code": "tsc && tsconfig-replace-paths", "lint:js": "eslint", "lint:ts": "eslint --config ./.eslintrc.tsc.js", "semantic-release": "semantic-release"}, "publishConfig": {"registry": "https://npm.pkg.github.com/", "access": "restricted"}, "files": ["dist", "CHANGELOG.md"], "devDependencies": {"@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@typescript-eslint/parser": "^6.2.1", "@unlockre/eslint-config": "^2.1.2", "eslint": "^8.46.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-tsc": "^2.0.0", "prettier": "^3.0.0", "semantic-release": "^24.2.1", "semantic-release-monorepo": "^8.0.2", "semantic-release-slack-bot": "^4.0.2", "semantic-release-yarn": "^3.0.2", "tsconfig-replace-paths": "^0.0.14", "typescript": "^5.1.6"}, "dependencies": {"@unlockre/utils-number": "workspace:^", "tslib": "^2.6.1"}}