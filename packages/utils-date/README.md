# utils-date

## Install

```
yarn add @unlockre/utils-date
```

## API

### clone

#### Description

Function that returns a new date from an existing one:

#### Example

```ts
import * as withDate from "@unlockre/utils-date/dist";

const date = new Date("2024-01-01");

const clonedDate = withDate.clone(date);

clonedDate.setFullYear(2025);

console.log(date.getFullYear()); // 2024

console.log(clonedDate.getFullYear()); // 2025
```

### compareAsc

#### Description

Function that compares two dates and returns the following:
- zero if the dates are equal.
- -1 if the first date comes after the second one.
- 1 if the first date comes before the second one.

#### Example

```ts
import {compareAsc} from "@unlockre/utils-date/dist";

const firstDate = new Date("2024-11-11");
const secondDate = new Date("2023-10-10");

console.log(compareAsc(firstDate, firstDate)); // 0
console.log(compareAsc(firstDate, secondDate)); // -1
console.log(compareAsc(secondDate, firstDate)); // 1
```

### compareDesc

#### Description

Function that compares two dates and returns the following:
- zero if the dates are equal.
- -1 if the first date comes before the second one.
- 1 if the first date comes after the second one.

#### Example

```ts
import {compareDesc} from "@unlockre/utils-date/dist";

const firstDate = new Date("2024-11-11");
const secondDate = new Date("2023-10-10");

console.log(compareDesc(firstDate, firstDate)); // 0
console.log(compareDesc(firstDate, secondDate)); // 1
console.log(compareDesc(secondDate, firstDate)); // -1
```

### fromIso8601Date

#### Description

Returns a date with timezone given a iso8601date string.

#### Example

```ts
import {fromIso8601Date} from "@unlockre/utils-date/dist";

const iso8601Date = "2023-10-10";

const date = fromIso8601Date(iso8601Date);

console.log(date);

// Tue Oct 10 2023 00:00:00 GMT-0300 (GMT-03:00)
```

### getDaysDiff

#### Description

Returns the days difference between two given dates

#### Example

```ts
import {getDaysDiff} from "@unlockre/utils-date/dist";

const date1 = new Date("2023-10-10T00:00Z");

const date2 = new Date("2023-10-01T00:00Z");

const daysDiff = getDaysDiff(date1, date2);

console.log(daysDiff); // 9
```

### toIso8601Date

#### Description

Returns an iso8601date given a Date.

#### Example

```ts
import {toIso8601Date} from "@unlockre/utils-date/dist";

const date = new Date("2023-10-10")

const iso8601Date = toIso8601Date(date)

console.log(iso8601Date);

// "2023-10-10"
```

### update

#### Description

Updates a copy of the given date using the updater.

#### Example

```ts
import * as withDate from "@unlockre/utils-date/dist";

const date = new Date("2024-01-01")

const updatedDate = withDate.update(date, date => date.setFullYear(2025));

console.log(date.getFullYear()); // 2024

console.log(updatedDate.getFullYear()); // 2025
```