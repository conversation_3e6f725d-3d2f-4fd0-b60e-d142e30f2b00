# [@unlockre/utils-formatting-v5.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v4.0.0...@unlockre/utils-formatting-v5.0.0) (2025-07-15)


### Features

* Add formatRatio utility ([#250](https://github.com/unlockre/utils-packages/issues/250)) ([d9aef59](https://github.com/unlockre/utils-packages/commit/d9aef597bf971bff30363c2dbbf74d071179cbe2))


### BREAKING CHANGES

* Use named exports for all the utilities

# [@unlockre/utils-formatting-v4.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v3.4.0...@unlockre/utils-formatting-v4.0.0) (2024-07-11)


### Features

* Simplify formatOptionalValue ([#154](https://github.com/unlockre/utils-packages/issues/154)) ([5a9fd26](https://github.com/unlockre/utils-packages/commit/5a9fd267fe34827c0ee3c8fa0667e44cc658fca5))


### BREAKING CHANGES

* Remove 2nd param (defaultValue) from formatOptionalValue function

# [@unlockre/utils-formatting-v3.4.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v3.3.0...@unlockre/utils-formatting-v3.4.0) (2024-06-24)


### Features

* Add formatRelativeTime utility ([#142](https://github.com/unlockre/utils-packages/issues/142)) ([08455d2](https://github.com/unlockre/utils-packages/commit/08455d241df66b006f4b72ab1f5385aea0687837))

# [@unlockre/utils-formatting-v3.3.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v3.2.0...@unlockre/utils-formatting-v3.3.0) (2024-06-06)


### Features

* Replace format accounting integer with parameter on format price ([#138](https://github.com/unlockre/utils-packages/issues/138)) ([a27020b](https://github.com/unlockre/utils-packages/commit/a27020bff58a64f9459e6523242605554c37e558))

# [@unlockre/utils-formatting-v3.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v3.1.0...@unlockre/utils-formatting-v3.2.0) (2024-06-06)


### Features

* Add capitalize and accounting price formatters ([#135](https://github.com/unlockre/utils-packages/issues/135)) ([90480c7](https://github.com/unlockre/utils-packages/commit/90480c780ab276830a17c7d5e8b6633378bdc1ee))

# [@unlockre/utils-formatting-v3.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v3.0.1...@unlockre/utils-formatting-v3.1.0) (2024-04-29)


### Features

* Set showSign default to "negative" ([#133](https://github.com/unlockre/utils-packages/issues/133)) ([7fc196f](https://github.com/unlockre/utils-packages/commit/7fc196ffb1ce502d1d3cfe526c2a8b412e00545c))

# [@unlockre/utils-formatting-v3.0.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v3.0.0...@unlockre/utils-formatting-v3.0.1) (2024-04-29)


### Bug Fixes

* Export formatNumber ([#131](https://github.com/unlockre/utils-packages/issues/131)) ([73b520e](https://github.com/unlockre/utils-packages/commit/73b520edc3dd2c913a69c9dac02a452f6730c306))

# [@unlockre/utils-formatting-v3.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v2.0.0...@unlockre/utils-formatting-v3.0.0) (2024-04-29)


### Features

* Add formatNumber utility ([#129](https://github.com/unlockre/utils-packages/issues/129)) ([9afc720](https://github.com/unlockre/utils-packages/commit/9afc72014857502543340f809f8cc53fe430997a))


### BREAKING CHANGES

* formatPercentage no longer accepts signDisplay option, now it expects the new showSign option

# [@unlockre/utils-formatting-v2.0.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v1.5.1...@unlockre/utils-formatting-v2.0.0) (2024-04-10)


### Features

* Add maximumFractionDigits prop to formatPercentage ([#122](https://github.com/unlockre/utils-packages/issues/122)) ([5db479a](https://github.com/unlockre/utils-packages/commit/5db479a0807b2a36a1eed894a5c95ff6521a7cab))


### BREAKING CHANGES

* Remove maximumFractionDigits default

# [@unlockre/utils-formatting-v1.5.1](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v1.5.0...@unlockre/utils-formatting-v1.5.1) (2024-02-28)


### Bug Fixes

* Correct format ordinal options ([#109](https://github.com/unlockre/utils-packages/issues/109)) ([3c80f66](https://github.com/unlockre/utils-packages/commit/3c80f660ef043fd7ecee923d6d63daafce9255f7))

# [@unlockre/utils-formatting-v1.5.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v1.4.0...@unlockre/utils-formatting-v1.5.0) (2024-02-28)


### Features

* Add format ordinal number util ([#108](https://github.com/unlockre/utils-packages/issues/108)) ([eaa871a](https://github.com/unlockre/utils-packages/commit/eaa871a5af0943d565913ccb33cc602d5c621539))

# [@unlockre/utils-formatting-v1.4.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v1.3.0...@unlockre/utils-formatting-v1.4.0) (2023-11-06)


### Features

* Allow sign display on formatPercentage ([#95](https://github.com/unlockre/utils-packages/issues/95)) ([e1273d9](https://github.com/unlockre/utils-packages/commit/e1273d9bdc9a61796a4e6acf1b7b1bca0148aeae))

# [@unlockre/utils-formatting-v1.3.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v1.2.0...@unlockre/utils-formatting-v1.3.0) (2023-11-01)


### Features

* Allow notation on format currency ([#94](https://github.com/unlockre/utils-packages/issues/94)) ([0b83c04](https://github.com/unlockre/utils-packages/commit/0b83c0446cdf84c2c7f9cafa35f92963ea542925))

# [@unlockre/utils-formatting-v1.2.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v1.1.0...@unlockre/utils-formatting-v1.2.0) (2023-06-28)


### Features

* Allow maximum fraction digits ([#46](https://github.com/unlockre/utils-packages/issues/46)) ([322b0b7](https://github.com/unlockre/utils-packages/commit/322b0b7b4a458e434273c1b567185063b05ce76d))

# [@unlockre/utils-formatting-v1.1.0](https://github.com/unlockre/utils-packages/compare/@unlockre/utils-formatting-v1.0.0...@unlockre/utils-formatting-v1.1.0) (2023-03-30)


### Features

* Add formatting utilities ([#29](https://github.com/unlockre/utils-packages/issues/29)) ([6c09089](https://github.com/unlockre/utils-packages/commit/6c090898c0345403e722d0118def3936a231e66e))

# @unlockre/utils-formatting-v1.0.0 (2022-09-22)


### Features

* Add utils-formatting package ([#22](https://github.com/unlockre/utils-packages/issues/22)) ([893075c](https://github.com/unlockre/utils-packages/commit/893075c3e479a727e9d3adf96c68b48b37de503b))
