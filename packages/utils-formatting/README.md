# utils-formatting


- [Installation](#installation)
- [Types](#types)
- [Utilities](#utilities)
  - [formatCurrency](#formatCurrency)
  - [formatDate](#formatDate)
  - [formatList](#formatList)
  - [formatNumber](#formatNumber)
  - [formatOptionalValue](#formatOptionalValue)
  - [formatOrdinalNumber](#formatOrdinalNumber)
  - [formatPercentage](#formatPercentage)
  - [formatRatio](#formatRatio)
  - [formatRelativeTime](#formatRelativeTime)

## Installation

```
yarn add @unlockre/utils-formatting
```

## Types

```ts
type FormatValue<TValue> = (value: TValue) => string;
```

## Utilities

### formatCurrency

#### Description

Function to format a currency value. Accepts currency and minimum decimal digits.

#### Example

```ts
import {formatCurrency} from "@unlockre/utils-formatting/dist";

const usdValue = 1000000.2;

const formattedCurrency = formatCurrency(usdValue, {minimumFractionDigits: 2});

console.log(formattedCurrency); // $1,000,000.20
```

#### Parameters

- **amount:** `number`
- **options?:** `Options` (see below)

#### Options (optionals)

- **currency?:** `string`: Currency ID (defaults to `"USD"`)
- **maximumFractionDigits?:** `number`: maximum amount of decimal digits to be displayed (defaults to `2`)
- **minimumFractionDigits?:** `number`: minimum amount of decimal digits to be displayed (defaults to `0`)
- **locale?:** `string`: Locale region value (defaults to `"en-US"`)
- **showAccounting?:** `boolean` Should use accounting notation (`($1000)`)
- **showCompact?:** `boolean`: Should use compact notation (`1000 => 1K`)

### formatDate

#### Description

Function to format a date. Accepts currency, maximum and minimum decimal digits.

#### Example

```ts
import {formatDate} from "@unlockre/utils-formatting/dist";

const date = new Date("2023-03-30T00:00");

const formattedDate = formatDate(date);

console.log(formattedDate); // 03/30/2023
```

#### Parameters

- **date:** `Date`: The date to format
- **options?:** `Options`: See below

#### Options (optionals)

- **locale?:** `string`: Locale region value (defaults to `"en-US"`)
- all the options specified [here](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#parameters)

### formatList

#### Description

Function to format a list of strings separating them with commas and adding connectors (or, and) to the last one depending on the type (`"conjunction"` or `"disjunction"`).

#### Example

```ts
import {formatList} from "@unlockre/utils-formatting/dist";

const formattedList = formatList(["one", "two", "three", "four"]);

console.log(formattedList); // one, two, three, and four
```

#### Parameters

- **items:** `string[]`: The items list to format
- **options?:** `Options`: See below

#### Options (optionals)

- **locale?:** `string`: Locale region value (defaults to `"en-US"`)
- **type?:** `"conjunction" | "disjunction"`: The formatting type (defaults to `"conjunction"`)

### formatNumber

#### Description

Function to format a number.

#### Example

```ts
import {formatNumber} from "@unlockre/utils-formatting/dist";

const formattedNumber = formatNumber(50.2, {minimumFractionDigits: 2});

console.log(formattedNumber); // 50.20
```

#### Parameters

- **value:** `number`
- **options?:** `Options` (see below)

#### Options (optionals)

- **locale?:** `string`: Locale region value (defaults to `"en-US"`)
- **maximumFractionDigits?:** `number`: maximum amount of decimal digits to be displayed
- **minimumFractionDigits?:** `number`: minimum amount of decimal digits to be displayed
- **showCompact?:** `boolean`: Should use compact notation (`1000 => 1K`)
- **showSign?:** `"all"` | `"negative"`: Should show sign in both, positive and negative numbers (`"all"`), or just negative ones (`"negative"`) (defaults to `"negative"`)

### formatOptionalValue

#### Description

Function to format an optional value returning an [em-dash](https://www.thesaurus.com/e/grammar/em-dash) in case it's `null` or `undefined` or the given value formatted by a `formatValue` function (optional, defaults to `String`) otherwise.

#### Example

```ts
import {formatCurrency, formatOptionalValue} from "@unlockre/utils-formatting/dist";

const maybeAmount: number | undefined = undefined;

const formattedCurrency = formatOptionalValue(maybeAmount, formatCurrency);

console.log(formattedCurrency); // "\u2014" (em-dash)
```

#### Parameters

- **optionalValue:** `string`: A value that could be `undefined`, `null` (optional), or something else
- **defaultValue:** `string`: The default value to be used in case the given value is `null` or `undefined`
- **formatValue?:** `FormatValue<NonNullable<TOptionalValue>>`: A function to format the `optionalValue` when it's not `null` or `undefined` (defaults to `String`)

### formatOrdinalNumber

#### Description
Appends an ordinal suffix to a given number

#### Example

```ts
import {formatOrdinalNumber} from "@unlockre/utils-formatting/dist";

const numberWithOrdinalSuffix = formatOrdinalNumber(3);

console.log(numberWithOrdinalSuffix) //3rd
```

#### Parameters

- **value:** `number`: the number the suffix will be appended to
- **options?:** `Options`: See below

#### Options (optionals)

- **locale?:** `string`: Locale region value (defaults to `"en-US"`)

### formatPercentage

#### Description

Function to format a percentage (a number between 0 and 100).

#### Example

```ts
import {formatPercentage} from "@unlockre/utils-formatting/dist";

const formattedPercentage = formatPercentage(50.2, {minimumFractionDigits: 2});

console.log(formattedPercentage); // 50.20%
```

#### Parameters

- **percentage:** `number`
- **options?:** `Options` (see below)

#### Options (optionals)

- **locale?:** `string`: Locale region value (defaults to `"en-US"`)
- **maximumFractionDigits?:** `number`: maximum amount of decimal digits to be displayed
- **minimumFractionDigits?:** `number`: minimum amount of decimal digits to be displayed
- **showSign?:** `"all"` | `"negative"`: Should show sign in both, positive and negative numbers (`"all"`), or just negative ones (`"negative"`) (defaults to `"negative"`)

### formatRatio

#### Description

Function to format a ratio (a number between 0 and 1).

#### Example

```ts
import {formatRatio} from "@unlockre/utils-formatting/dist";

const formattedRatio = formatRatio(0.502, {minimumFractionDigits: 2});

console.log(formattedRatio); // 50.20%
```

#### Parameters

- **ratio:** `number`
- **options?:** `Options` (see below)

#### Options (optionals)

- **locale?:** `string`: Locale region value (defaults to `"en-US"`)
- **maximumFractionDigits?:** `number`: maximum amount of decimal digits to be displayed
- **minimumFractionDigits?:** `number`: minimum amount of decimal digits to be displayed
- **showSign?:** `"all"` | `"negative"`: Should show sign in both, positive and negative numbers (`"all"`), or just negative ones (`"negative"`) (defaults to `"negative"`)

### formatRelativeTime

#### Description

Function to format a relative time. Accepts a time value and a unit (see below).

#### Example

```ts
import {formatRelativeTime} from "@unlockre/utils-formatting/dist";

const days = -1;

const formattedDays = formatPercentage(days, "days");

console.log(formattedDays); // "Yesterday"
```

#### Parameters

- **value:** `number` (number of days, months, etc)
- **unit:** All specified [here](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/RelativeTimeFormat/format#unit)
