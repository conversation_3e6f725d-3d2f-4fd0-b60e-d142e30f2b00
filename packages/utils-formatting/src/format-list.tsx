import defaultLocale from "./default-locale";

type FormatListType = Exclude<Intl.ListFormatType, "unit">;

// We are using the defaults of Intl.ListFormatOptions
// see: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/ListFormat/ListFormat#parameters
type Options = {
  locale?: string;
  type?: FormatListType;
};

const formatList = (
  items: string[],
  {locale = defaultLocale, ...rest}: Options = {}
) => new Intl.ListFormat(locale, rest).format(items);

export {formatList};
