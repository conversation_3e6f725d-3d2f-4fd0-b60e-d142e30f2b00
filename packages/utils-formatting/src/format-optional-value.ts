import type {FormatValue} from "./types";

const defaultValue = "\u2014"; // em-dash

const isDefined = <TValue>(value: TValue): value is NonNullable<TValue> =>
  value !== undefined && value !== null;

const formatOptionalValue = <TOptionalValue>(
  optionalValue: TOptionalValue,
  formatValue: FormatValue<NonNullable<TOptionalValue>> = String
) => (isDefined(optionalValue) ? formatValue(optionalValue) : defaultValue);

export {formatOptionalValue};
