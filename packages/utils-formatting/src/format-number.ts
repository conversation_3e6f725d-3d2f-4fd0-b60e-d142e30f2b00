import defaultLocale from "./default-locale";
import * as withShowSignOption from "./show-sign-option";
import type {ShowSignOption} from "./show-sign-option";

type ExposedNumberFormatOptions = Pick<
  Intl.NumberFormatOptions,
  "maximumFractionDigits" | "minimumFractionDigits"
>;

type Options = ExposedNumberFormatOptions & {
  locale?: string;
  showCompact?: boolean;
  showSign?: ShowSignOption;
};

const {showSignOptions} = withShowSignOption;

const getNumberFormatOptions = ({
  locale,
  showCompact,
  showSign = showSignOptions.negative,
  ...rest
}: Options) =>
  ({
    ...rest,
    notation: showCompact ? "compact" : undefined,
    signDisplay: withShowSignOption.toIntlSignDisplay(showSign)
  }) as const;

const formatNumber = (amount: number, options: Options = {}) =>
  new Intl.NumberFormat(
    options.locale ?? defaultLocale,
    getNumberFormatOptions(options)
  ).format(amount);

export {formatNumber};
