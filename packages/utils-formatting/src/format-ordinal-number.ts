import defaultLocale from "./default-locale";

type Options = {
  locale?: string;
};

const suffixes = {
  one: "st",
  two: "nd",
  few: "rd",
  many: "th",
  zero: "th",
  other: "th"
};

const formatOrdinalNumber = (
  value: number,
  {locale = defaultLocale}: Options = {}
) => {
  const pr = new Intl.PluralRules(locale, {type: "ordinal"});

  return value + suffixes[pr.select(value) as keyof typeof suffixes];
};

export {formatOrdinalNumber};
