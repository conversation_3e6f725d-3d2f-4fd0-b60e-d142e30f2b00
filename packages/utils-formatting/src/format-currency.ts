import defaultLocale from "./default-locale";

type ExposedNumberFormatOptions = Pick<
  Intl.NumberFormatOptions,
  "currency" | "maximumFractionDigits" | "minimumFractionDigits"
>;

type Options = ExposedNumberFormatOptions & {
  locale?: string;
  showAccounting?: boolean;
  showCompact?: boolean;
};

const getNumberFormatOptions = ({
  currency = "USD",
  maximumFractionDigits = 2,
  minimumFractionDigits = 0,
  showAccounting,
  showCompact
}: Options) =>
  ({
    currency,
    maximumFractionDigits,
    minimumFractionDigits,
    notation: showCompact ? "compact" : undefined,
    style: "currency",
    currencySign: showAccounting ? "accounting" : "standard"
  }) as const;

const formatCurrency = (amount: number, options: Options = {}) =>
  new Intl.NumberFormat(
    options.locale ?? defaultLocale,
    getNumberFormatOptions(options)
  ).format(amount);

export {formatCurrency};
