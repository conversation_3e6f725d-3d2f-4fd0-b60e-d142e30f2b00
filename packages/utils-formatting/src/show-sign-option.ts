type IntlSignDisplay = NonNullable<Intl.NumberFormatOptions["signDisplay"]>;

const showSignOptions = {
  all: "all",
  negative: "negative"
} as const;

type ShowSignOption = (typeof showSignOptions)[keyof typeof showSignOptions];

const intlSignDisplays = {
  [showSignOptions.all]: "except<PERSON><PERSON>",
  [showSignOptions.negative]: "negative"
};

// We need to cast to IntlSignDisplay because of:
// https://github.com/microsoft/TypeScript/issues/56269
const toIntlSignDisplay = (showSignOption: ShowSignOption) =>
  intlSignDisplays[showSignOption] as IntlSignDisplay;

export {showSignOptions, toIntlSignDisplay};
export type {ShowSignOption};
