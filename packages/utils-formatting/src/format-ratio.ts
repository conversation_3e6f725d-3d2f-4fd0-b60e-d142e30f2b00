import defaultLocale from "./default-locale";
import * as withShowSignOption from "./show-sign-option";
import type {ShowSignOption} from "./show-sign-option";

type ExposedNumberFormatOptions = Pick<
  Intl.NumberFormatOptions,
  "maximumFractionDigits" | "minimumFractionDigits"
>;

type Options = ExposedNumberFormatOptions & {
  locale?: string;
  showSign?: ShowSignOption;
};

const {showSignOptions} = withShowSignOption;

const getNumberFormatOptions = ({
  locale,
  showSign = showSignOptions.negative,
  ...rest
}: Options) => ({
  ...rest,
  signDisplay: withShowSignOption.toIntlSignDisplay(showSign),
  style: "percent"
});

const formatRatio = (ratio: number, options: Options = {}) =>
  new Intl.NumberFormat(
    options.locale ?? defaultLocale,
    getNumberFormatOptions(options)
  ).format(ratio);

export {formatRatio};
export type {Options as FormatRatioOptions};
